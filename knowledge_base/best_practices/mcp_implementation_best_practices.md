# Model Context Protocol (MCP) Implementation Best Practices (May 2025)

This document outlines best practices for implementing the Model Context Protocol (MCP) in VS Code extensions, based on the latest information as of May 2025.

## Overview

The Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM applications and external data sources and tools. It provides a standardized way for AI models to interact with context providers, making it easier to build AI-powered applications that can access and use external information.

## MCP Architecture

A typical MCP implementation consists of the following components:

1. **MCP Server**: Provides resources, tools, prompts, and agents to clients
2. **MCP Client**: Connects to the server and uses its capabilities
3. **Transport Layer**: Handles communication between client and server
4. **Resources**: Provide context to the model (e.g., file content, editor context)
5. **Tools**: Provide functionality to the model (e.g., code analysis, refactoring)
6. **Prompts**: Provide templates for generating text
7. **Agents**: Provide specialized AI capabilities

## MCP Server Implementation

### Basic Server Setup

```typescript
import { createServer, Server } from 'http';
import { MCPServer, Resource, Tool, Prompt, Agent } from '@anthropic/mcp-sdk';

class X10sionMCPServer {
  private httpServer: Server;
  private mcpServer: MCPServer;
  
  constructor(port: number = 3000) {
    this.httpServer = createServer();
    this.mcpServer = new MCPServer({
      server: this.httpServer,
      path: '/mcp'
    });
    
    this.httpServer.listen(port, () => {
      console.log(`MCP server listening on port ${port}`);
    });
    
    this.setupEventHandlers();
  }
  
  private setupEventHandlers(): void {
    this.mcpServer.on('connection', (client) => {
      console.log('Client connected:', client.id);
      
      client.on('disconnect', () => {
        console.log('Client disconnected:', client.id);
      });
    });
  }
  
  registerResource(resource: Resource): void {
    this.mcpServer.registerResource(resource);
  }
  
  registerTool(tool: Tool): void {
    this.mcpServer.registerTool(tool);
  }
  
  registerPrompt(prompt: Prompt): void {
    this.mcpServer.registerPrompt(prompt);
  }
  
  registerAgent(agent: Agent): void {
    this.mcpServer.registerAgent(agent);
  }
  
  stop(): void {
    this.httpServer.close();
  }
}
```

### Resource Implementation

```typescript
import { Resource, ResourceRequest, ResourceResponse } from '@anthropic/mcp-sdk';
import * as vscode from 'vscode';

class EditorContextResource implements Resource {
  readonly id = 'editor-context';
  readonly description = 'Provides context from the active editor';
  
  async handle(request: ResourceRequest): Promise<ResourceResponse> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return {
        error: {
          message: 'No active editor found'
        }
      };
    }
    
    const document = editor.document;
    const selection = editor.selection;
    const selectedText = document.getText(selection);
    
    return {
      data: {
        filePath: document.uri.fsPath,
        language: document.languageId,
        selectedText: selectedText,
        lineCount: document.lineCount,
        selection: {
          startLine: selection.start.line,
          startCharacter: selection.start.character,
          endLine: selection.end.line,
          endCharacter: selection.end.character
        }
      }
    };
  }
}
```

### Tool Implementation

```typescript
import { Tool, ToolRequest, ToolResponse } from '@anthropic/mcp-sdk';
import * as vscode from 'vscode';

class CodeAnalysisTool implements Tool {
  readonly id = 'code-analysis';
  readonly description = 'Analyzes code and provides insights';
  
  async handle(request: ToolRequest): Promise<ToolResponse> {
    const { filePath } = request.params;
    
    if (!filePath) {
      return {
        error: {
          message: 'File path is required'
        }
      };
    }
    
    try {
      const uri = vscode.Uri.file(filePath);
      const document = await vscode.workspace.openTextDocument(uri);
      
      // Perform code analysis
      const analysis = analyzeCode(document.getText(), document.languageId);
      
      return {
        data: analysis
      };
    } catch (error) {
      return {
        error: {
          message: `Failed to analyze code: ${error.message}`
        }
      };
    }
  }
}

function analyzeCode(code: string, language: string): any {
  // Implement code analysis logic
  // This is a simplified example
  const analysis = {
    language,
    lineCount: code.split('\n').length,
    functionCount: (code.match(/function\s+\w+/g) || []).length,
    classCount: (code.match(/class\s+\w+/g) || []).length,
    importCount: (code.match(/import\s+/g) || []).length
  };
  
  return analysis;
}
```

### Prompt Implementation

```typescript
import { Prompt, PromptRequest, PromptResponse } from '@anthropic/mcp-sdk';

class CodeReviewPrompt implements Prompt {
  readonly id = 'code-review';
  readonly description = 'Generates a code review for the given code';
  
  async handle(request: PromptRequest): Promise<PromptResponse> {
    const { code, language } = request.params;
    
    if (!code) {
      return {
        error: {
          message: 'Code is required'
        }
      };
    }
    
    const prompt = `
You are a code reviewer. Review the following ${language || ''} code and provide feedback:

\`\`\`${language || ''}
${code}
\`\`\`

Your review should include:
1. A summary of what the code does
2. Potential bugs or issues
3. Suggestions for improvement
4. Code style and best practices
`;
    
    return {
      data: {
        prompt
      }
    };
  }
}
```

### Agent Implementation

```typescript
import { Agent, AgentRequest, AgentResponse } from '@anthropic/mcp-sdk';
import * as vscode from 'vscode';

class CodeGenerationAgent implements Agent {
  readonly id = 'code-generation';
  readonly description = 'Generates code based on natural language descriptions';
  
  async handle(request: AgentRequest): Promise<AgentResponse> {
    const { description, language } = request.params;
    
    if (!description) {
      return {
        error: {
          message: 'Description is required'
        }
      };
    }
    
    try {
      // Generate code using Ollama
      const code = await generateCode(description, language || 'typescript');
      
      return {
        data: {
          code
        }
      };
    } catch (error) {
      return {
        error: {
          message: `Failed to generate code: ${error.message}`
        }
      };
    }
  }
}

async function generateCode(description: string, language: string): Promise<string> {
  const prompt = `
Generate ${language} code for the following description:

${description}

Only return the code, no explanations.
`;
  
  const response = await fetch('http://localhost:11434/api/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'gemma3:4b-it-q4_K_M',
      prompt
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to generate code: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data.response;
}
```

## MCP Client Implementation

### Basic Client Setup

```typescript
import { MCPClient, Resource, Tool, Prompt, Agent } from '@anthropic/mcp-sdk';

class X10sionMCPClient {
  private client: MCPClient;
  
  constructor(url: string = 'http://localhost:3000/mcp') {
    this.client = new MCPClient({
      url
    });
    
    this.setupEventHandlers();
  }
  
  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      console.log('Connected to MCP server');
    });
    
    this.client.on('disconnect', () => {
      console.log('Disconnected from MCP server');
    });
    
    this.client.on('error', (error) => {
      console.error('MCP client error:', error);
    });
  }
  
  async connect(): Promise<void> {
    await this.client.connect();
  }
  
  async disconnect(): Promise<void> {
    await this.client.disconnect();
  }
  
  async getResources(): Promise<Resource[]> {
    return this.client.getResources();
  }
  
  async getTools(): Promise<Tool[]> {
    return this.client.getTools();
  }
  
  async getPrompts(): Promise<Prompt[]> {
    return this.client.getPrompts();
  }
  
  async getAgents(): Promise<Agent[]> {
    return this.client.getAgents();
  }
  
  async useResource(id: string, params: any): Promise<any> {
    return this.client.useResource(id, params);
  }
  
  async useTool(id: string, params: any): Promise<any> {
    return this.client.useTool(id, params);
  }
  
  async usePrompt(id: string, params: any): Promise<any> {
    return this.client.usePrompt(id, params);
  }
  
  async useAgent(id: string, params: any): Promise<any> {
    return this.client.useAgent(id, params);
  }
}
```

## Transport Layer Implementation

### Server-Sent Events (SSE) Transport

```typescript
import { createServer, Server } from 'http';
import { MCPServer, Transport } from '@anthropic/mcp-sdk';
import * as EventSource from 'eventsource';

class SSETransport implements Transport {
  private clients: Map<string, any> = new Map();
  
  constructor(server: Server, path: string) {
    server.on('request', (req, res) => {
      if (req.url === path && req.method === 'GET') {
        this.handleSSEConnection(req, res);
      }
    });
  }
  
  private handleSSEConnection(req: any, res: any): void {
    const clientId = Date.now().toString();
    
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    });
    
    res.write(`data: ${JSON.stringify({ type: 'connection', clientId })}\n\n`);
    
    const client = {
      id: clientId,
      send: (data: any) => {
        res.write(`data: ${JSON.stringify(data)}\n\n`);
      }
    };
    
    this.clients.set(clientId, client);
    
    req.on('close', () => {
      this.clients.delete(clientId);
      this.emit('disconnect', clientId);
    });
    
    this.emit('connection', client);
  }
  
  send(clientId: string, data: any): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.send(data);
    }
  }
  
  broadcast(data: any): void {
    for (const client of this.clients.values()) {
      client.send(data);
    }
  }
  
  // Event emitter methods
  private listeners: Map<string, Function[]> = new Map();
  
  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }
  
  emit(event: string, ...args: any[]): void {
    if (this.listeners.has(event)) {
      for (const listener of this.listeners.get(event)!) {
        listener(...args);
      }
    }
  }
}
```

## Best Practices

### 1. Error Handling

- **Graceful Degradation**: Handle errors gracefully and provide fallbacks
- **Detailed Error Messages**: Provide detailed error messages to help with debugging
- **Retry Logic**: Implement retry logic for transient errors
- **Timeout Handling**: Set appropriate timeouts for operations

### 2. Security

- **Authentication**: Implement authentication for MCP servers
- **Authorization**: Implement authorization for resources, tools, prompts, and agents
- **Input Validation**: Validate all input to prevent injection attacks
- **Rate Limiting**: Implement rate limiting to prevent abuse

### 3. Performance

- **Caching**: Cache results to improve performance
- **Batching**: Batch requests when possible
- **Streaming**: Use streaming for large responses
- **Resource Pooling**: Pool resources to reduce overhead

### 4. Extensibility

- **Plugin System**: Implement a plugin system for adding new resources, tools, prompts, and agents
- **Middleware**: Use middleware for cross-cutting concerns
- **Event System**: Implement an event system for notifications
- **Configuration**: Make the implementation configurable

## Latest Developments (May 2025)

- **MCP 2.0**: The latest version of the MCP specification with enhanced capabilities
- **TypeScript SDK**: Official TypeScript SDK for implementing MCP servers and clients
- **Remote MCP Servers**: Support for remote MCP servers accessible over the internet
- **MCP Marketplace**: Emerging marketplace for MCP resources, tools, prompts, and agents

## References

- [Model Context Protocol Specification](https://modelcontextprotocol.io/specification/2025-03-26) (March 2025)
- [MCP TypeScript SDK](https://github.com/anthropic/mcp-sdk-typescript) (May 2025)
- [VS Code Extension API](https://code.visualstudio.com/api/references/vscode-api) (May 2025)

## Timestamp

Last updated: May 19, 2025
