# TypeScript Best Practices

This document outlines best practices for TypeScript development, with a focus on VS Code extension development.

## Type Definitions

### Use Explicit Types

Always use explicit type annotations for function parameters and return types:

```typescript
// Good
function calculateTotal(prices: number[]): number {
  return prices.reduce((total, price) => total + price, 0);
}

// Avoid
function calculateTotal(prices) {
  return prices.reduce((total, price) => total + price, 0);
}
```

### Use Interfaces for Object Shapes

Use interfaces to define the shape of objects:

```typescript
// Define an interface
interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'user';
}

// Use the interface
function getUserById(id: number): User | undefined {
  // Implementation
}
```

### Use Type Aliases for Complex Types

Use type aliases for complex types, unions, and intersections:

```typescript
// Union type
type ID = string | number;

// Intersection type
type AdminUser = User & { permissions: string[] };

// Function type
type Callback = (error: Error | null, result?: any) => void;
```

### Use Generics for Reusable Components

Use generics to create reusable components:

```typescript
// Generic function
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] {
  return obj[key];
}

// Generic interface
interface Repository<T> {
  getById(id: string): Promise<T | undefined>;
  getAll(): Promise<T[]>;
  create(item: Omit<T, 'id'>): Promise<T>;
  update(id: string, item: Partial<T>): Promise<T>;
  delete(id: string): Promise<boolean>;
}
```

### Use Utility Types

TypeScript provides several utility types that can help you manipulate types:

```typescript
// Partial - makes all properties optional
type PartialUser = Partial<User>;

// Required - makes all properties required
type RequiredUser = Required<User>;

// Pick - picks specific properties
type UserCredentials = Pick<User, 'email' | 'password'>;

// Omit - omits specific properties
type PublicUser = Omit<User, 'password'>;

// Record - creates a type with specified keys and values
type UserRoles = Record<string, 'admin' | 'user' | 'guest'>;
```

## Null and Undefined Handling

### Use Optional Parameters and Properties

Use optional parameters and properties instead of allowing `undefined`:

```typescript
// Good
interface User {
  id: number;
  name: string;
  email: string;
  phone?: string; // Optional property
}

// Good
function greet(name: string, greeting?: string): string {
  return `${greeting || 'Hello'}, ${name}!`;
}
```

### Use Nullish Coalescing and Optional Chaining

Use nullish coalescing (`??`) and optional chaining (`?.`) for safer null/undefined handling:

```typescript
// Nullish coalescing
const name = user.name ?? 'Anonymous';

// Optional chaining
const city = user?.address?.city;
```

### Use Non-Null Assertion Only When Necessary

Use the non-null assertion operator (`!`) only when you're certain a value is not null or undefined:

```typescript
// Only use when you're certain
const element = document.getElementById('app')!;
```

## Error Handling

### Use Discriminated Unions for Result Types

Use discriminated unions for result types to handle success and error cases:

```typescript
type Result<T> = 
  | { success: true; data: T }
  | { success: false; error: Error };

function fetchData(): Promise<Result<User>> {
  try {
    // Implementation
    return { success: true, data: user };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}

// Usage
const result = await fetchData();
if (result.success) {
  // Use result.data
} else {
  // Handle result.error
}
```

### Use Custom Error Classes

Create custom error classes for different types of errors:

```typescript
class ValidationError extends Error {
  constructor(
    message: string,
    public readonly field: string
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

class NotFoundError extends Error {
  constructor(
    message: string,
    public readonly resourceType: string,
    public readonly resourceId: string
  ) {
    super(message);
    this.name = 'NotFoundError';
  }
}
```

## Asynchronous Code

### Use Async/Await

Use async/await for asynchronous code:

```typescript
// Good
async function fetchUser(id: string): Promise<User> {
  try {
    const response = await fetch(`/api/users/${id}`);
    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
}

// Avoid
function fetchUser(id: string): Promise<User> {
  return fetch(`/api/users/${id}`)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error: ${response.status}`);
      }
      return response.json();
    })
    .catch(error => {
      console.error('Error fetching user:', error);
      throw error;
    });
}
```

### Use Promise.all for Parallel Operations

Use `Promise.all` for parallel operations:

```typescript
async function fetchUserData(userId: string): Promise<UserData> {
  const [user, posts, comments] = await Promise.all([
    fetchUser(userId),
    fetchPosts(userId),
    fetchComments(userId)
  ]);
  
  return { user, posts, comments };
}
```

## Code Organization

### Use Barrel Exports

Use barrel exports to simplify imports:

```typescript
// In models/index.ts
export * from './user';
export * from './post';
export * from './comment';

// In another file
import { User, Post, Comment } from './models';
```

### Use Namespaces Sparingly

Use namespaces sparingly, prefer modules:

```typescript
// Prefer modules
export interface User {
  // ...
}

export function validateUser(user: User): boolean {
  // ...
}

// Instead of namespaces
namespace UserModule {
  export interface User {
    // ...
  }
  
  export function validateUser(user: User): boolean {
    // ...
  }
}
```

### Use Dependency Injection

Use dependency injection for better testability:

```typescript
// Define interfaces for dependencies
interface Logger {
  log(message: string): void;
  error(message: string, error?: Error): void;
}

interface UserRepository {
  getById(id: string): Promise<User | undefined>;
}

// Use dependency injection
class UserService {
  constructor(
    private readonly logger: Logger,
    private readonly userRepository: UserRepository
  ) {}
  
  async getUserById(id: string): Promise<User> {
    this.logger.log(`Getting user with ID: ${id}`);
    const user = await this.userRepository.getById(id);
    if (!user) {
      const error = new Error(`User not found: ${id}`);
      this.logger.error('User not found', error);
      throw error;
    }
    return user;
  }
}
```

## VS Code Extension Specific

### Use VS Code API Types

Use VS Code API types for better type safety:

```typescript
import * as vscode from 'vscode';

function openDocument(uri: vscode.Uri): Promise<vscode.TextDocument> {
  return vscode.workspace.openTextDocument(uri);
}

function showDocument(document: vscode.TextDocument): Promise<vscode.TextEditor> {
  return vscode.window.showTextDocument(document);
}
```

### Use Disposables

Properly manage disposables to prevent memory leaks:

```typescript
export function activate(context: vscode.ExtensionContext) {
  // Register commands
  const disposable1 = vscode.commands.registerCommand('extension.command1', () => {
    // Implementation
  });
  
  const disposable2 = vscode.commands.registerCommand('extension.command2', () => {
    // Implementation
  });
  
  // Add to subscriptions
  context.subscriptions.push(disposable1, disposable2);
}
```

### Use Configuration

Access extension configuration with proper typing:

```typescript
function getConfiguration<T>(section: string, defaultValue: T): T {
  const config = vscode.workspace.getConfiguration('myExtension');
  return config.get<T>(section, defaultValue);
}

const apiUrl = getConfiguration<string>('apiUrl', 'https://api.example.com');
const maxItems = getConfiguration<number>('maxItems', 10);
```

## Performance

### Use Memoization

Use memoization for expensive operations:

```typescript
function memoize<T, R>(fn: (arg: T) => R): (arg: T) => R {
  const cache = new Map<T, R>();
  
  return (arg: T): R => {
    if (cache.has(arg)) {
      return cache.get(arg)!;
    }
    
    const result = fn(arg);
    cache.set(arg, result);
    return result;
  };
}

const expensiveOperation = memoize((input: string): number => {
  // Expensive calculation
  return input.length * 2;
});
```

### Use Lazy Initialization

Use lazy initialization for expensive resources:

```typescript
class ExpensiveResource {
  private _instance: Resource | undefined;
  
  get instance(): Resource {
    if (!this._instance) {
      this._instance = this.createResource();
    }
    return this._instance;
  }
  
  private createResource(): Resource {
    // Expensive resource creation
    return new Resource();
  }
}
```

## Testing

### Use Dependency Injection for Testability

Use dependency injection to make your code more testable:

```typescript
// Instead of direct dependencies
class UserService {
  private api = new ApiClient();
  
  async getUser(id: string): Promise<User> {
    return this.api.get(`/users/${id}`);
  }
}

// Use dependency injection
class UserService {
  constructor(private readonly api: ApiClient) {}
  
  async getUser(id: string): Promise<User> {
    return this.api.get(`/users/${id}`);
  }
}

// In tests
const mockApi = {
  get: jest.fn().mockResolvedValue({ id: '1', name: 'Test User' })
};
const userService = new UserService(mockApi as unknown as ApiClient);
```

### Use Interface Segregation

Use interface segregation to make your code more testable:

```typescript
// Instead of a large interface
interface ApiClient {
  get<T>(url: string): Promise<T>;
  post<T>(url: string, data: any): Promise<T>;
  put<T>(url: string, data: any): Promise<T>;
  delete<T>(url: string): Promise<T>;
}

// Use smaller interfaces
interface ApiReader {
  get<T>(url: string): Promise<T>;
}

interface ApiWriter {
  post<T>(url: string, data: any): Promise<T>;
  put<T>(url: string, data: any): Promise<T>;
  delete<T>(url: string): Promise<T>;
}

// Then use only what you need
class UserService {
  constructor(private readonly api: ApiReader) {}
  
  async getUser(id: string): Promise<User> {
    return this.api.get(`/users/${id}`);
  }
}
```
