# VS Code Native UI Best Practices (May 2025)

This document outlines best practices for creating native UIs in VS Code extensions, based on the latest information as of May 2025.

## Overview

VS Code provides a rich set of native UI components that can be used to create powerful and consistent user interfaces for extensions. These components include tree views, panels, status bar items, and more.

## Tree Views

Tree views are a powerful way to display hierarchical data in VS Code. They can be used to display files, folders, chat messages, or any other data that can be represented as a list or tree.

### Creating a Tree View

```typescript
import * as vscode from 'vscode';

// Define a tree data provider
class MyTreeDataProvider implements vscode.TreeDataProvider<MyTreeItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<MyTreeItem | undefined | null | void> = new vscode.EventEmitter<MyTreeItem | undefined | null | void>();
  readonly onDidChangeTreeData: vscode.Event<MyTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

  refresh(): void {
    this._onDidChangeTreeData.fire();
  }

  getTreeItem(element: MyTreeItem): vscode.TreeItem {
    return element;
  }

  getChildren(element?: MyTreeItem): Thenable<MyTreeItem[]> {
    if (element) {
      return Promise.resolve(this.getChildrenOfElement(element));
    } else {
      return Promise.resolve(this.getRootElements());
    }
  }

  private getChildrenOfElement(element: MyTreeItem): MyTreeItem[] {
    // Return children of the element
    return [];
  }

  private getRootElements(): MyTreeItem[] {
    // Return root elements
    return [
      new MyTreeItem('Item 1', vscode.TreeItemCollapsibleState.Collapsed),
      new MyTreeItem('Item 2', vscode.TreeItemCollapsibleState.Collapsed)
    ];
  }
}

// Define a tree item
class MyTreeItem extends vscode.TreeItem {
  constructor(
    public readonly label: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState
  ) {
    super(label, collapsibleState);
    this.tooltip = `${this.label}`;
    this.description = 'Description';
  }
}

// Register the tree view
const treeDataProvider = new MyTreeDataProvider();
const treeView = vscode.window.createTreeView('myTreeView', { treeDataProvider });
```

### Tree View Best Practices

1. **Use Icons**: Use icons to visually distinguish different types of items
2. **Use Context Values**: Set context values to enable context menu items
3. **Use Descriptions**: Add descriptions to provide additional information
4. **Use Tooltips**: Add tooltips to provide more detailed information
5. **Use Collapsible State**: Set the appropriate collapsible state for items
6. **Use Commands**: Associate commands with tree items for actions
7. **Use Reveal**: Use the reveal method to scroll to specific items

### Using Tree View for Chat Interfaces

Tree views can be used to create chat interfaces without using webviews, which can be more resource-efficient:

```typescript
// Define a chat message interface
interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
}

// Define a tree item for chat messages
class ChatMessageTreeItem extends vscode.TreeItem {
    constructor(
        public readonly message: ChatMessage
    ) {
        super(message.content, vscode.TreeItemCollapsibleState.None);

        // Set icon based on role
        if (message.role === 'user') {
            this.iconPath = new vscode.ThemeIcon('account');
        } else if (message.role === 'assistant') {
            this.iconPath = new vscode.ThemeIcon('hubot');
        }

        // Set tooltip and description
        const date = new Date(message.timestamp);
        this.tooltip = `${message.role} - ${date.toLocaleTimeString()}`;
        this.description = date.toLocaleTimeString();

        // Set context value for context menu
        this.contextValue = `chatMessage-${message.role}`;
    }
}

// Create a tree data provider for chat messages
class ChatViewProvider implements vscode.TreeDataProvider<ChatMessageTreeItem> {
    private _onDidChangeTreeData = new vscode.EventEmitter<ChatMessageTreeItem | undefined>();
    readonly onDidChangeTreeData = this._onDidChangeTreeData.event;

    private messages: ChatMessage[] = [];

    getTreeItem(element: ChatMessageTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(): ChatMessageTreeItem[] {
        return this.messages.map(msg => new ChatMessageTreeItem(msg));
    }

    addMessage(message: ChatMessage): void {
        this.messages.push(message);
        this._onDidChangeTreeData.fire(undefined);
    }
}

// Register the tree view
const chatProvider = new ChatViewProvider();
const chatView = vscode.window.createTreeView('chatView', { treeDataProvider: chatProvider });

// Add a command to send messages
vscode.commands.registerCommand('chat.sendMessage', async () => {
    const input = await vscode.window.showInputBox({ prompt: 'Type your message' });
    if (input) {
        // Add user message
        const userMsg: ChatMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: input,
            timestamp: Date.now()
        };
        chatProvider.addMessage(userMsg);

        // Process and add response
        // ...
    }
});
```

## Status Bar Items

Status bar items are a great way to provide quick access to commands and display status information.

### Creating a Status Bar Item

```typescript
import * as vscode from 'vscode';

// Create a status bar item
const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
statusBarItem.text = '$(rocket) My Extension';
statusBarItem.tooltip = 'Click to open My Extension';
statusBarItem.command = 'myExtension.openPanel';
statusBarItem.show();
```

### Status Bar Best Practices

1. **Use Icons**: Use icons to visually distinguish different status bar items
2. **Use Tooltips**: Add tooltips to provide more detailed information
3. **Use Commands**: Associate commands with status bar items
4. **Use Priority**: Set the appropriate priority for status bar items
5. **Show/Hide Dynamically**: Show or hide status bar items based on context

## Quick Picks

Quick picks are a great way to allow users to select from a list of options.

### Creating a Quick Pick

```typescript
import * as vscode from 'vscode';

async function showQuickPick() {
  const options = [
    { label: 'Option 1', description: 'Description 1' },
    { label: 'Option 2', description: 'Description 2' },
    { label: 'Option 3', description: 'Description 3' }
  ];

  const selectedOption = await vscode.window.showQuickPick(options, {
    placeHolder: 'Select an option',
    ignoreFocusOut: true
  });

  if (selectedOption) {
    vscode.window.showInformationMessage(`Selected: ${selectedOption.label}`);
  }
}
```

### Quick Pick Best Practices

1. **Use Descriptions**: Add descriptions to provide additional information
2. **Use Icons**: Use icons to visually distinguish different options
3. **Use Placeholders**: Add placeholders to provide guidance
4. **Use ignoreFocusOut**: Set ignoreFocusOut to true for important selections
5. **Use canPickMany**: Enable multi-select when appropriate

## Input Boxes

Input boxes are a great way to get text input from users.

### Creating an Input Box

```typescript
import * as vscode from 'vscode';

async function showInputBox() {
  const result = await vscode.window.showInputBox({
    prompt: 'Enter a value',
    placeHolder: 'Value',
    value: 'Default value',
    ignoreFocusOut: true,
    validateInput: (value) => {
      return value.length > 0 ? null : 'Value cannot be empty';
    }
  });

  if (result) {
    vscode.window.showInformationMessage(`Entered: ${result}`);
  }
}
```

### Input Box Best Practices

1. **Use Prompts**: Add prompts to provide guidance
2. **Use Placeholders**: Add placeholders to provide examples
3. **Use Default Values**: Provide default values when appropriate
4. **Use Validation**: Validate input to ensure it meets requirements
5. **Use ignoreFocusOut**: Set ignoreFocusOut to true for important inputs

## Progress Indicators

Progress indicators are a great way to show users that a long-running operation is in progress.

### Creating a Progress Indicator

```typescript
import * as vscode from 'vscode';

async function showProgress() {
  await vscode.window.withProgress({
    location: vscode.ProgressLocation.Notification,
    title: 'Long-running operation',
    cancellable: true
  }, async (progress, token) => {
    token.onCancellationRequested(() => {
      console.log('User canceled the operation');
    });

    progress.report({ increment: 0, message: 'Starting...' });

    for (let i = 0; i < 10; i++) {
      if (token.isCancellationRequested) {
        return;
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
      progress.report({ increment: 10, message: `Step ${i + 1} of 10` });
    }

    return 'Done';
  });
}
```

### Progress Indicator Best Practices

1. **Use Increments**: Report progress increments to show progress
2. **Use Messages**: Update messages to provide status information
3. **Support Cancellation**: Allow users to cancel long-running operations
4. **Use Appropriate Location**: Choose the appropriate location for the progress indicator
5. **Return Results**: Return results from the progress operation

## Notifications

Notifications are a great way to provide feedback to users.

### Creating a Notification

```typescript
import * as vscode from 'vscode';

// Show an information message
vscode.window.showInformationMessage('This is an information message');

// Show a warning message
vscode.window.showWarningMessage('This is a warning message');

// Show an error message
vscode.window.showErrorMessage('This is an error message');

// Show a message with actions
vscode.window.showInformationMessage('Do you want to continue?', 'Yes', 'No')
  .then(selection => {
    if (selection === 'Yes') {
      vscode.window.showInformationMessage('Continuing...');
    } else if (selection === 'No') {
      vscode.window.showInformationMessage('Cancelled');
    }
  });
```

### Notification Best Practices

1. **Use Appropriate Type**: Use the appropriate notification type (information, warning, error)
2. **Keep Messages Concise**: Keep messages short and to the point
3. **Provide Actions**: Add actions to allow users to respond to notifications
4. **Use Modal Notifications**: Use modal notifications for important messages
5. **Avoid Excessive Notifications**: Don't overwhelm users with too many notifications

## Theming

VS Code provides a rich set of theme colors that can be used to style UI components.

### Using Theme Colors

```typescript
import * as vscode from 'vscode';

// Get theme colors
const editorBackground = new vscode.ThemeColor('editor.background');
const editorForeground = new vscode.ThemeColor('editor.foreground');

// Use theme colors in UI components
const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
statusBarItem.backgroundColor = editorBackground;
statusBarItem.color = editorForeground;
```

### Theming Best Practices

1. **Use Theme Colors**: Use VS Code's theme colors for consistent styling
2. **Support Dark and Light Themes**: Ensure your UI works well in both dark and light themes
3. **Support High Contrast Themes**: Ensure your UI works well in high contrast themes
4. **Use Icons from Icon Theme**: Use icons from the current icon theme
5. **Test with Different Themes**: Test your UI with different themes

## Accessibility

VS Code has a strong focus on accessibility, and extensions should follow the same principles.

### Accessibility Best Practices

1. **Keyboard Navigation**: Ensure all functionality is accessible via keyboard
2. **Screen Reader Support**: Ensure your UI works well with screen readers
3. **Color Contrast**: Ensure sufficient contrast for text and UI elements
4. **Focus Management**: Properly manage focus for interactive elements
5. **Aria Attributes**: Use appropriate ARIA attributes

## Performance

Performance is critical for a good user experience, especially for UI components.

### Performance Best Practices

1. **Lazy Loading**: Load components and resources only when needed
2. **Efficient Data Structures**: Use efficient data structures for large data sets
3. **Debounce and Throttle**: Use debounce and throttle for frequent events
4. **Virtualization**: Use virtualization for large lists
5. **Asynchronous Operations**: Use asynchronous operations for long-running tasks
6. **Prefer Native UI over Webviews**: Use native VS Code UI components when possible instead of webviews for better performance and lower resource usage
7. **Limit Tree View Updates**: Batch updates to tree views to reduce UI refreshes
8. **Optimize Command Execution**: Ensure commands execute quickly and efficiently

### Native UI vs. Webviews

When deciding between native UI components and webviews, consider the following:

#### Native UI Advantages
- Lower memory footprint
- Better performance
- Consistent with VS Code's look and feel
- Better accessibility
- Automatic theme support
- Lower CPU usage

#### Webview Advantages
- More flexible UI customization
- Support for complex layouts
- Rich text formatting
- Custom styling
- Interactive elements

#### When to Use Native UI
- For simple interfaces
- For displaying hierarchical data
- For performance-critical extensions
- For extensions targeting low-resource environments
- When consistency with VS Code is important

#### When to Use Webviews
- For complex, highly interactive UIs that cannot be achieved with native components
- When rich text formatting is essential
- When custom styling is a requirement that cannot be met with theme colors
- For visualizations and graphics that require HTML canvas or SVG

## References

- [VS Code Extension API Documentation](https://code.visualstudio.com/api/references/vscode-api) (May 2025)
- [VS Code Extension Samples](https://github.com/microsoft/vscode-extension-samples) (May 2025)
- [VS Code Theme Color Reference](https://code.visualstudio.com/api/references/theme-color) (May 2025)
- [VS Code Tree View API Documentation](https://code.visualstudio.com/api/extension-guides/tree-view) (May 2025)
- [VS Code UX Guidelines](https://code.visualstudio.com/api/ux-guidelines/overview) (May 2025)

## Timestamp

Last updated: May 19, 2025
