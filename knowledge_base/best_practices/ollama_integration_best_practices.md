# Ollama Integration Best Practices (May 2025)

This document outlines best practices for integrating Ollama with VS Code extensions, based on the latest information as of May 2025.

## Overview

Ollama is a popular framework for running large language models locally. It provides a simple API for interacting with these models, making it an excellent choice for VS Code extensions that need to provide AI capabilities without relying on cloud services.

## API Endpoints

As of May 2025, Ollama provides the following key API endpoints:

### Generate Text

```
POST http://localhost:11434/api/generate
```

Request body:
```json
{
  "model": "string",
  "prompt": "string",
  "system": "string",
  "template": "string",
  "context": [number],
  "stream": boolean,
  "raw": boolean,
  "format": "json",
  "options": {
    "num_predict": number,
    "temperature": number,
    "top_p": number,
    "top_k": number,
    "presence_penalty": number,
    "frequency_penalty": number,
    "seed": number
  }
}
```

### Generate Embeddings

```
POST http://localhost:11434/api/embeddings
```

Request body:
```json
{
  "model": "string",
  "prompt": "string",
  "options": {
    "temperature": number
  }
}
```

### List Models

```
GET http://localhost:11434/api/tags
```

### Pull Model

```
POST http://localhost:11434/api/pull
```

Request body:
```json
{
  "name": "string",
  "insecure": boolean
}
```

## Best Practices

### 1. Connection Management

- **Health Check**: Implement a health check to verify that Ollama is running before attempting to use it.
- **Retry Logic**: Implement retry logic with exponential backoff for transient failures.
- **Timeout Handling**: Set appropriate timeouts for API calls to prevent hanging.
- **Error Handling**: Provide clear error messages when Ollama is not available or returns an error.

```typescript
async function isOllamaRunning(url: string = 'http://localhost:11434'): Promise<boolean> {
  try {
    const response = await fetch(`${url}/api/tags`, {
      method: 'GET',
      signal: AbortSignal.timeout(2000) // 2-second timeout
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}
```

### 2. Model Management

- **Model Verification**: Verify that the requested model is available before attempting to use it.
- **Model Fallback**: Implement fallback logic to use an alternative model if the requested model is not available.
- **Model Caching**: Cache model information to reduce API calls.
- **Model Recommendations**: Provide recommendations for models based on the task.

```typescript
async function getAvailableModels(url: string = 'http://localhost:11434'): Promise<string[]> {
  try {
    const response = await fetch(`${url}/api/tags`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    if (!response.ok) {
      throw new Error(`Failed to get models: ${response.statusText}`);
    }

    const data = await response.json();
    return data.models.map((model: any) => model.name);
  } catch (error) {
    console.error('Error getting available models:', error);
    return [];
  }
}
```

### 3. Prompt Engineering

- **System Prompts**: Use system prompts to provide context and instructions to the model.
- **Context Window Management**: Be mindful of the model's context window size and chunk large inputs.
- **Prompt Templates**: Use templates to structure prompts consistently.
- **Few-Shot Examples**: Include examples in the prompt to guide the model's responses.

```typescript
function createPrompt(userInput: string, context: any, guidelines: string): string {
  return `
System: You are an AI assistant helping with coding tasks. Follow these guidelines:
${guidelines}

Context:
${JSON.stringify(context, null, 2)}

User: ${userInput}
`;
}
```

### 4. Response Handling

- **Streaming**: Use streaming for long responses to provide immediate feedback.
- **Parsing**: Parse JSON responses carefully, handling potential errors.
- **Formatting**: Format responses for display in the VS Code UI.
- **Truncation**: Truncate very long responses to prevent UI issues.

```typescript
async function sendToOllama(prompt: string, model: string = 'gemma3:4b-it-q4_K_M', stream: boolean = false): Promise<string> {
  try {
    const response = await fetch('http://localhost:11434/api/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        prompt,
        stream
      }),
      signal: AbortSignal.timeout(30000) // 30-second timeout
    });

    if (!response.ok) {
      throw new Error(`Failed to generate text: ${response.statusText}`);
    }

    if (stream) {
      // Handle streaming response
      return handleStreamingResponse(response);
    } else {
      // Handle non-streaming response
      const data = await response.json();
      return data.response;
    }
  } catch (error) {
    console.error('Error sending to Ollama:', error);
    throw error;
  }
}

async function handleStreamingResponse(response: Response): Promise<string> {
  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('Failed to get response reader');
  }

  let result = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = new TextDecoder().decode(value);
      try {
        const parsedChunk = JSON.parse(chunk);
        result += parsedChunk.response;
        // Emit event or callback with partial result
      } catch (error) {
        console.warn('Failed to parse chunk:', error);
      }
    }
  } finally {
    reader.releaseLock();
  }

  return result;
}
```

### 5. Security Considerations

- **URL Configuration**: Allow users to configure the Ollama URL, but store it securely.
- **Input Validation**: Validate user input to prevent injection attacks.
- **Model Restrictions**: Consider allowing users to restrict which models can be used.
- **Content Filtering**: Implement content filtering for sensitive contexts.

```typescript
// Store Ollama URL securely
async function storeOllamaUrl(context: vscode.ExtensionContext, url: string): Promise<void> {
  await context.secrets.store('ollamaUrl', url);
}

// Retrieve Ollama URL securely
async function getOllamaUrl(context: vscode.ExtensionContext): Promise<string> {
  const url = await context.secrets.get('ollamaUrl');
  return url || 'http://localhost:11434';
}

// Validate URL
function isValidOllamaUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return ['http:', 'https:'].includes(parsedUrl.protocol);
  } catch (error) {
    return false;
  }
}
```

### 6. Performance Optimization

- **Caching**: Cache responses for identical or similar queries.
- **Batching**: Batch multiple requests when possible.
- **Compression**: Use compression for large requests and responses.
- **Background Processing**: Process large requests in the background.

```typescript
// Simple in-memory cache
const responseCache = new Map<string, { response: string, timestamp: number }>();

async function getCachedOrFresh(prompt: string, model: string, maxAge: number = 60000): Promise<string> {
  const cacheKey = `${model}:${prompt}`;
  const cached = responseCache.get(cacheKey);

  if (cached && (Date.now() - cached.timestamp) < maxAge) {
    return cached.response;
  }

  const response = await sendToOllama(prompt, model);
  responseCache.set(cacheKey, { response, timestamp: Date.now() });

  return response;
}
```

## Integration with VS Code

### 1. Commands and Menus

- Register commands for interacting with Ollama
- Add menu items for these commands
- Use context menus for code-specific actions

### 2. Settings

- Provide settings for configuring Ollama integration
- Use appropriate setting types (string, boolean, number, etc.)
- Provide sensible defaults

### 3. Status Bar

- Show Ollama connection status in the status bar
- Provide quick actions from the status bar

### 4. Webview Integration

- Use webviews for rich UI interactions
- Implement proper message passing between extension and webview

## Latest Developments (May 2025)

- **Ollama 0.1.27**: Latest stable version with improved API performance
- **Model Compatibility**: Support for Llama 3.3, Gemma 3, and other latest models
- **VS Code Extensions**: Several popular extensions now integrate with Ollama
- **Security Updates**: Enhanced security features for local model usage

## References

- [Ollama API Documentation](https://github.com/ollama/ollama/blob/main/docs/api.md) (May 2025)
- [VS Code Extension API](https://code.visualstudio.com/api/references/vscode-api) (May 2025)
- [Continue VS Code Extension](https://github.com/continuedev/continue) (Example of Ollama integration)

## Timestamp

Last updated: May 19, 2025
