# VS Code Extension Best Practices

This document outlines best practices for developing VS Code extensions, with a focus on performance, user experience, and maintainability.

## Performance

### Activation Events

Use specific activation events to delay loading your extension until it's needed:

```json
"activationEvents": [
  "onCommand:myExtension.myCommand",
  "onLanguage:javascript",
  "onView:myExtension.myView"
]
```

Avoid using `*` as an activation event, as this will load your extension on every VS Code startup.

### Asynchronous Operations

Perform expensive operations asynchronously to avoid blocking the UI:

```typescript
// Bad: Blocks the UI
function doExpensiveOperation() {
  const result = expensiveCalculation();
  return result;
}

// Good: Doesn't block the UI
async function doExpensiveOperation() {
  return new Promise<Result>((resolve) => {
    setTimeout(() => {
      const result = expensiveCalculation();
      resolve(result);
    }, 0);
  });
}
```

### Debouncing

Debounce operations that might be triggered frequently:

```typescript
let timeout: NodeJS.Timeout | undefined;

function debouncedOperation() {
  if (timeout) {
    clearTimeout(timeout);
  }
  
  timeout = setTimeout(() => {
    // Perform operation
    timeout = undefined;
  }, 300);
}
```

### Dispose Resources

Always dispose resources when they're no longer needed:

```typescript
const disposable = vscode.window.onDidChangeActiveTextEditor(() => {
  // Handle event
});

// Later, when no longer needed:
disposable.dispose();

// Or, better, add to context.subscriptions:
context.subscriptions.push(disposable);
```

## User Experience

### Progress Indication

Show progress for long-running operations:

```typescript
vscode.window.withProgress({
  location: vscode.ProgressLocation.Notification,
  title: "Doing something...",
  cancellable: true
}, async (progress, token) => {
  token.onCancellationRequested(() => {
    console.log("User canceled the operation");
  });
  
  progress.report({ increment: 0 });
  
  // Step 1
  await step1();
  progress.report({ increment: 50, message: "Step 1 complete" });
  
  // Step 2
  await step2();
  progress.report({ increment: 50, message: "Step 2 complete" });
  
  return "Done";
});
```

### Error Handling

Handle errors gracefully and provide useful error messages:

```typescript
try {
  await riskyOperation();
} catch (error) {
  vscode.window.showErrorMessage(`Operation failed: ${error.message}`);
  console.error("Detailed error:", error);
}
```

### Configuration

Provide sensible defaults and validate user configuration:

```typescript
const config = vscode.workspace.getConfiguration('myExtension');
const value = config.get<string>('setting', 'default'); // Provide default

if (!isValidValue(value)) {
  vscode.window.showWarningMessage(`Invalid value for 'myExtension.setting': ${value}`);
  return;
}
```

### Commands

Use descriptive command names and provide keyboard shortcuts for common operations:

```json
"contributes": {
  "commands": [
    {
      "command": "myExtension.doSomething",
      "title": "My Extension: Do Something"
    }
  ],
  "keybindings": [
    {
      "command": "myExtension.doSomething",
      "key": "ctrl+shift+d",
      "mac": "cmd+shift+d",
      "when": "editorFocus"
    }
  ]
}
```

## Architecture

### Separation of Concerns

Separate your code into modules with clear responsibilities:

- `extension.ts`: Entry point, command registration
- `commands/`: Command implementations
- `services/`: Business logic
- `views/`: UI components
- `utils/`: Utility functions

### Dependency Injection

Use dependency injection for better testability:

```typescript
class CommandHandler {
  constructor(private service: Service) {}
  
  async execute() {
    return this.service.doSomething();
  }
}

// In production:
const handler = new CommandHandler(new RealService());

// In tests:
const handler = new CommandHandler(new MockService());
```

### Configuration

Use VS Code's configuration API for user settings:

```typescript
// In package.json
"contributes": {
  "configuration": {
    "title": "My Extension",
    "properties": {
      "myExtension.setting": {
        "type": "string",
        "default": "default value",
        "description": "Description of the setting"
      }
    }
  }
}

// In code
const config = vscode.workspace.getConfiguration('myExtension');
const value = config.get<string>('setting');
```

## Testing

### Unit Tests

Write unit tests for your business logic:

```typescript
suite('My Extension Tests', () => {
  test('My Test', () => {
    const result = myFunction();
    assert.strictEqual(result, expectedValue);
  });
});
```

### Integration Tests

Write integration tests that run in a VS Code environment:

```typescript
suite('Extension Test Suite', () => {
  vscode.window.showInformationMessage('Start all tests.');

  test('Extension should be present', () => {
    assert.ok(vscode.extensions.getExtension('publisher.myExtension'));
  });

  test('Should register commands', async () => {
    const commands = await vscode.commands.getCommands();
    assert.ok(commands.includes('myExtension.myCommand'));
  });
});
```

### Mocking

Use mocking to isolate the code being tested:

```typescript
// Create a mock VS Code API
const mockVscode = {
  window: {
    showInformationMessage: sinon.spy()
  }
};

// Test with the mock
function testFunction() {
  myFunction(mockVscode);
  assert.ok(mockVscode.window.showInformationMessage.calledOnce);
}
```

## Documentation

### README

Provide a comprehensive README with:

- Description of the extension
- Installation instructions
- Usage instructions with screenshots
- Configuration options
- Troubleshooting tips
- Contributing guidelines

### Code Comments

Document your code with clear comments:

```typescript
/**
 * Performs an operation on the given input.
 * 
 * @param input The input to operate on
 * @returns The result of the operation
 * @throws Error if the input is invalid
 */
function myFunction(input: string): Result {
  // Implementation
}
```

### Change Log

Maintain a change log to document changes between versions:

```markdown
# Change Log

## [1.0.0] - 2023-01-01
### Added
- Initial release

## [1.1.0] - 2023-02-01
### Added
- New feature X
### Fixed
- Bug in feature Y
```

## Security

### Input Validation

Validate all user input:

```typescript
function processUserInput(input: string) {
  if (!input || input.length > 100) {
    throw new Error("Invalid input");
  }
  
  // Process input
}
```

### Secure Storage

Use VS Code's secret storage for sensitive information:

```typescript
// Store a secret
await context.secrets.store('mySecret', 'secretValue');

// Retrieve a secret
const secret = await context.secrets.get('mySecret');
```

### External Process Execution

Be careful when executing external processes:

```typescript
// Bad: Vulnerable to command injection
exec(`git clone ${userInput}`, (error, stdout, stderr) => {
  // Handle result
});

// Good: Use an array of arguments
spawn('git', ['clone', userInput], {
  // Options
});
```
