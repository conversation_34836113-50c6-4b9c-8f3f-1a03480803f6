# Dependency Management Best Practices

## Overview

This document outlines best practices for managing dependencies in the X10sion project. Keeping dependencies up-to-date is crucial for security, performance, and compatibility. This guide covers tools and strategies for different programming languages, with a focus on Node.js/TypeScript.

## General Principles

### 1. Regular Updates

- **Schedule Regular Updates**: Set a regular schedule (e.g., monthly) for dependency updates.
- **Prioritize Security Updates**: Prioritize updates that address security vulnerabilities.
- **Update Incrementally**: Update dependencies incrementally to minimize breaking changes.
- **Test After Updates**: Always run tests after updating dependencies to catch regressions.

### 2. Version Pinning

- **Pin Exact Versions**: Use exact versions (`1.2.3` instead of `^1.2.3` or `~1.2.3`) for critical dependencies.
- **Use Lock Files**: Always commit lock files (`package-lock.json`, `yarn.lock`, `poetry.lock`, etc.) to ensure consistent installations.
- **Document Version Decisions**: Document why specific versions are pinned, especially if avoiding updates.

### 3. Security Scanning

- **Use Security Scanners**: Regularly scan dependencies for known vulnerabilities.
- **Integrate with CI/CD**: Integrate security scanning into CI/CD pipelines.
- **Set Up Alerts**: Configure alerts for new vulnerabilities in dependencies.

## Node.js/TypeScript

### Automated Update Tools

#### npm-check-updates (ncu)

The `npm-check-updates` tool is the recommended way to update Node.js dependencies:

```bash
# Install globally
npm install -g npm-check-updates

# Check for updates without modifying package.json
ncu

# Update all dependencies in package.json
ncu -u

# Update only production dependencies
ncu -u --dep prod

# Update only development dependencies
ncu -u --dep dev

# Update to latest versions, ignoring specified version constraints
ncu -u --newest

# Update a specific package
ncu -u <package-name>

# After updating package.json, install the new versions
npm install
```

#### Renovate Bot

For automated dependency updates in CI/CD:

- **Renovate Bot**: Configure Renovate Bot in your repository to automatically create PRs for dependency updates.
- **Configuration**: Create a `renovate.json` file to customize update behavior.
- **Scheduling**: Schedule updates during off-hours to minimize disruption.

### Manual Update Process

1. **Check for Updates**:
   ```bash
   ncu
   ```

2. **Review Changes**:
   - Review the list of available updates
   - Check changelogs for breaking changes
   - Prioritize security updates

3. **Update Dependencies**:
   ```bash
   ncu -u
   npm install
   ```

4. **Run Tests**:
   ```bash
   npm test
   ```

5. **Fix Issues**:
   - Address any breaking changes
   - Update code to use new APIs
   - Fix failing tests

6. **Commit Changes**:
   - Commit updated `package.json` and `package-lock.json`
   - Include detailed commit message about updates

### Handling Breaking Changes

- **Read Release Notes**: Always read release notes before updating major versions.
- **Update One Major Version at a Time**: When updating across major versions, update one major version at a time.
- **Create Separate PRs**: Create separate PRs for major version updates to isolate changes.
- **Use Codemods**: Use codemods when available to automate code changes for new versions.

## Python

### Tools for Python

#### pip-tools

```bash
# Install pip-tools
pip install pip-tools

# Generate requirements.txt from requirements.in
pip-compile requirements.in

# Update all dependencies
pip-compile --upgrade requirements.in

# Install updated dependencies
pip-sync requirements.txt
```

#### Poetry

```bash
# Update all dependencies
poetry update

# Update a specific package
poetry update <package-name>

# Show outdated packages
poetry show --outdated
```

## Other Languages

### Java/Kotlin

- **Maven Versions Plugin**: Use the Maven Versions Plugin to update dependencies.
- **Gradle Version Catalog**: Use Gradle Version Catalog to manage dependency versions.

### Ruby

- **Bundler**: Use `bundle update` to update dependencies.
- **Dependabot**: Configure Dependabot for automated updates.

### Go

- **Go Modules**: Use `go get -u` to update dependencies.
- **Go Mod Tidy**: Run `go mod tidy` to clean up dependencies.

## AI Agent for Dependency Management

X10sion includes a specialized AI agent for dependency management that can:

1. **Scan for Outdated Dependencies**: Automatically scan the project for outdated dependencies.
2. **Research Updates**: Search the web for information about the latest versions of dependencies.
3. **Analyze Compatibility**: Analyze compatibility between different dependencies.
4. **Suggest Updates**: Suggest specific updates based on project requirements.
5. **Generate Update Commands**: Generate the commands needed to update dependencies.
6. **Document Changes**: Document changes made to dependencies.

### Using the Dependency Management Agent

The agent can be invoked with commands like:

- "Check for outdated dependencies"
- "Update all dependencies to the latest versions"
- "Update security-critical dependencies"
- "Research breaking changes in the latest version of [package]"
- "Generate a dependency update plan"

## Security Considerations

- **Verify Package Sources**: Only use packages from trusted sources.
- **Check for Typosquatting**: Be aware of typosquatting attacks (e.g., `lodash` vs. `l0dash`).
- **Review New Dependencies**: Carefully review new dependencies before adding them.
- **Minimize Dependencies**: Minimize the number of dependencies to reduce the attack surface.
- **Use Security Scanners**: Use tools like `npm audit`, `safety`, or `snyk` to scan for vulnerabilities.

## Continuous Integration

- **Automated Updates**: Set up automated dependency updates in CI/CD.
- **Security Scanning**: Include security scanning in CI/CD pipelines.
- **Breaking Change Detection**: Set up tests to detect breaking changes from updates.
- **Scheduled Updates**: Schedule regular dependency updates.

## References

- [npm-check-updates Documentation](https://github.com/raineorshine/npm-check-updates)
- [Renovate Bot Documentation](https://docs.renovatebot.com/)
- [npm audit Documentation](https://docs.npmjs.com/cli/v8/commands/npm-audit)
- [pip-tools Documentation](https://github.com/jazzband/pip-tools)
- [Poetry Documentation](https://python-poetry.org/docs/)
