# RAG Implementation Best Practices (May 2025)

This document outlines best practices for implementing Retrieval-Augmented Generation (RAG) in VS Code extensions, based on the latest information as of May 2025.

## Overview

Retrieval-Augmented Generation (RAG) is a technique that enhances large language models by retrieving relevant information from external sources and incorporating it into the generation process. In the context of VS Code extensions, RAG can be used to provide more accurate and contextually relevant AI assistance by retrieving information from the user's codebase, documentation, and other sources.

## RAG Architecture

A typical RAG implementation in a VS Code extension consists of the following components:

1. **Document Processor**: Processes documents (code files, markdown, etc.) into chunks
2. **Embedding Generator**: Generates embeddings for document chunks
3. **Vector Store**: Stores embeddings and enables similarity search
4. **Query Processor**: Processes user queries and retrieves relevant chunks
5. **Context Assembler**: Assembles retrieved chunks into context for the LLM
6. **LLM Interface**: Sends the enhanced prompt to the LLM and processes the response

## Document Processing

### Chunking Strategies

Different types of documents require different chunking strategies:

#### Code Files

```typescript
function chunkCodeFile(content: string, language: string): string[] {
  if (language === 'typescript' || language === 'javascript') {
    // Use tree-sitter to parse the code and chunk by function/class
    return chunkByFunctionOrClass(content, language);
  } else {
    // Fall back to simpler chunking for unsupported languages
    return chunkByLines(content, 50, 25); // 50 lines per chunk, 25 lines overlap
  }
}

function chunkByFunctionOrClass(content: string, language: string): string[] {
  const parser = getParser(language);
  const tree = parser.parse(content);
  const root = tree.rootNode;
  
  const chunks: string[] = [];
  const functionNodes = findNodes(root, ['function_declaration', 'method_definition', 'class_declaration']);
  
  for (const node of functionNodes) {
    chunks.push(content.substring(node.startIndex, node.endIndex));
  }
  
  return chunks;
}

function chunkByLines(content: string, linesPerChunk: number, overlap: number): string[] {
  const lines = content.split('\n');
  const chunks: string[] = [];
  
  for (let i = 0; i < lines.length; i += linesPerChunk - overlap) {
    const chunk = lines.slice(i, i + linesPerChunk).join('\n');
    chunks.push(chunk);
  }
  
  return chunks;
}
```

#### Markdown Files

```typescript
function chunkMarkdownFile(content: string): string[] {
  // Parse the markdown to get the structure
  const parsedMarkdown = parseMarkdown(content);
  
  // Chunk by heading
  return chunkByHeading(parsedMarkdown);
}

function chunkByHeading(parsedMarkdown: any): string[] {
  const chunks: string[] = [];
  let currentChunk = '';
  let currentHeadingLevel = 0;
  
  for (const node of parsedMarkdown.children) {
    if (node.type === 'heading') {
      if (currentChunk) {
        chunks.push(currentChunk);
      }
      currentChunk = renderMarkdownNode(node);
      currentHeadingLevel = node.depth;
    } else if (node.type === 'heading' && node.depth <= currentHeadingLevel) {
      chunks.push(currentChunk);
      currentChunk = renderMarkdownNode(node);
      currentHeadingLevel = node.depth;
    } else {
      currentChunk += '\n' + renderMarkdownNode(node);
    }
  }
  
  if (currentChunk) {
    chunks.push(currentChunk);
  }
  
  return chunks;
}
```

### Metadata Extraction

Extract metadata from chunks to enhance retrieval:

```typescript
interface ChunkMetadata {
  filePath: string;
  language: string;
  startLine: number;
  endLine: number;
  type: 'function' | 'class' | 'method' | 'heading' | 'paragraph';
  name?: string;
  parent?: string;
}

function extractMetadata(chunk: string, filePath: string, language: string, startLine: number): ChunkMetadata {
  const metadata: ChunkMetadata = {
    filePath,
    language,
    startLine,
    endLine: startLine + chunk.split('\n').length - 1,
    type: 'paragraph'
  };
  
  if (language === 'typescript' || language === 'javascript') {
    // Extract function/class/method name
    const functionMatch = chunk.match(/function\s+(\w+)/);
    const classMatch = chunk.match(/class\s+(\w+)/);
    const methodMatch = chunk.match(/(\w+)\s*\([^)]*\)\s*{/);
    
    if (functionMatch) {
      metadata.type = 'function';
      metadata.name = functionMatch[1];
    } else if (classMatch) {
      metadata.type = 'class';
      metadata.name = classMatch[1];
    } else if (methodMatch) {
      metadata.type = 'method';
      metadata.name = methodMatch[1];
    }
  } else if (language === 'markdown') {
    // Extract heading
    const headingMatch = chunk.match(/^#+\s+(.+)$/m);
    if (headingMatch) {
      metadata.type = 'heading';
      metadata.name = headingMatch[1];
    }
  }
  
  return metadata;
}
```

## Embedding Generation

### Local Embedding Models

For privacy and performance, use local embedding models:

```typescript
async function generateEmbedding(text: string): Promise<number[]> {
  // Use Ollama's embedding endpoint
  try {
    const response = await fetch('http://localhost:11434/api/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'nomic-embed-text',
        prompt: text
      })
    });
    
    if (!response.ok) {
      throw new Error(`Failed to generate embedding: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    // Fall back to a simpler embedding method
    return generateSimpleEmbedding(text);
  }
}

function generateSimpleEmbedding(text: string): number[] {
  // A very simple embedding method for fallback
  // This is not suitable for production use
  const embedding = new Array(384).fill(0);
  const words = text.toLowerCase().split(/\W+/).filter(Boolean);
  
  for (const word of words) {
    const hash = simpleHash(word);
    embedding[hash % embedding.length] += 1;
  }
  
  // Normalize the embedding
  const norm = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
  return embedding.map(val => val / (norm || 1));
}

function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = ((hash << 5) - hash) + str.charCodeAt(i);
    hash |= 0; // Convert to 32bit integer
  }
  return Math.abs(hash);
}
```

### Batching

Batch embedding requests for better performance:

```typescript
async function batchGenerateEmbeddings(texts: string[]): Promise<number[][]> {
  const batchSize = 10;
  const embeddings: number[][] = [];
  
  for (let i = 0; i < texts.length; i += batchSize) {
    const batch = texts.slice(i, i + batchSize);
    const batchEmbeddings = await Promise.all(batch.map(text => generateEmbedding(text)));
    embeddings.push(...batchEmbeddings);
  }
  
  return embeddings;
}
```

## Vector Store

### In-Memory Vector Store

For small to medium-sized codebases, an in-memory vector store is sufficient:

```typescript
interface VectorStoreEntry {
  id: string;
  text: string;
  embedding: number[];
  metadata: ChunkMetadata;
}

class InMemoryVectorStore {
  private entries: VectorStoreEntry[] = [];
  
  add(id: string, text: string, embedding: number[], metadata: ChunkMetadata): void {
    this.entries.push({ id, text, embedding, metadata });
  }
  
  addBatch(entries: Omit<VectorStoreEntry, 'id'>[]): void {
    const newEntries = entries.map((entry, index) => ({
      id: `${Date.now()}-${index}`,
      ...entry
    }));
    this.entries.push(...newEntries);
  }
  
  search(queryEmbedding: number[], limit: number = 5): VectorStoreEntry[] {
    // Calculate cosine similarity for each entry
    const similarities = this.entries.map(entry => ({
      entry,
      similarity: cosineSimilarity(queryEmbedding, entry.embedding)
    }));
    
    // Sort by similarity (descending) and take the top 'limit' entries
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(item => item.entry);
  }
  
  clear(): void {
    this.entries = [];
  }
  
  size(): number {
    return this.entries.length;
  }
}

function cosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same length');
  }
  
  let dotProduct = 0;
  let normA = 0;
  let normB = 0;
  
  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }
  
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}
```

### Persistent Vector Store

For larger codebases or to persist embeddings between sessions:

```typescript
class PersistentVectorStore extends InMemoryVectorStore {
  private storagePath: string;
  
  constructor(storagePath: string) {
    super();
    this.storagePath = storagePath;
  }
  
  async load(): Promise<void> {
    try {
      const data = await fs.readFile(this.storagePath, 'utf-8');
      const entries = JSON.parse(data);
      this.entries = entries;
    } catch (error) {
      console.warn('Failed to load vector store:', error);
      this.entries = [];
    }
  }
  
  async save(): Promise<void> {
    try {
      await fs.writeFile(this.storagePath, JSON.stringify(this.entries), 'utf-8');
    } catch (error) {
      console.error('Failed to save vector store:', error);
    }
  }
}
```

## Query Processing

### Query Enhancement

Enhance user queries to improve retrieval:

```typescript
function enhanceQuery(query: string, context: any): string {
  // Add context-specific terms to the query
  let enhancedQuery = query;
  
  if (context.language) {
    enhancedQuery += ` ${context.language}`;
  }
  
  if (context.currentFile) {
    // Extract relevant terms from the current file
    const relevantTerms = extractRelevantTerms(context.currentFile);
    enhancedQuery += ` ${relevantTerms.join(' ')}`;
  }
  
  return enhancedQuery;
}

function extractRelevantTerms(filePath: string): string[] {
  // Extract function names, class names, etc. from the file
  // This is a simplified example
  const content = fs.readFileSync(filePath, 'utf-8');
  const terms: string[] = [];
  
  // Extract function names
  const functionMatches = content.matchAll(/function\s+(\w+)/g);
  for (const match of functionMatches) {
    terms.push(match[1]);
  }
  
  // Extract class names
  const classMatches = content.matchAll(/class\s+(\w+)/g);
  for (const match of classMatches) {
    terms.push(match[1]);
  }
  
  return terms;
}
```

### Hybrid Search

Combine embedding-based search with keyword search for better results:

```typescript
function hybridSearch(query: string, vectorStore: InMemoryVectorStore, limit: number = 5): VectorStoreEntry[] {
  // Generate embedding for the query
  const queryEmbedding = await generateEmbedding(query);
  
  // Perform embedding-based search
  const embeddingResults = vectorStore.search(queryEmbedding, limit * 2);
  
  // Perform keyword search
  const keywords = query.toLowerCase().split(/\W+/).filter(Boolean);
  const keywordResults = keywordSearch(keywords, vectorStore.getAll(), limit * 2);
  
  // Combine and deduplicate results
  const combinedResults = [...embeddingResults];
  for (const result of keywordResults) {
    if (!combinedResults.some(r => r.id === result.id)) {
      combinedResults.push(result);
    }
  }
  
  // Sort by a combined score and take the top 'limit' entries
  return combinedResults
    .map(entry => ({
      entry,
      score: calculateCombinedScore(entry, query, queryEmbedding)
    }))
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.entry);
}

function keywordSearch(keywords: string[], entries: VectorStoreEntry[], limit: number): VectorStoreEntry[] {
  // Calculate keyword match score for each entry
  const scores = entries.map(entry => {
    const text = entry.text.toLowerCase();
    let score = 0;
    for (const keyword of keywords) {
      if (text.includes(keyword)) {
        score += 1;
      }
    }
    return { entry, score };
  });
  
  // Sort by score (descending) and take the top 'limit' entries
  return scores
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.entry);
}

function calculateCombinedScore(entry: VectorStoreEntry, query: string, queryEmbedding: number[]): number {
  const embeddingScore = cosineSimilarity(queryEmbedding, entry.embedding);
  
  const text = entry.text.toLowerCase();
  const keywords = query.toLowerCase().split(/\W+/).filter(Boolean);
  let keywordScore = 0;
  for (const keyword of keywords) {
    if (text.includes(keyword)) {
      keywordScore += 1;
    }
  }
  keywordScore /= keywords.length;
  
  // Combine scores (70% embedding, 30% keyword)
  return embeddingScore * 0.7 + keywordScore * 0.3;
}
```

## Context Assembly

### Token Budget Management

Manage the token budget to ensure the prompt fits within the LLM's context window:

```typescript
function assembleContext(query: string, retrievedChunks: VectorStoreEntry[], tokenBudget: number): string {
  let context = '';
  let usedTokens = 0;
  
  // Estimate tokens in the query and other parts of the prompt
  const queryTokens = estimateTokens(query);
  const promptOverheadTokens = 200; // Estimate for system prompt, instructions, etc.
  const availableTokens = tokenBudget - queryTokens - promptOverheadTokens;
  
  // Add chunks until we reach the token budget
  for (const chunk of retrievedChunks) {
    const chunkTokens = estimateTokens(chunk.text);
    if (usedTokens + chunkTokens > availableTokens) {
      break;
    }
    
    context += `\n\nFrom ${chunk.metadata.filePath} (${chunk.metadata.type}${chunk.metadata.name ? ` ${chunk.metadata.name}` : ''}):\n${chunk.text}`;
    usedTokens += chunkTokens;
  }
  
  return context;
}

function estimateTokens(text: string): number {
  // A simple token estimator (4 characters per token is a rough estimate)
  return Math.ceil(text.length / 4);
}
```

### Context Formatting

Format the context to help the LLM understand the structure:

```typescript
function formatContext(retrievedChunks: VectorStoreEntry[]): string {
  let formattedContext = '## Retrieved Context\n\n';
  
  for (const chunk of retrievedChunks) {
    formattedContext += `### ${chunk.metadata.filePath}\n`;
    formattedContext += `Type: ${chunk.metadata.type}${chunk.metadata.name ? ` (${chunk.metadata.name})` : ''}\n`;
    formattedContext += `Lines: ${chunk.metadata.startLine}-${chunk.metadata.endLine}\n\n`;
    formattedContext += '```' + chunk.metadata.language + '\n';
    formattedContext += chunk.text + '\n';
    formattedContext += '```\n\n';
  }
  
  return formattedContext;
}
```

## Latest Developments (May 2025)

- **Hybrid RAG**: Combining embedding-based and keyword-based search
- **Local Embedding Models**: More efficient local embedding models
- **Chunking Strategies**: Advanced chunking strategies for different file types
- **Token-Aware Context Assembly**: Better management of token budgets

## References

- [VS Code Extension API Documentation](https://code.visualstudio.com/api/references/vscode-api) (May 2025)
- [Ollama API Documentation](https://github.com/ollama/ollama/blob/main/docs/api.md) (May 2025)
- [Tree-sitter Documentation](https://tree-sitter.github.io/tree-sitter/) (May 2025)

## Timestamp

Last updated: May 19, 2025
