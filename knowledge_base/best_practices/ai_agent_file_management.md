# AI Agent File Management Best Practices

## Overview

This document outlines best practices for managing files and content in X10sion's AI agent system. Proper file management is crucial for maintaining consistency, preventing duplication, and ensuring that changes are incremental and traceable.

## File Creation and Modification

### 1. File Existence Verification

Before creating a new file, AI agents should:

```typescript
// Example: File existence check before creation
async function createFileIfNotExists(filePath: string, content: string): Promise<boolean> {
  const fs = require('fs').promises;
  const path = require('path');
  
  try {
    // Check if file exists
    await fs.access(filePath);
    console.log(`File ${filePath} already exists. Consider updating instead.`);
    return false;
  } catch (error) {
    // File doesn't exist, create it
    try {
      // Ensure directory exists
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      // Create file
      await fs.writeFile(filePath, content);
      console.log(`File ${filePath} created successfully.`);
      return true;
    } catch (createError) {
      console.error(`Error creating file ${filePath}:`, createError);
      throw createError;
    }
  }
}
```

### 2. Similar File Detection

Implement similarity detection to identify files with similar purposes:

```typescript
// Example: Check for similar files
async function findSimilarFiles(filePath: string, workspace: string): Promise<string[]> {
  const fs = require('fs').promises;
  const path = require('path');
  const glob = require('glob');
  
  const fileName = path.basename(filePath);
  const fileExt = path.extname(filePath);
  const similarFiles = [];
  
  // Find files with similar names
  const files = glob.sync(`**/*${fileExt}`, { cwd: workspace });
  
  for (const file of files) {
    const currentFileName = path.basename(file);
    // Simple similarity check based on filename
    if (currentFileName !== fileName && 
        (currentFileName.includes(fileName.substring(0, fileName.length - fileExt.length)) || 
         fileName.includes(currentFileName.substring(0, currentFileName.length - fileExt.length)))) {
      similarFiles.push(file);
    }
  }
  
  return similarFiles;
}
```

### 3. Content Similarity Analysis

Implement content similarity analysis to detect duplicate content:

```typescript
// Example: Check content similarity
function calculateContentSimilarity(content1: string, content2: string): number {
  // Simple Jaccard similarity for demonstration
  const words1 = new Set(content1.toLowerCase().split(/\s+/));
  const words2 = new Set(content2.toLowerCase().split(/\s+/));
  
  const intersection = new Set([...words1].filter(word => words2.has(word)));
  const union = new Set([...words1, ...words2]);
  
  return intersection.size / union.size;
}
```

### 4. File Registry

Maintain a registry of all files created or modified by AI agents:

```typescript
// Example: File registry
interface FileRegistryEntry {
  path: string;
  lastModified: Date;
  creator: string;
  purpose: string;
  dependencies: string[];
}

class FileRegistry {
  private registry: Map<string, FileRegistryEntry> = new Map();
  private registryPath: string;
  
  constructor(registryPath: string) {
    this.registryPath = registryPath;
    this.loadRegistry();
  }
  
  private async loadRegistry() {
    try {
      const fs = require('fs').promises;
      const content = await fs.readFile(this.registryPath, 'utf8');
      const data = JSON.parse(content);
      this.registry = new Map(Object.entries(data));
    } catch (error) {
      console.log('Registry not found or invalid, creating new registry');
      this.registry = new Map();
    }
  }
  
  async saveRegistry() {
    try {
      const fs = require('fs').promises;
      const data = Object.fromEntries(this.registry);
      await fs.writeFile(this.registryPath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error saving registry:', error);
    }
  }
  
  registerFile(entry: FileRegistryEntry) {
    this.registry.set(entry.path, entry);
    this.saveRegistry();
  }
  
  getFileInfo(path: string): FileRegistryEntry | undefined {
    return this.registry.get(path);
  }
  
  getAllFiles(): FileRegistryEntry[] {
    return Array.from(this.registry.values());
  }
  
  getFilesByPurpose(purpose: string): FileRegistryEntry[] {
    return Array.from(this.registry.values())
      .filter(entry => entry.purpose === purpose);
  }
}
```

## Content Management

### 1. Incremental Updates

Prefer incremental updates over complete rewrites:

```typescript
// Example: Incremental file update
async function updateFileIncrementally(filePath: string, updater: (content: string) => string): Promise<void> {
  const fs = require('fs').promises;
  
  try {
    // Read existing content
    const content = await fs.readFile(filePath, 'utf8');
    
    // Apply incremental update
    const updatedContent = updater(content);
    
    // Write back only if changed
    if (content !== updatedContent) {
      await fs.writeFile(filePath, updatedContent);
      console.log(`File ${filePath} updated incrementally.`);
    } else {
      console.log(`No changes needed for ${filePath}.`);
    }
  } catch (error) {
    console.error(`Error updating file ${filePath}:`, error);
    throw error;
  }
}
```

### 2. Content Templates

Use templates for consistent file structure:

```typescript
// Example: Apply template for file creation
function applyTemplate(templateName: string, variables: Record<string, string>): string {
  const templates = {
    'typescript-class': `
/**
 * {{className}}
 * {{description}}
 * @created {{date}}
 */
export class {{className}} {
  constructor() {
    // TODO: Implement constructor
  }
  
  // TODO: Add methods
}
`,
    'markdown-doc': `
# {{title}}

## Overview

{{overview}}

## Details

// TODO: Add details

## References

// TODO: Add references
`
  };
  
  let content = templates[templateName] || '';
  
  // Replace variables
  for (const [key, value] of Object.entries(variables)) {
    content = content.replace(new RegExp(`{{${key}}}`, 'g'), value);
  }
  
  // Add date if not provided
  if (!variables['date']) {
    content = content.replace(/{{date}}/g, new Date().toISOString().split('T')[0]);
  }
  
  return content;
}
```

### 3. Content Validation

Implement validation rules for content:

```typescript
// Example: Content validation
interface ValidationRule {
  name: string;
  validate: (content: string) => boolean;
  message: string;
}

function validateContent(content: string, fileType: string): string[] {
  const rules: Record<string, ValidationRule[]> = {
    'typescript': [
      {
        name: 'hasExport',
        validate: (content) => /export\s+(class|function|const|interface|type)/.test(content),
        message: 'TypeScript files should export at least one symbol'
      },
      {
        name: 'hasDocumentation',
        validate: (content) => /\/\*\*[\s\S]*?\*\//.test(content),
        message: 'TypeScript files should include JSDoc documentation'
      }
    ],
    'markdown': [
      {
        name: 'hasTitle',
        validate: (content) => /^#\s+.+$/m.test(content),
        message: 'Markdown files should have a title (# Title)'
      },
      {
        name: 'hasStructure',
        validate: (content) => /^##\s+.+$/m.test(content),
        message: 'Markdown files should have at least one section (## Section)'
      }
    ]
  };
  
  const errors: string[] = [];
  const applicableRules = rules[fileType] || [];
  
  for (const rule of applicableRules) {
    if (!rule.validate(content)) {
      errors.push(rule.message);
    }
  }
  
  return errors;
}
```

## Terminal Output Monitoring

### 1. VS Code Terminal API

Use VS Code's Terminal API to monitor terminal output:

```typescript
// Example: Monitor terminal output
function monitorTerminalOutput(context: vscode.ExtensionContext) {
  // Listen for terminal creation
  vscode.window.onDidOpenTerminal(terminal => {
    console.log(`Terminal opened: ${terminal.name}`);
    
    // Listen for data written to the terminal
    const listener = terminal.onDidWriteData(data => {
      // Process terminal output
      processTerminalOutput(terminal.name, data);
    });
    
    // Store the listener to dispose when terminal closes
    context.subscriptions.push(listener);
  });
  
  // Listen for terminal closure
  vscode.window.onDidCloseTerminal(terminal => {
    console.log(`Terminal closed: ${terminal.name}`);
  });
}

function processTerminalOutput(terminalName: string, data: string) {
  // Look for error patterns
  if (data.includes('Error:') || data.includes('error:') || data.includes('ERROR:')) {
    // Log error for AI agent to analyze
    console.log(`Error detected in terminal ${terminalName}: ${data}`);
    // Notify AI agent about the error
    // ...
  }
  
  // Look for warning patterns
  if (data.includes('Warning:') || data.includes('warning:') || data.includes('WARNING:')) {
    // Log warning for AI agent to analyze
    console.log(`Warning detected in terminal ${terminalName}: ${data}`);
    // Notify AI agent about the warning
    // ...
  }
  
  // Store output for context
  // ...
}
```

### 2. Task Execution Monitoring

Monitor task execution and capture output:

```typescript
// Example: Execute task and monitor output
async function executeTaskWithMonitoring(taskName: string): Promise<string> {
  return new Promise((resolve, reject) => {
    let output = '';
    
    const task = vscode.tasks.executeTask(
      new vscode.Task(
        { type: 'shell' },
        vscode.TaskScope.Workspace,
        taskName,
        'X10sion',
        new vscode.ShellExecution(taskName)
      )
    );
    
    const disposable = vscode.tasks.onDidEndTaskProcess(e => {
      if (e.execution === task) {
        disposable.dispose();
        if (e.exitCode === 0) {
          resolve(output);
        } else {
          reject(new Error(`Task ${taskName} failed with exit code ${e.exitCode}`));
        }
      }
    });
    
    // This is a simplified example - in reality, capturing output requires
    // using the terminal API as shown in the previous example
  });
}
```

## Change Tracking

### 1. Automatic Timestamping

Add timestamps to all changes:

```typescript
// Example: Add timestamp to file content
function addTimestampToContent(content: string, fileType: string): string {
  const timestamp = new Date().toISOString();
  
  switch (fileType) {
    case 'typescript':
    case 'javascript':
      if (content.startsWith('/**')) {
        // Update existing JSDoc
        return content.replace(
          /\/\*\*[\s\S]*?\*\//,
          match => match.includes('@lastModified')
            ? match.replace(/@lastModified.*$/m, `@lastModified ${timestamp}`)
            : match.replace(/\*\//, ` * @lastModified ${timestamp}\n */`)
        );
      } else {
        // Add new JSDoc
        return `/**\n * @lastModified ${timestamp}\n */\n${content}`;
      }
    
    case 'markdown':
      if (content.includes('Last updated:')) {
        // Update existing timestamp
        return content.replace(
          /Last updated:.*$/m,
          `Last updated: ${timestamp}`
        );
      } else {
        // Add new timestamp at the end
        return `${content}\n\n---\nLast updated: ${timestamp}`;
      }
    
    default:
      return content;
  }
}
```

### 2. Change Logs

Maintain detailed change logs:

```typescript
// Example: Update changelog
async function updateChangelog(filePath: string, change: string): Promise<void> {
  const fs = require('fs').promises;
  const path = require('path');
  
  const changelogPath = path.join(path.dirname(filePath), 'CHANGELOG.md');
  const timestamp = new Date().toISOString();
  const entry = `\n## ${timestamp}\n\n- ${change}\n`;
  
  try {
    let content = '';
    
    try {
      content = await fs.readFile(changelogPath, 'utf8');
    } catch (error) {
      // Changelog doesn't exist, create new one
      content = '# Changelog\n';
    }
    
    // Add new entry at the top (after the title)
    content = content.replace(/# Changelog\n/, `# Changelog\n${entry}`);
    
    await fs.writeFile(changelogPath, content);
    console.log(`Changelog updated for ${filePath}`);
  } catch (error) {
    console.error(`Error updating changelog for ${filePath}:`, error);
  }
}
```

## References

- [VS Code API Documentation](https://code.visualstudio.com/api/references/vscode-api)
- [VS Code FileSystemWatcher API](https://code.visualstudio.com/api/references/vscode-api#FileSystemWatcher)
- [VS Code Terminal API](https://code.visualstudio.com/api/references/vscode-api#Terminal)
- [VS Code Task API](https://code.visualstudio.com/api/references/vscode-api#Task)
