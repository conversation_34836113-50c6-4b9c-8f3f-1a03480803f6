# Source of Truth Documentation

## Overview

This document outlines the source of truth documentation for the X10sion project. These documents serve as the authoritative references for the project's architecture, development guidelines, and best practices.

## Core Documentation

### README.md

- **Purpose**: Provides an overview of the project, its vision, features, and development guide
- **Location**: Root directory
- **Audience**: All developers, users, and contributors
- **Update Frequency**: As needed when major features or changes are introduced

### dirStructure.md

- **Purpose**: Documents the directory structure of the project
- **Location**: Root directory
- **Audience**: Developers and contributors
- **Update Frequency**: Automatically updated by background workers when file structure changes
- **Automation**: Real-time background workers update this file with timestamps

### fileRelations.md

- **Purpose**: Documents the relationships between files in the project
- **Location**: Root directory
- **Audience**: Developers and contributors
- **Update Frequency**: Automatically updated by background workers when file relationships change
- **Automation**: Real-time background workers update this file with timestamps

### testMethod.md

- **Purpose**: Documents the testing methodology for the project
- **Location**: Root directory
- **Audience**: Developers and contributors
- **Update Frequency**: Updated when testing methodology changes
- **Special Notes**: Should be updated when new components are added to the project

## Development Guides

### docs/dev_guide/development_phases.md

- **Purpose**: Provides an overview of all development phases
- **Location**: docs/dev_guide/
- **Audience**: Developers and project managers
- **Update Frequency**: Updated when development phases change or are completed

### docs/dev_guide/devPhaseX/devPhaseX.md

- **Purpose**: Provides an overview of a specific development phase
- **Location**: docs/dev_guide/devPhaseX/
- **Audience**: Developers working on that phase
- **Update Frequency**: Updated when phase scope or goals change
- **Special Notes**: Should include references to knowledge_base files

### docs/dev_guide/devPhaseX/devPhaseX_tasks.md

- **Purpose**: Documents detailed tasks for a specific development phase
- **Location**: docs/dev_guide/devPhaseX/
- **Audience**: Developers working on that phase
- **Update Frequency**: Updated when tasks are added, modified, or completed
- **Special Notes**: Tasks should be broken down into very small steps for 8GB VRAM LLMs with 4K context windows

## Knowledge Base

### knowledge_base/architecture/*.md

- **Purpose**: Documents the architecture of the project
- **Location**: knowledge_base/architecture/
- **Audience**: Developers and architects
- **Update Frequency**: Updated when architecture changes
- **Special Notes**: Should be referenced in development phase documentation

### knowledge_base/best_practices/*.md

- **Purpose**: Documents best practices for various aspects of the project
- **Location**: knowledge_base/best_practices/
- **Audience**: All developers
- **Update Frequency**: Updated when best practices change or new ones are introduced
- **Special Notes**: Should be referenced in development phase documentation

## Guidelines

### x10sion_general_guidelines.md

- **Purpose**: Documents general AI assistant guidelines
- **Location**: Root directory
- **Audience**: AI assistants and developers
- **Update Frequency**: Updated when general guidelines change

### x10sion_project_guidelines.md

- **Purpose**: Documents project-specific guidelines
- **Location**: Root directory
- **Audience**: AI assistants and developers
- **Update Frequency**: Updated when project guidelines change

## Test Results

### testResults/phaseX/phaseX_taskY_testResult.md

- **Purpose**: Documents test results for a specific task in a specific phase
- **Location**: testResults/phaseX/
- **Audience**: Developers and quality assurance
- **Update Frequency**: Updated when tests are run for that task
- **Special Notes**: Should include test environment, test summary, and timestamp

### testResults/phaseX/phaseX_full_testResult.md

- **Purpose**: Documents full test results for a specific phase
- **Location**: testResults/phaseX/
- **Audience**: Developers and quality assurance
- **Update Frequency**: Updated when all tests for that phase are run
- **Special Notes**: Should include test environment, test summary, and timestamp

## Maintenance and Updates

- All documentation should reference May 2025 sources
- Documentation should be updated to reflect the current state of the project
- Duplicate or outdated information should be removed
- Documentation should be written for 8GB VRAM LLMs with 4K context windows
- Real-time background workers should automatically update dirStructure.md and fileRelations.md

## Conclusion

Following these guidelines for source of truth documentation will ensure that all team members have access to accurate and up-to-date information about the project. This will improve development efficiency, reduce errors, and make it easier for new team members to onboard.
