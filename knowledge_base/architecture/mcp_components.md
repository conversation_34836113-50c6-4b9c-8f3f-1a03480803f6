# MCP Components Guide (May 2025)

This document outlines the implementation details for Model Context Protocol (MCP) components in the X10sion project, based on the latest information as of May 2025.

## Overview

The Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM applications and external data sources and tools. X10sion implements MCP to provide a standardized way for AI models to interact with context providers, making it easier to build AI-powered applications that can access and use external information.

## MCP Component Types

X10sion implements four main types of MCP components:

1. **Agents**: AI agents that perform specific tasks
2. **Tools**: Functions that agents can use to interact with the environment
3. **Resources**: Data sources that provide context to agents
4. **Prompts**: Templates for generating text

## Agents

Agents are AI-powered components that can perform specific tasks. They can use tools, resources, and prompts to accomplish their goals.

### Agent Structure

```typescript
interface Agent {
    id: string;
    name: string;
    description: string;
    version: string;
    author: string;
    capabilities: string[];
    requiredTools: string[];
    requiredResources: string[];
    requiredPrompts: string[];
    execute(request: AgentRequest): Promise<AgentResponse>;
}

interface AgentRequest {
    task: string;
    context?: any;
    params?: any;
}

interface AgentResponse {
    result: any;
    error?: string;
    metadata?: any;
}
```

### Agent Implementation

```typescript
class CodeReviewAgent implements Agent {
    id = 'code-review-agent';
    name = 'Code Review Agent';
    description = 'Reviews code for quality and issues';
    version = '1.0.0';
    author = 'X10sion Team';
    capabilities = ['code-review', 'code-quality', 'bug-detection'];
    requiredTools = ['code-analysis-tool'];
    requiredResources = ['file-system-resource'];
    requiredPrompts = ['code-review-prompt'];

    constructor(
        private toolRegistry: ToolRegistry,
        private resourceRegistry: ResourceRegistry,
        private promptRegistry: PromptRegistry,
        private llm: LanguageModel
    ) {}

    async execute(request: AgentRequest): Promise<AgentResponse> {
        try {
            // Get required tools, resources, and prompts
            const codeAnalysisTool = this.toolRegistry.getTool('code-analysis-tool');
            const fileSystemResource = this.resourceRegistry.getResource('file-system-resource');
            const codeReviewPrompt = this.promptRegistry.getPrompt('code-review-prompt');

            // Get file content
            const filePath = request.params?.filePath;
            if (!filePath) {
                return {
                    result: null,
                    error: 'File path is required'
                };
            }

            const fileContent = await fileSystemResource.get({ path: filePath });

            // Analyze code
            const codeAnalysis = await codeAnalysisTool.execute({ code: fileContent });

            // Generate prompt
            const prompt = await codeReviewPrompt.generate({
                code: fileContent,
                analysis: codeAnalysis
            });

            // Generate review using LLM
            const review = await this.llm.generate(prompt);

            return {
                result: review,
                metadata: {
                    filePath,
                    analysisResults: codeAnalysis
                }
            };
        } catch (error) {
            return {
                result: null,
                error: error.message
            };
        }
    }
}
```

## Tools

Tools are functions that agents can use to interact with the environment. They provide specific capabilities like code analysis, file operations, or web access.

### Tool Structure

```typescript
interface Tool {
    id: string;
    name: string;
    description: string;
    version: string;
    author: string;
    inputSchema: any; // JSON Schema
    outputSchema: any; // JSON Schema
    execute(params: any): Promise<any>;
}
```

### Tool Implementation

```typescript
class CodeAnalysisTool implements Tool {
    id = 'code-analysis-tool';
    name = 'Code Analysis Tool';
    description = 'Analyzes code for quality and issues';
    version = '1.0.0';
    author = 'X10sion Team';
    inputSchema = {
        type: 'object',
        properties: {
            code: { type: 'string' },
            language: { type: 'string' }
        },
        required: ['code']
    };
    outputSchema = {
        type: 'object',
        properties: {
            complexity: { type: 'number' },
            issues: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        line: { type: 'number' },
                        message: { type: 'string' },
                        severity: { type: 'string' }
                    }
                }
            }
        }
    };

    async execute(params: any): Promise<any> {
        const { code, language = 'typescript' } = params;

        // Analyze code complexity
        const complexity = this.analyzeComplexity(code);

        // Analyze code issues
        const issues = this.analyzeIssues(code, language);

        return {
            complexity,
            issues
        };
    }

    private analyzeComplexity(code: string): number {
        // Calculate cyclomatic complexity
        // This is a simplified implementation
        const functionCount = (code.match(/function/g) || []).length;
        const ifCount = (code.match(/if/g) || []).length;
        const forCount = (code.match(/for/g) || []).length;
        const whileCount = (code.match(/while/g) || []).length;
        const switchCount = (code.match(/switch/g) || []).length;

        return 1 + ifCount + forCount + whileCount + switchCount;
    }

    private analyzeIssues(code: string, language: string): any[] {
        // Analyze code issues
        // This is a simplified implementation
        const issues = [];

        // Check for console.log statements
        const consoleLogMatches = code.match(/console\.log\(/g) || [];
        for (const match of consoleLogMatches) {
            issues.push({
                line: this.findLineNumber(code, match),
                message: 'Avoid using console.log in production code',
                severity: 'warning'
            });
        }

        // Check for TODO comments
        const todoMatches = code.match(/\/\/\s*TODO/g) || [];
        for (const match of todoMatches) {
            issues.push({
                line: this.findLineNumber(code, match),
                message: 'TODO comment found',
                severity: 'info'
            });
        }

        return issues;
    }

    private findLineNumber(code: string, substring: string): number {
        const lines = code.split('\n');
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes(substring)) {
                return i + 1;
            }
        }
        return 0;
    }
}
```

## Resources

Resources are data sources that provide context to agents. They can be files, databases, APIs, or any other source of information.

### Resource Structure

```typescript
interface Resource {
    id: string;
    name: string;
    description: string;
    version: string;
    author: string;
    schema: any; // JSON Schema
    get(params: any): Promise<any>;
}
```

### Resource Implementation

```typescript
class FileSystemResource implements Resource {
    id = 'file-system-resource';
    name = 'File System Resource';
    description = 'Provides access to the file system';
    version = '1.0.0';
    author = 'X10sion Team';
    schema = {
        type: 'object',
        properties: {
            path: { type: 'string' }
        },
        required: ['path']
    };

    async get(params: any): Promise<any> {
        const { path } = params;

        // Check if file exists
        if (!fs.existsSync(path)) {
            throw new Error(`File not found: ${path}`);
        }

        // Read file content
        const content = await fs.promises.readFile(path, 'utf-8');

        // Determine file type
        const fileType = this.getFileType(path);

        return {
            path,
            content,
            type: fileType
        };
    }

    private getFileType(path: string): string {
        const extension = path.split('.').pop()?.toLowerCase();
        switch (extension) {
            case 'ts':
            case 'tsx':
                return 'typescript';
            case 'js':
            case 'jsx':
                return 'javascript';
            case 'py':
                return 'python';
            case 'java':
                return 'java';
            case 'c':
                return 'c';
            case 'cpp':
                return 'cpp';
            case 'cs':
                return 'csharp';
            case 'go':
                return 'go';
            case 'rb':
                return 'ruby';
            case 'php':
                return 'php';
            case 'html':
                return 'html';
            case 'css':
                return 'css';
            case 'json':
                return 'json';
            case 'md':
                return 'markdown';
            default:
                return 'text';
        }
    }
}
```

## Prompts

Prompts are templates for generating text. They provide a structured way to create prompts for language models.

### Prompt Structure

```typescript
interface Prompt {
    id: string;
    name: string;
    description: string;
    version: string;
    author: string;
    schema: any; // JSON Schema
    generate(params: any): Promise<string>;
}
```

### Prompt Implementation

```typescript
class CodeReviewPrompt implements Prompt {
    id = 'code-review-prompt';
    name = 'Code Review Prompt';
    description = 'Generates a prompt for code review';
    version = '1.0.0';
    author = 'X10sion Team';
    schema = {
        type: 'object',
        properties: {
            code: { type: 'string' },
            analysis: {
                type: 'object',
                properties: {
                    complexity: { type: 'number' },
                    issues: { type: 'array' }
                }
            }
        },
        required: ['code']
    };

    async generate(params: any): Promise<string> {
        const { code, analysis } = params;

        // Generate prompt
        let prompt = `
You are a code reviewer. Please review the following code and provide feedback.

CODE:
\`\`\`
${code}
\`\`\`
`;

        // Add analysis if available
        if (analysis) {
            prompt += `
ANALYSIS:
- Complexity: ${analysis.complexity}
- Issues: ${analysis.issues.length}
`;

            if (analysis.issues.length > 0) {
                prompt += `
ISSUES:
${analysis.issues.map(issue => `- Line ${issue.line}: ${issue.message} (${issue.severity})`).join('\n')}
`;
            }
        }

        prompt += `
Please provide a comprehensive review of the code, including:
1. Code quality assessment
2. Potential bugs or issues
3. Suggestions for improvement
4. Best practices that should be followed

Your review should be constructive and actionable.
`;

        return prompt;
    }
}
```

## Component Registry

X10sion uses registries to manage MCP components. Each component type has its own registry.

### Registry Implementation

```typescript
class ComponentRegistry<T extends { id: string }> {
    private components: Map<string, T> = new Map();

    register(component: T): void {
        this.components.set(component.id, component);
    }

    unregister(id: string): void {
        this.components.delete(id);
    }

    get(id: string): T {
        const component = this.components.get(id);
        if (!component) {
            throw new Error(`Component not found: ${id}`);
        }
        return component;
    }

    getAll(): T[] {
        return Array.from(this.components.values());
    }
}

class AgentRegistry extends ComponentRegistry<Agent> {}
class ToolRegistry extends ComponentRegistry<Tool> {}
class ResourceRegistry extends ComponentRegistry<Resource> {}
class PromptRegistry extends ComponentRegistry<Prompt> {}
```

## MCP Server

X10sion implements an MCP server that provides access to all registered components.

### Server Implementation

```typescript
class MCPServer {
    private agentRegistry: AgentRegistry;
    private toolRegistry: ToolRegistry;
    private resourceRegistry: ResourceRegistry;
    private promptRegistry: PromptRegistry;

    constructor() {
        this.agentRegistry = new AgentRegistry();
        this.toolRegistry = new ToolRegistry();
        this.resourceRegistry = new ResourceRegistry();
        this.promptRegistry = new PromptRegistry();
    }

    registerAgent(agent: Agent): void {
        this.agentRegistry.register(agent);
    }

    registerTool(tool: Tool): void {
        this.toolRegistry.register(tool);
    }

    registerResource(resource: Resource): void {
        this.resourceRegistry.register(resource);
    }

    registerPrompt(prompt: Prompt): void {
        this.promptRegistry.register(prompt);
    }

    async executeAgent(id: string, request: AgentRequest): Promise<AgentResponse> {
        const agent = this.agentRegistry.get(id);
        return await agent.execute(request);
    }

    async executeTool(id: string, params: any): Promise<any> {
        const tool = this.toolRegistry.get(id);
        return await tool.execute(params);
    }

    async getResource(id: string, params: any): Promise<any> {
        const resource = this.resourceRegistry.get(id);
        return await resource.get(params);
    }

    async generatePrompt(id: string, params: any): Promise<string> {
        const prompt = this.promptRegistry.get(id);
        return await prompt.generate(params);
    }
}
```

## Timestamp

Last updated: May 19, 2025
