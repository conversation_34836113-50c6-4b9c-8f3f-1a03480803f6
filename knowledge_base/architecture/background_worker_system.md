# Background Worker System

## Overview

The Background Worker System in X10sion provides a resource-efficient way to perform tasks in the background without impacting the main extension performance. It's designed to handle documentation maintenance, file management, and other resource-intensive tasks while minimizing the impact on the user's system.

## Design Principles

### 1. Just-in-Time Initialization

Workers are initialized only when needed, reducing the memory footprint when idle:

```typescript
// Example: Just-in-Time Worker Initialization
class JustInTimeWorker {
  private worker: Worker | null = null;
  private isInitializing: boolean = false;
  private initPromise: Promise<Worker> | null = null;
  private lastUsed: number = 0;
  private readonly idleTimeout: number = 60000; // 1 minute
  
  constructor(private readonly scriptPath: string) {}
  
  public async getWorker(): Promise<Worker> {
    // If worker exists and is active, return it
    if (this.worker) {
      this.lastUsed = Date.now();
      return this.worker;
    }
    
    // If initialization is in progress, wait for it
    if (this.isInitializing && this.initPromise) {
      return this.initPromise;
    }
    
    // Initialize worker
    this.isInitializing = true;
    this.initPromise = new Promise((resolve, reject) => {
      try {
        const { Worker } = require('worker_threads');
        const worker = new Worker(this.scriptPath);
        
        worker.on('online', () => {
          this.worker = worker;
          this.lastUsed = Date.now();
          this.isInitializing = false;
          resolve(worker);
        });
        
        worker.on('error', (err) => {
          this.isInitializing = false;
          this.worker = null;
          reject(err);
        });
      } catch (err) {
        this.isInitializing = false;
        reject(err);
      }
    });
    
    return this.initPromise;
  }
  
  public checkIdle(): boolean {
    if (!this.worker) return true;
    
    const idleTime = Date.now() - this.lastUsed;
    if (idleTime > this.idleTimeout) {
      this.terminate();
      return true;
    }
    
    return false;
  }
  
  public terminate(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    this.initPromise = null;
  }
}
```

### 2. Task Prioritization

Tasks are prioritized based on importance and resource availability:

```typescript
// Example: Task Prioritization
enum TaskPriority {
  LOW = 0,
  MEDIUM = 1,
  HIGH = 2,
  CRITICAL = 3
}

interface Task {
  id: string;
  type: string;
  data: any;
  priority: TaskPriority;
  createdAt: number;
}

class TaskScheduler {
  private tasks: Task[] = [];
  private isProcessing: boolean = false;
  
  public addTask(task: Task): void {
    this.tasks.push(task);
    this.tasks.sort((a, b) => b.priority - a.priority);
    
    if (!this.isProcessing) {
      this.processNextTask();
    }
  }
  
  private async processNextTask(): Promise<void> {
    if (this.tasks.length === 0) {
      this.isProcessing = false;
      return;
    }
    
    this.isProcessing = true;
    const task = this.tasks.shift();
    
    try {
      // Process task based on type
      await this.executeTask(task);
    } catch (error) {
      console.error(`Error processing task ${task.id}:`, error);
    }
    
    // Process next task
    this.processNextTask();
  }
  
  private async executeTask(task: Task): Promise<void> {
    // Implementation depends on task type
    switch (task.type) {
      case 'update-dir-structure':
        await this.updateDirStructure(task.data);
        break;
      case 'update-file-relations':
        await this.updateFileRelations(task.data);
        break;
      case 'check-file-existence':
        await this.checkFileExistence(task.data);
        break;
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }
  
  // Task implementations...
  private async updateDirStructure(data: any): Promise<void> {
    // Implementation
  }
  
  private async updateFileRelations(data: any): Promise<void> {
    // Implementation
  }
  
  private async checkFileExistence(data: any): Promise<void> {
    // Implementation
  }
}
```

### 3. Resource Monitoring

The system monitors resource usage and adjusts its behavior accordingly:

```typescript
// Example: Resource Monitoring
class ResourceMonitor {
  private cpuUsage: number = 0;
  private memoryUsage: number = 0;
  private readonly checkInterval: number = 5000; // 5 seconds
  private intervalId: NodeJS.Timeout | null = null;
  
  public start(): void {
    if (this.intervalId) return;
    
    this.intervalId = setInterval(() => {
      this.updateResourceUsage();
    }, this.checkInterval);
  }
  
  public stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
  
  private updateResourceUsage(): void {
    const os = require('os');
    
    // Calculate CPU usage
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }
    
    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    this.cpuUsage = 100 - (idle / total * 100);
    
    // Calculate memory usage
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    this.memoryUsage = 100 - (freeMem / totalMem * 100);
    
    // Emit event if resources are constrained
    if (this.cpuUsage > 80 || this.memoryUsage > 80) {
      this.emit('resource-constrained', {
        cpuUsage: this.cpuUsage,
        memoryUsage: this.memoryUsage
      });
    }
  }
  
  public shouldDeferTask(priority: TaskPriority): boolean {
    // Defer low priority tasks if resources are constrained
    if (priority === TaskPriority.LOW && (this.cpuUsage > 70 || this.memoryUsage > 70)) {
      return true;
    }
    
    // Defer medium priority tasks if resources are highly constrained
    if (priority === TaskPriority.MEDIUM && (this.cpuUsage > 85 || this.memoryUsage > 85)) {
      return true;
    }
    
    return false;
  }
  
  // Event emitter implementation...
  private emit(event: string, data: any): void {
    // Implementation
  }
}
```

## Core Components

### 1. Background Worker Manager

The Background Worker Manager coordinates all background workers and tasks:

```typescript
// Example: Background Worker Manager
class BackgroundWorkerManager {
  private workers: Map<string, JustInTimeWorker> = new Map();
  private taskScheduler: TaskScheduler;
  private resourceMonitor: ResourceMonitor;
  private isEnabled: boolean = true;
  
  constructor() {
    this.taskScheduler = new TaskScheduler();
    this.resourceMonitor = new ResourceMonitor();
    this.resourceMonitor.start();
  }
  
  public scheduleTask(type: string, data: any, priority: TaskPriority = TaskPriority.MEDIUM): string {
    if (!this.isEnabled) return null;
    
    // Check if task should be deferred due to resource constraints
    if (this.resourceMonitor.shouldDeferTask(priority)) {
      console.log(`Deferring task ${type} due to resource constraints`);
      setTimeout(() => {
        this.scheduleTask(type, data, priority);
      }, 30000); // Retry after 30 seconds
      return null;
    }
    
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.taskScheduler.addTask({
      id: taskId,
      type,
      data,
      priority,
      createdAt: Date.now()
    });
    
    return taskId;
  }
  
  public getWorker(type: string): Promise<Worker> {
    if (!this.workers.has(type)) {
      const scriptPath = this.getWorkerScriptPath(type);
      this.workers.set(type, new JustInTimeWorker(scriptPath));
    }
    
    return this.workers.get(type).getWorker();
  }
  
  private getWorkerScriptPath(type: string): string {
    // Map worker type to script path
    const workerScripts = {
      'documentation': './out/workers/documentation-worker.js',
      'file-management': './out/workers/file-management-worker.js',
      'analysis': './out/workers/analysis-worker.js'
    };
    
    return workerScripts[type] || workerScripts['documentation'];
  }
  
  public checkIdleWorkers(): void {
    for (const [type, worker] of this.workers.entries()) {
      worker.checkIdle();
    }
  }
  
  public enable(): void {
    this.isEnabled = true;
    this.resourceMonitor.start();
  }
  
  public disable(): void {
    this.isEnabled = false;
    this.resourceMonitor.stop();
    
    // Terminate all workers
    for (const [type, worker] of this.workers.entries()) {
      worker.terminate();
    }
    
    this.workers.clear();
  }
}
```

### 2. Documentation Worker

The Documentation Worker maintains `dirStructure.md` and `fileRelations.md`:

```typescript
// Example: Documentation Worker (documentation-worker.js)
const { parentPort, workerData } = require('worker_threads');
const fs = require('fs').promises;
const path = require('path');
const glob = require('glob');

// Handle messages from main thread
parentPort.on('message', async (message) => {
  if (message.type === 'update-dir-structure') {
    try {
      const result = await updateDirStructure(message.workspacePath);
      parentPort.postMessage({
        type: 'task-complete',
        taskId: message.taskId,
        result
      });
    } catch (error) {
      parentPort.postMessage({
        type: 'task-error',
        taskId: message.taskId,
        error: {
          message: error.message,
          stack: error.stack
        }
      });
    }
  } else if (message.type === 'update-file-relations') {
    try {
      const result = await updateFileRelations(message.workspacePath);
      parentPort.postMessage({
        type: 'task-complete',
        taskId: message.taskId,
        result
      });
    } catch (error) {
      parentPort.postMessage({
        type: 'task-error',
        taskId: message.taskId,
        error: {
          message: error.message,
          stack: error.stack
        }
      });
    }
  }
});

async function updateDirStructure(workspacePath) {
  // Implementation to scan directory structure and update dirStructure.md
  // with timestamp
}

async function updateFileRelations(workspacePath) {
  // Implementation to analyze file relationships and update fileRelations.md
  // with timestamp
}

// Send ready message
parentPort.postMessage({
  type: 'ready'
});
```

### 3. File Management Worker

The File Management Worker handles file existence checks and content similarity analysis:

```typescript
// Example: File Management Worker (file-management-worker.js)
const { parentPort, workerData } = require('worker_threads');
const fs = require('fs').promises;
const path = require('path');

// Handle messages from main thread
parentPort.on('message', async (message) => {
  if (message.type === 'check-file-existence') {
    try {
      const result = await checkFileExistence(
        message.filePath,
        message.workspacePath
      );
      parentPort.postMessage({
        type: 'task-complete',
        taskId: message.taskId,
        result
      });
    } catch (error) {
      parentPort.postMessage({
        type: 'task-error',
        taskId: message.taskId,
        error: {
          message: error.message,
          stack: error.stack
        }
      });
    }
  } else if (message.type === 'check-content-similarity') {
    try {
      const result = await checkContentSimilarity(
        message.content,
        message.filePath,
        message.workspacePath
      );
      parentPort.postMessage({
        type: 'task-complete',
        taskId: message.taskId,
        result
      });
    } catch (error) {
      parentPort.postMessage({
        type: 'task-error',
        taskId: message.taskId,
        error: {
          message: error.message,
          stack: error.stack
        }
      });
    }
  }
});

async function checkFileExistence(filePath, workspacePath) {
  // Implementation to check if file exists or if similar files exist
}

async function checkContentSimilarity(content, filePath, workspacePath) {
  // Implementation to check content similarity with existing files
}

// Send ready message
parentPort.postMessage({
  type: 'ready'
});
```

## Integration with VS Code Extension

The Background Worker System is integrated with the VS Code extension through the following components:

### 1. File System Watcher

```typescript
// Example: File System Watcher Integration
function setupFileSystemWatcher(context: vscode.ExtensionContext, workerManager: BackgroundWorkerManager): void {
  const watcher = vscode.workspace.createFileSystemWatcher('**/*');
  
  // When a file is created
  watcher.onDidCreate((uri) => {
    workerManager.scheduleTask('update-dir-structure', {
      workspacePath: vscode.workspace.rootPath
    }, TaskPriority.LOW);
    
    workerManager.scheduleTask('update-file-relations', {
      workspacePath: vscode.workspace.rootPath
    }, TaskPriority.LOW);
  });
  
  // When a file is deleted
  watcher.onDidDelete((uri) => {
    workerManager.scheduleTask('update-dir-structure', {
      workspacePath: vscode.workspace.rootPath
    }, TaskPriority.LOW);
    
    workerManager.scheduleTask('update-file-relations', {
      workspacePath: vscode.workspace.rootPath
    }, TaskPriority.LOW);
  });
  
  // Register disposable
  context.subscriptions.push(watcher);
}
```

### 2. Idle Time Detection

```typescript
// Example: Idle Time Detection
function setupIdleTimeDetection(context: vscode.ExtensionContext, workerManager: BackgroundWorkerManager): void {
  let lastActivity = Date.now();
  const idleCheckInterval = 60000; // 1 minute
  
  // Track user activity
  vscode.window.onDidChangeActiveTextEditor(() => {
    lastActivity = Date.now();
  });
  
  vscode.workspace.onDidChangeTextDocument(() => {
    lastActivity = Date.now();
  });
  
  // Check for idle time
  const intervalId = setInterval(() => {
    const idleTime = Date.now() - lastActivity;
    
    // If idle for more than 5 minutes, run documentation update
    if (idleTime > 300000) {
      workerManager.scheduleTask('update-dir-structure', {
        workspacePath: vscode.workspace.rootPath
      }, TaskPriority.MEDIUM);
      
      workerManager.scheduleTask('update-file-relations', {
        workspacePath: vscode.workspace.rootPath
      }, TaskPriority.MEDIUM);
    }
    
    // Check for idle workers
    workerManager.checkIdleWorkers();
  }, idleCheckInterval);
  
  // Register disposable
  context.subscriptions.push({
    dispose: () => clearInterval(intervalId)
  });
}
```

## References

- [VS Code API Documentation](https://code.visualstudio.com/api/references/vscode-api)
- [Node.js Worker Threads Documentation](https://nodejs.org/api/worker_threads.html)
- [VS Code Extension Performance Best Practices](https://code.visualstudio.com/api/advanced-topics/extension-host#performance-considerations)
