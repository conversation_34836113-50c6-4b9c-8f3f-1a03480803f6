# TypeScript Code Patterns

This document outlines common TypeScript code patterns that are useful for VS Code extension development.

## Type Definitions

### Interfaces vs. Types

TypeScript provides two ways to define custom types: interfaces and type aliases. Here's when to use each:

**Interfaces** are preferred when:
- You need to define an object shape
- You want to allow extension of the type
- You're defining a public API

```typescript
interface EditorContext {
  filePath: string | null;
  languageId: string | null;
  selectedText: string | null;
  fileContent: string | null;
}
```

**Type aliases** are preferred when:
- You need to define a union or intersection type
- You need to create a type based on another type
- You're creating a complex type that isn't just an object shape

```typescript
type Result<T> = { success: true, data: T } | { success: false, error: Error };
```

### Nullable Types

Use the union with `null` or `undefined` to indicate nullable types:

```typescript
function getSelectedText(): string | null {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return null;
  }
  return editor.document.getText(editor.selection);
}
```

## Async Patterns

### Async/Await

Use async/await for asynchronous operations:

```typescript
async function getFileContent(uri: vscode.Uri): Promise<string> {
  const content = await vscode.workspace.fs.readFile(uri);
  return new TextDecoder().decode(content);
}
```

### Promise Handling

Handle promises with proper error catching:

```typescript
async function fetchData(): Promise<Data | null> {
  try {
    const response = await fetch('https://api.example.com/data');
    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch data:', error);
    return null;
  }
}
```

## VS Code Extension Patterns

### Command Registration

Register commands in the `activate` function:

```typescript
export function activate(context: vscode.ExtensionContext) {
  const disposable = vscode.commands.registerCommand('extension.command', () => {
    // Command implementation
  });
  
  context.subscriptions.push(disposable);
}
```

### Disposables

Properly manage disposables to prevent memory leaks:

```typescript
export function activate(context: vscode.ExtensionContext) {
  // Create disposables
  const disposable1 = vscode.commands.registerCommand('extension.command1', () => {});
  const disposable2 = vscode.commands.registerCommand('extension.command2', () => {});
  
  // Add to subscriptions
  context.subscriptions.push(disposable1, disposable2);
}
```

### Configuration

Access extension configuration:

```typescript
function getConfiguration() {
  const config = vscode.workspace.getConfiguration('extension');
  const setting = config.get<string>('settingName');
  return setting;
}
```

## Functional Programming

### Array Methods

Use array methods for cleaner code:

```typescript
// Instead of:
const result = [];
for (const item of items) {
  if (item.condition) {
    result.push(item.value);
  }
}

// Use:
const result = items
  .filter(item => item.condition)
  .map(item => item.value);
```

### Optional Chaining and Nullish Coalescing

Use optional chaining and nullish coalescing for cleaner null handling:

```typescript
// Instead of:
const value = obj && obj.prop && obj.prop.value ? obj.prop.value : defaultValue;

// Use:
const value = obj?.prop?.value ?? defaultValue;
```

## Error Handling

### Try/Catch with Specific Types

Use try/catch with specific error types:

```typescript
try {
  await riskyOperation();
} catch (error) {
  if (error instanceof NetworkError) {
    // Handle network errors
  } else if (error instanceof ValidationError) {
    // Handle validation errors
  } else {
    // Handle other errors
    console.error('Unknown error:', error);
  }
}
```

### Error Objects

Create custom error classes for better error handling:

```typescript
class ApplicationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ApplicationError';
  }
}

class ValidationError extends ApplicationError {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}
```

## Module Patterns

### Barrel Exports

Use barrel exports to simplify imports:

```typescript
// In index.ts
export * from './module1';
export * from './module2';
export * from './module3';

// In another file
import { func1, func2, func3 } from './features';
```

### Dependency Injection

Use dependency injection for better testability:

```typescript
class Service {
  constructor(private dependency: Dependency) {}
  
  doSomething() {
    return this.dependency.method();
  }
}

// In production code:
const service = new Service(new RealDependency());

// In tests:
const service = new Service(new MockDependency());
```

## Design Patterns

### Singleton

Implement singletons for services that should have only one instance:

```typescript
export class LoggerService {
  private static instance: LoggerService;
  
  private constructor() {
    // Private constructor to prevent direct construction calls
  }
  
  public static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService();
    }
    
    return LoggerService.instance;
  }
  
  log(message: string) {
    console.log(message);
  }
}

// Usage:
const logger = LoggerService.getInstance();
logger.log('Message');
```

### Factory

Use factories to create objects:

```typescript
interface Product {
  operation(): string;
}

class ConcreteProduct1 implements Product {
  operation(): string {
    return 'Result of ConcreteProduct1';
  }
}

class ConcreteProduct2 implements Product {
  operation(): string {
    return 'Result of ConcreteProduct2';
  }
}

class ProductFactory {
  createProduct(type: 'type1' | 'type2'): Product {
    if (type === 'type1') {
      return new ConcreteProduct1();
    } else {
      return new ConcreteProduct2();
    }
  }
}
```
