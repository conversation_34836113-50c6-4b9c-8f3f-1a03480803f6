# JavaScript Code Patterns

This document outlines common JavaScript code patterns that are useful for VS Code extension development.

## Modern JavaScript Features

### Arrow Functions

Arrow functions provide a concise syntax for writing functions and lexically bind the `this` value:

```javascript
// Traditional function
function add(a, b) {
  return a + b;
}

// Arrow function
const add = (a, b) => a + b;

// Arrow function with block body
const add = (a, b) => {
  const result = a + b;
  return result;
};
```

### Destructuring

Destructuring allows you to extract values from objects and arrays:

```javascript
// Object destructuring
const { name, age } = person;

// Array destructuring
const [first, second] = items;

// With default values
const { name, age = 30 } = person;

// Nested destructuring
const { address: { city, country } } = person;
```

### Spread and Rest Operators

Spread and rest operators provide a concise way to work with arrays and objects:

```javascript
// Spread operator (arrays)
const newArray = [...oldArray, newItem];

// Spread operator (objects)
const newObject = { ...oldObject, newProperty: value };

// Rest operator (arrays)
const [first, ...rest] = items;

// Rest operator (objects)
const { name, ...rest } = person;
```

### Template Literals

Template literals provide a more readable way to concatenate strings:

```javascript
const name = 'John';
const greeting = `Hello, ${name}!`;

// Multi-line strings
const message = `
  This is a multi-line
  string that preserves
  line breaks.
`;
```

## Asynchronous Patterns

### Promises

Promises provide a cleaner way to handle asynchronous operations:

```javascript
// Creating a promise
const fetchData = () => {
  return new Promise((resolve, reject) => {
    // Asynchronous operation
    if (success) {
      resolve(data);
    } else {
      reject(error);
    }
  });
};

// Using a promise
fetchData()
  .then(data => {
    // Handle success
  })
  .catch(error => {
    // Handle error
  })
  .finally(() => {
    // Always executed
  });
```

### Async/Await

Async/await provides a more synchronous-looking way to write asynchronous code:

```javascript
// Async function
async function fetchData() {
  try {
    const response = await fetch('https://api.example.com/data');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

// Using an async function
async function processData() {
  try {
    const data = await fetchData();
    // Process data
  } catch (error) {
    // Handle error
  }
}
```

### Promise Combinators

Promise combinators allow you to work with multiple promises:

```javascript
// Promise.all - waits for all promises to resolve
const results = await Promise.all([
  fetchData1(),
  fetchData2(),
  fetchData3()
]);

// Promise.race - resolves/rejects when the first promise resolves/rejects
const result = await Promise.race([
  fetchWithTimeout(),
  timeout(5000)
]);

// Promise.allSettled - waits for all promises to settle
const results = await Promise.allSettled([
  fetchData1(),
  fetchData2(),
  fetchData3()
]);

// Promise.any - resolves when the first promise resolves
const result = await Promise.any([
  fetchFromSource1(),
  fetchFromSource2(),
  fetchFromSource3()
]);
```

## Functional Programming

### Array Methods

JavaScript provides several array methods for functional programming:

```javascript
// map - transform each element
const doubled = numbers.map(n => n * 2);

// filter - keep elements that pass a test
const evens = numbers.filter(n => n % 2 === 0);

// reduce - combine elements into a single value
const sum = numbers.reduce((acc, n) => acc + n, 0);

// find - find the first element that passes a test
const found = items.find(item => item.id === 42);

// some - check if at least one element passes a test
const hasEven = numbers.some(n => n % 2 === 0);

// every - check if all elements pass a test
const allPositive = numbers.every(n => n > 0);
```

### Function Composition

Function composition allows you to combine multiple functions:

```javascript
// Simple function composition
const compose = (f, g) => x => f(g(x));

// Multiple function composition
const compose = (...fns) => x => fns.reduceRight((y, f) => f(y), x);

// Example
const addOne = x => x + 1;
const double = x => x * 2;
const addOneThenDouble = compose(double, addOne);
```

## Object-Oriented Patterns

### Classes

JavaScript classes provide a cleaner syntax for object-oriented programming:

```javascript
class Person {
  constructor(name, age) {
    this.name = name;
    this.age = age;
  }

  greet() {
    return `Hello, my name is ${this.name}`;
  }

  static create(name, age) {
    return new Person(name, age);
  }
}

// Inheritance
class Employee extends Person {
  constructor(name, age, position) {
    super(name, age);
    this.position = position;
  }

  greet() {
    return `${super.greet()} and I am a ${this.position}`;
  }
}
```

### Getters and Setters

Getters and setters allow you to define computed properties:

```javascript
class Circle {
  constructor(radius) {
    this._radius = radius;
  }

  get radius() {
    return this._radius;
  }

  set radius(value) {
    if (value <= 0) {
      throw new Error('Radius must be positive');
    }
    this._radius = value;
  }

  get area() {
    return Math.PI * this._radius * this._radius;
  }
}
```

## Module Patterns

### ES Modules

ES Modules provide a standard way to organize code:

```javascript
// Exporting
export const PI = 3.14159;
export function square(x) {
  return x * x;
}
export default class Calculator {
  // ...
}

// Importing
import Calculator, { PI, square } from './math.js';
import * as math from './math.js';
```

### Dynamic Imports

Dynamic imports allow you to load modules on demand:

```javascript
async function loadModule() {
  try {
    const module = await import('./module.js');
    module.doSomething();
  } catch (error) {
    console.error('Error loading module:', error);
  }
}
```

## Error Handling

### Try/Catch

Try/catch blocks allow you to handle errors:

```javascript
try {
  // Code that might throw an error
  const data = JSON.parse(text);
  processData(data);
} catch (error) {
  // Handle the error
  console.error('Error processing data:', error);
  // Optionally rethrow or transform the error
  throw new ProcessingError('Failed to process data', { cause: error });
} finally {
  // Code that always runs
  cleanup();
}
```

### Custom Error Classes

Custom error classes allow you to create more specific errors:

```javascript
class ValidationError extends Error {
  constructor(message, field) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

class NotFoundError extends Error {
  constructor(message, resourceType, resourceId) {
    super(message);
    this.name = 'NotFoundError';
    this.resourceType = resourceType;
    this.resourceId = resourceId;
  }
}
```
