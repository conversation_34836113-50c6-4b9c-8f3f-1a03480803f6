# X10sion Features

This document provides an overview of the features available in X10sion, a local-first AI coding co-pilot for VS Code.

## Core Features

### Context-Aware AI Assistance

X10sion provides AI assistance that is aware of the context of your code. When you ask a question, X10sion sends the following context to the AI:

- The content of the active file
- The selected text (if any)
- The file path and language ID
- Any contextual guidelines you've provided

This allows the AI to provide more relevant and accurate responses.

### Local-First Architecture

X10sion uses a local-first architecture, which means:

- All AI processing happens on your local machine
- Your code never leaves your computer
- You have full control over the AI models used
- You can use X10sion without an internet connection

This provides better privacy, security, and control compared to cloud-based AI assistants.

### Customizable Prompts

X10sion allows you to customize the prompts sent to the AI through contextual guidelines. You can create two special files in your workspace:

- `x10sion_general_guidelines.md`: General guidelines for the AI assistant
- `x10sion_project_guidelines.md`: Project-specific guidelines

These files are read by X10sion and included in the context sent to the AI, allowing you to guide the AI's responses.

## User Interface

### Command Palette Integration

X10sion integrates with the VS Code Command Palette, allowing you to access its features with a few keystrokes:

- "X10sion: Ask AI (with context from active file)" - Ask a question about the current file
- "X10sion: Set Ollama API URL" - Configure the Ollama API URL

### Input Box

When you run the "Ask AI" command, X10sion displays an input box where you can enter your query. The input box includes:

- A prompt explaining what to enter
- A placeholder with example queries
- Focus retention (the input box stays open if you click elsewhere)

### Progress Indication

X10sion provides progress indication while waiting for the AI to respond, showing:

- A notification with a progress bar
- Status messages indicating the current step (gathering context, sending to Ollama, etc.)

### Response Display

AI responses are displayed in a notification, allowing you to quickly see the answer to your query. For longer responses, a more comprehensive display will be available in future versions.

## Configuration

### Ollama Integration

X10sion integrates with Ollama, a tool for running local LLMs. You can configure:

- The Ollama API URL (default: http://127.0.0.1:11434)
- The default model to use (default: gemma3:4b-it-q4_K_M)

### Resource Optimization

X10sion is designed to work well on systems with limited resources:

- It targets small, fast models like `gemma3:4b-it-q4_K_M` and `mistral:7b-instruct-v0.2-q4_K_M`
- It optimizes token usage to stay within the context window of these models
- It provides configuration options to adjust resource usage

## Upcoming Features

The following features are planned for future versions of X10sion:

### Phase 3: Basic UI & Contextual Guidelines
- Webview chat panel for more interactive conversations
- Better integration of contextual guidelines

### Phase 4: Rudimentary Local RAG
- Indexing of markdown files for retrieval-augmented generation
- Embedding generation and similarity search
- Integration of retrieved content into AI prompts

### Phase 5: Smarter Context & Agentic Foundations
- File system watcher for real-time updates to indexed files
- Tree-sitter integration for better code understanding
- Tool-aware prompt enhancement

For more information on upcoming features, see the [project roadmap](https://github.com/yourusername/x10sion/blob/main/README.md).
