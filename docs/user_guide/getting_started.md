# Getting Started with X10sion

This guide will help you get started with <PERSON><PERSON><PERSON>, a local-first AI coding co-pilot for VS Code.

## Prerequisites

Before using X10sion, you need to have the following installed:

1. **VS Code**: X10sion is a VS Code extension, so you need to have VS Code installed. You can download it from [here](https://code.visualstudio.com/).

2. **Ollama**: X10sion uses Ollama to run local LLMs. You can download and install Ollama from [here](https://ollama.ai/).

3. **LLM Model**: You need to have at least one LLM model installed in Ollama. X10sion works best with small, fast models like:
   - `gemma3:4b-it-q4_K_M`
   - `mistral:7b-instruct-v0.2-q4_K_M`
   - `tinyllama`

   You can install a model using the Ollama CLI:
   ```bash
   ollama pull gemma3:4b-it-q4_K_M
   ```

## Installation

1. Open VS Code
2. Go to the Extensions view (Ctrl+Shift+X or Cmd+Shift+X on Mac)
3. Search for "X10sion"
4. Click "Install"

Alternatively, you can install the extension from the VS Code Marketplace [here](https://marketplace.visualstudio.com/items?itemName=x10sion.x10sion).

## Configuration

X10sion has a few configuration options that you can set:

1. **Ollama URL**: By default, X10sion connects to Ollama at `http://127.0.0.1:11434`. If you're running Ollama on a different host or port, you can change this setting.
   - Run the command "X10sion: Set Ollama API URL" from the Command Palette
   - Enter the new URL

2. **Default Model**: By default, X10sion uses the `gemma3:4b-it-q4_K_M` model. You can change this in the VS Code settings:
   - Open the Settings UI (Ctrl+, or Cmd+, on Mac)
   - Search for "X10sion"
   - Change the "Default Model" setting

## Basic Usage

1. Open a file in VS Code
2. Run the command "X10sion: Ask AI (with context from active file)" from the Command Palette (Ctrl+Shift+P or Cmd+Shift+P on Mac)
3. Enter your query in the input box
4. Wait for the AI to respond
5. The response will be displayed in a notification

## Examples

Here are some examples of queries you can ask:

- "Explain what this code does"
- "Suggest improvements for this function"
- "Write documentation for this class"
- "Find potential bugs in this code"
- "How can I make this code more efficient?"

## Contextual Guidelines

X10sion can use contextual guidelines to provide more relevant responses. You can create two special files in your workspace:

1. `x10sion_general_guidelines.md`: General guidelines for the AI assistant
2. `x10sion_project_guidelines.md`: Project-specific guidelines

These files should be placed in the root of your workspace. X10sion will read them and include their content in the context sent to the AI.

## Troubleshooting

If you encounter issues with X10sion, try the following:

1. **Ollama not running**: Make sure Ollama is running on your machine. You can start it with the `ollama serve` command.

2. **Model not found**: Make sure you have the required model installed in Ollama. You can check installed models with `ollama list`.

3. **Connection issues**: Check that the Ollama URL is correct in the X10sion settings.

4. **Performance issues**: If the AI is slow to respond, try using a smaller model or reducing the context size.

## Next Steps

Once you're comfortable with the basic usage of X10sion, you can explore more advanced features:

- Create contextual guidelines to improve AI responses
- Use the RAG system to provide additional context from documentation
- Customize the extension settings to suit your needs

For more information, see the [Features](features.md) and [Configuration](configuration.md) guides.
