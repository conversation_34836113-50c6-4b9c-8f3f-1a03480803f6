# X10sion Configuration

This document provides information on how to configure X10sion, a local-first AI coding co-pilot for VS Code.

## Extension Settings

X10sion provides several configuration options that can be set through the VS Code settings UI or directly in your `settings.json` file.

### Ollama URL

The URL of the Ollama API endpoint. By default, this is set to `http://127.0.0.1:11434`, which is the default URL for a local Ollama instance.

- **Setting ID**: `x10sion.ollamaUrl`
- **Type**: String
- **Default**: `http://127.0.0.1:11434`

You can change this setting in two ways:

1. **Using the Command Palette**:
   - Open the Command Palette (Ctrl+Shift+P or Cmd+Shift+P on Mac)
   - Run the command "X10sion: Set Ollama API URL"
   - Enter the new URL in the input box

2. **Using the Settings UI**:
   - Open the Settings UI (Ctrl+, or Cmd+, on Mac)
   - Search for "X10sion"
   - Edit the "Ollama URL" setting

### Default Model

The default model to use for Ollama requests. This should be a model that is installed in your Ollama instance.

- **Setting ID**: `x10sion.defaultModel`
- **Type**: String
- **Default**: `gemma3:4b-it-q4_K_M`

You can change this setting using the Settings UI:
- Open the Settings UI (Ctrl+, or Cmd+, on Mac)
- Search for "X10sion"
- Edit the "Default Model" setting

## Contextual Guidelines

X10sion can use contextual guidelines to provide more relevant responses. You can create two special files in your workspace:

### General Guidelines

General guidelines for the AI assistant, such as tone, style, and general behavior.

- **File**: `x10sion_general_guidelines.md`
- **Location**: Root of your workspace
- **Format**: Markdown

Example content:
```markdown
# X10sion General Guidelines

## Response Style
- Be concise and to the point
- Use code examples when appropriate
- Explain complex concepts in simple terms

## Code Style
- Follow standard TypeScript/JavaScript conventions
- Use modern ES6+ syntax
- Prioritize readability over cleverness
```

### Project Guidelines

Project-specific guidelines, such as architecture, coding standards, and project-specific terminology.

- **File**: `x10sion_project_guidelines.md`
- **Location**: Root of your workspace
- **Format**: Markdown

Example content:
```markdown
# X10sion Project Guidelines

## Project Architecture
- This project uses a Model-View-Controller architecture
- All database access should go through the model layer
- All UI logic should be in the view layer
- All business logic should be in the controller layer

## Coding Standards
- Use camelCase for variables and functions
- Use PascalCase for classes and interfaces
- Use UPPER_CASE for constants
- Use 2-space indentation
```

## Advanced Configuration

### Manual Settings.json Configuration

You can also configure X10sion by directly editing your VS Code `settings.json` file:

```json
{
  "x10sion.ollamaUrl": "http://127.0.0.1:11434",
  "x10sion.defaultModel": "gemma3:4b-it-q4_K_M"
}
```

### Workspace-Specific Configuration

You can create workspace-specific configuration by adding these settings to your workspace `settings.json` file (located in the `.vscode` folder of your workspace).

This is useful if you want to use different settings for different projects, such as using a different model or Ollama instance for a specific project.

## Troubleshooting

If you encounter issues with your configuration, try the following:

1. **Check Ollama URL**: Make sure the Ollama URL is correct and that Ollama is running at that URL. You can test this by opening the URL in a browser or using curl:
   ```bash
   curl http://127.0.0.1:11434/api/version
   ```

2. **Check Model Availability**: Make sure the specified model is installed in your Ollama instance. You can check this with:
   ```bash
   ollama list
   ```

3. **Reset to Defaults**: If you're having issues, you can reset to the default configuration by removing the X10sion settings from your `settings.json` file.

4. **Check Logs**: If you're still having issues, check the VS Code Developer Tools console for any error messages. You can open this with Help > Toggle Developer Tools.
