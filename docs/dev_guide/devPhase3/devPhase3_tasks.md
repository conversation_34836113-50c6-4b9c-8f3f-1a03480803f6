# Development Phase 3 Tasks

This document outlines the specific tasks for Phase 3 of the X10sion project.

## Task 3.1: Create a Native Chat View

**Description**: Create a native VS Code tree view for chat with message display.

**Steps**:
1. Create a `ChatMessage` interface and `ChatMessageTreeItem` class:
   ```typescript
   interface ChatMessage {
       id: string;
       role: 'user' | 'assistant' | 'system';
       content: string;
       timestamp: number;
       isError?: boolean;
   }

   class ChatMessageTreeItem extends vscode.TreeItem {
       constructor(
           public readonly message: ChatMessage
       ) {
           super(
               message.content,
               vscode.TreeItemCollapsibleState.None
           );

           // Set icon based on role
           if (message.role === 'user') {
               this.iconPath = new vscode.ThemeIcon('account');
           } else if (message.role === 'assistant') {
               this.iconPath = new vscode.ThemeIcon('hubot');
           } else {
               this.iconPath = new vscode.ThemeIcon('info');
           }

           // Set tooltip with timestamp
           const date = new Date(message.timestamp);
           this.tooltip = `${message.role.charAt(0).toUpperCase() + message.role.slice(1)} - ${date.toLocaleTimeString()}`;

           // Set description with timestamp
           this.description = date.toLocaleTimeString();

           // Set context value for context menu filtering
           this.contextValue = `chatMessage-${message.role}`;

           // Set command to copy message to clipboard
           this.command = {
               title: 'Copy Message',
               command: 'x10sion.copyMessageToClipboard',
               arguments: [message.content]
           };
       }
   }
   ```

2. Create a `ChatViewProvider` class:
   ```typescript
   export class ChatViewProvider implements vscode.TreeDataProvider<ChatMessageTreeItem> {
       private _onDidChangeTreeData: vscode.EventEmitter<ChatMessageTreeItem | undefined | null | void> = new vscode.EventEmitter<ChatMessageTreeItem | undefined | null | void>();
       readonly onDidChangeTreeData: vscode.Event<ChatMessageTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

       private messages: ChatMessage[] = [];
       private isProcessing: boolean = false;
       private statusBarItem: vscode.StatusBarItem;
       private treeView: vscode.TreeView<ChatMessageTreeItem> | undefined;

       constructor(context: vscode.ExtensionContext) {
           // Initialize with a welcome message
           this.messages.push({
               id: 'welcome',
               role: 'assistant',
               content: 'Hello! I\'m X10sion AI. How can I help you with your code today?',
               timestamp: Date.now()
           });

           // Create status bar item
           this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
           this.statusBarItem.text = '$(comment) Chat';
           this.statusBarItem.tooltip = 'X10sion Chat';
           this.statusBarItem.command = 'x10sion.focusChatInput';
           this.statusBarItem.show();
           context.subscriptions.push(this.statusBarItem);

           // Register commands
           context.subscriptions.push(
               vscode.commands.registerCommand('x10sion.sendChatMessage', async () => {
                   await this.sendMessage();
               }),
               vscode.commands.registerCommand('x10sion.clearChat', () => {
                   this.clearChat();
               }),
               vscode.commands.registerCommand('x10sion.copyMessageToClipboard', (text: string) => {
                   vscode.env.clipboard.writeText(text);
                   vscode.window.showInformationMessage('Message copied to clipboard');
               }),
               vscode.commands.registerCommand('x10sion.focusChatInput', () => {
                   this.focusInput();
               })
           );
       }

       setTreeView(treeView: vscode.TreeView<ChatMessageTreeItem>) {
           this.treeView = treeView;
       }

       getTreeItem(element: ChatMessageTreeItem): vscode.TreeItem {
           return element;
       }

       getChildren(element?: ChatMessageTreeItem): Thenable<ChatMessageTreeItem[]> {
           if (element) {
               return Promise.resolve([]);
           } else {
               return Promise.resolve(
                   this.messages.map(message => new ChatMessageTreeItem(message))
               );
           }
       }

       async sendMessage() {
           if (this.isProcessing) {
               vscode.window.showInformationMessage('Please wait for the current message to be processed');
               return;
           }

           const userInput = await vscode.window.showInputBox({
               prompt: 'Type your message',
               placeHolder: 'Ask me anything about your code...',
               ignoreFocusOut: true
           });

           if (!userInput || userInput.trim() === '') {
               return;
           }

           // Add user message
           const userMessage: ChatMessage = {
               id: `user-${Date.now()}`,
               role: 'user',
               content: userInput,
               timestamp: Date.now()
           };
           this.messages.push(userMessage);
           this._onDidChangeTreeData.fire();

           // Add thinking message
           const thinkingMessage: ChatMessage = {
               id: `thinking-${Date.now()}`,
               role: 'assistant',
               content: 'Thinking...',
               timestamp: Date.now()
           };
           this.messages.push(thinkingMessage);
           this._onDidChangeTreeData.fire();

           // Scroll to bottom
           if (this.treeView) {
               this.treeView.reveal(new ChatMessageTreeItem(thinkingMessage), { select: false, focus: false });
           }

           // Set processing state
           this.isProcessing = true;
           this.statusBarItem.text = '$(sync~spin) Processing...';

           try {
               // Get editor context
               const contextInfo = await getActiveEditorContext();

               // Send to Ollama
               const response = await sendToOllama(userInput, contextInfo || {});

               // Remove thinking message
               this.messages = this.messages.filter(m => m.id !== thinkingMessage.id);

               // Add assistant message
               const assistantMessage: ChatMessage = {
                   id: `assistant-${Date.now()}`,
                   role: 'assistant',
                   content: response || 'Sorry, I couldn\'t process your request.',
                   timestamp: Date.now(),
                   isError: !response
               };
               this.messages.push(assistantMessage);
               this._onDidChangeTreeData.fire();

               // Scroll to bottom
               if (this.treeView) {
                   this.treeView.reveal(new ChatMessageTreeItem(assistantMessage), { select: false, focus: false });
               }
           } catch (error) {
               // Remove thinking message
               this.messages = this.messages.filter(m => m.id !== thinkingMessage.id);

               // Add error message
               const errorMessage: ChatMessage = {
                   id: `error-${Date.now()}`,
                   role: 'assistant',
                   content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                   timestamp: Date.now(),
                   isError: true
               };
               this.messages.push(errorMessage);
               this._onDidChangeTreeData.fire();
           } finally {
               // Reset processing state
               this.isProcessing = false;
               this.statusBarItem.text = '$(comment) Chat';
           }
       }

       clearChat() {
           // Keep only the welcome message
           this.messages = [this.messages[0]];
           this._onDidChangeTreeData.fire();
       }

       focusInput() {
           vscode.commands.executeCommand('x10sion.sendChatMessage');
       }

       refresh() {
           this._onDidChangeTreeData.fire();
       }
   }
   ```

3. Register the view and commands in `package.json`:
   ```json
   "contributes": {
     "views": {
       "x10sion": [
         {
           "id": "x10sionChat",
           "name": "Chat",
           "icon": "$(comment)",
           "contextualTitle": "X10sion Chat"
         }
       ]
     },
     "commands": [
       {
         "command": "x10sion.sendChatMessage",
         "title": "X10sion: Send Chat Message"
       },
       {
         "command": "x10sion.clearChat",
         "title": "X10sion: Clear Chat",
         "icon": "$(clear-all)"
       },
       {
         "command": "x10sion.copyMessageToClipboard",
         "title": "X10sion: Copy Message to Clipboard"
       }
     ],
     "menus": {
       "view/title": [
         {
           "command": "x10sion.sendChatMessage",
           "when": "view == x10sionChat",
           "group": "navigation"
         },
         {
           "command": "x10sion.clearChat",
           "when": "view == x10sionChat",
           "group": "navigation"
         }
       ],
       "view/item/context": [
         {
           "command": "x10sion.copyMessageToClipboard",
           "when": "view == x10sionChat && viewItem =~ /^chatMessage-/",
           "group": "inline"
         }
       ]
     }
   }
   ```

4. Register the view in the extension activation function:
   ```typescript
   export function activate(context: vscode.ExtensionContext) {
       // Create chat view provider
       const chatViewProvider = new ChatViewProvider(context);

       // Register tree view
       const chatTreeView = vscode.window.createTreeView('x10sionChat', {
           treeDataProvider: chatViewProvider,
           showCollapseAll: false
       });
       context.subscriptions.push(chatTreeView);
       chatViewProvider.setTreeView(chatTreeView);

       // Other code...
   }
   ```

**Expected Output**:
- A native VS Code tree view for chat
- Commands for sending messages and clearing the chat
- A status bar item for quick access to the chat

**Verification**:
1. Run the extension
2. Verify that the chat view appears in the sidebar
3. Verify that the status bar item appears
4. Click the status bar item and verify that the input box appears

## Task 3.2: Implement Commands for Sending and Receiving Messages

**Description**: Implement commands for sending messages and handling responses in the chat view.

**Steps**:
1. Create a function to get the active editor context:
   ```typescript
   async function getActiveEditorContext(): Promise<EditorContext | null> {
       const editor = vscode.window.activeTextEditor;
       if (!editor) {
           return null;
       }
       const document = editor.document;
       const selection = editor.selection;
       const selectedText = selection.isEmpty ? null : document.getText(selection);
       const fileContent = document.getText();
       const filePath = document.uri.fsPath;
       const languageId = document.languageId;
       return {
           filePath,
           languageId,
           selectedText,
           fileContent
       };
   }
   ```

2. Create a function to send messages to Ollama:
   ```typescript
   async function sendToOllama(userRequest: string, contextPayload: EditorContext): Promise<string | null> {
       try {
           // Get Ollama API URL from settings, default to localhost:11434
           const config = vscode.workspace.getConfiguration('x10sion');
           const ollamaApiUrl = config.get<string>('ollamaApiUrl', 'http://localhost:11434');
           const ollamaModel = config.get<string>('ollamaModel', 'llama3');

           // Construct the prompt with context
           let prompt = `User Query: ${userRequest}\n\n`;

           if (contextPayload.selectedText) {
               prompt += `Selected Code:\n\`\`\`${contextPayload.languageId || ''}\n${contextPayload.selectedText}\n\`\`\`\n\n`;
           } else if (contextPayload.fileContent) {
               prompt += `File Content (${contextPayload.filePath}):\n\`\`\`${contextPayload.languageId || ''}\n${contextPayload.fileContent}\n\`\`\`\n\n`;
           }

           prompt += `Please provide a helpful response to the user's query based on the provided context.`;

           // Call Ollama API
           const response = await fetch(`${ollamaApiUrl}/api/generate`, {
               method: 'POST',
               headers: {
                   'Content-Type': 'application/json',
               },
               body: JSON.stringify({
                   model: ollamaModel,
                   prompt: prompt,
                   stream: false,
               }),
           });

           if (!response.ok) {
               const errorText = await response.text();
               throw new Error(`Ollama API returned ${response.status}: ${errorText}`);
           }

           const responseData = await response.json();
           console.log('Ollama Response:', responseData);

           if (responseData && typeof responseData.response === 'string') {
               return responseData.response.trim();
           } else {
               vscode.window.showErrorMessage('Ollama response malformed or missing "response" field. Check Debug Console.');
               console.error('Ollama response missing "response" field or not a string:', responseData);
               return null;
           }
       } catch (error: any) {
           vscode.window.showErrorMessage(`Error calling Ollama: ${error.message}. Check Debug Console.`);
           console.error('Error calling Ollama:', error);
           return null;
       }
   }
   ```

**Expected Output**:
- The ability to send messages from the chat view to Ollama
- The ability to receive and display responses from Ollama in the chat view

**Verification**:
1. Open the chat view
2. Click the "Send Chat Message" button or use the status bar item
3. Type a message and press Enter
4. Verify that the message is displayed in the chat view
5. Verify that a response from Ollama is displayed in the chat view

## Task 3.3: Read Contextual Guidelines

**Description**: Add functionality to read `x10sion_general_guidelines.md` and `x10sion_project_guidelines.md` from the workspace root if they exist.

**Steps**:
1. Create a function to read guideline files:
   ```typescript
   async function readGuidelines(): Promise<{ general: string | null, project: string | null }> {
     const workspaceFolders = vscode.workspace.workspaceFolders;
     if (!workspaceFolders) {
       return { general: null, project: null };
     }

     const rootPath = workspaceFolders[0].uri;
     const generalPath = vscode.Uri.joinPath(rootPath, 'x10sion_general_guidelines.md');
     const projectPath = vscode.Uri.joinPath(rootPath, 'x10sion_project_guidelines.md');

     let generalContent: string | null = null;
     let projectContent: string | null = null;

     try {
       const generalFile = await vscode.workspace.fs.readFile(generalPath);
       generalContent = new TextDecoder().decode(generalFile);
       console.log('Read general guidelines:', generalPath.fsPath);
     } catch (error) {
       console.log('No general guidelines found or error reading file:', error.message);
     }

     try {
       const projectFile = await vscode.workspace.fs.readFile(projectPath);
       projectContent = new TextDecoder().decode(projectFile);
       console.log('Read project guidelines:', projectPath.fsPath);
     } catch (error) {
       console.log('No project guidelines found or error reading file:', error.message);
     }

     return { general: generalContent, project: projectContent };
   }
   ```

2. Add a command to test reading guidelines:
   ```typescript
   const testGuidelinesCommand = vscode.commands.registerCommand('x10sion.testGuidelines', async () => {
     const guidelines = await readGuidelines();

     if (guidelines.general || guidelines.project) {
       vscode.window.showInformationMessage('Guidelines found. Check Debug Console for details.');
       console.log('Guidelines:', guidelines);
     } else {
       vscode.window.showInformationMessage('No guidelines found. Create x10sion_general_guidelines.md and/or x10sion_project_guidelines.md in the workspace root.');
     }
   });

   context.subscriptions.push(testGuidelinesCommand);
   ```

3. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testGuidelines",
         "title": "X10sion: Test Guidelines"
       }
     ]
   }
   ```

**Expected Output**:
- A function that reads guideline files from the workspace root
- A command that tests reading guidelines and logs the content

**Verification**:
1. Create test guideline files in the workspace root
2. Run the "X10sion: Test Guidelines" command
3. Verify that the content of the guideline files is logged to the Debug Console

## Task 3.4: Integrate Guidelines into Context

**Description**: Add content from guideline files to the context JSON sent to Ollama, respecting token budget.

**Steps**:
1. Update the `sendToOllama` function to include guidelines:
   ```typescript
   async function sendToOllama(userRequest: string, contextPayload: EditorContext, guidelines?: { general: string | null, project: string | null }): Promise<string | null> {
     // Read guidelines if not provided
     if (!guidelines) {
       guidelines = await readGuidelines();
     }

     // Create a context object that includes guidelines
     const fullContext = {
       ...contextPayload,
       guidelines: {
         general: guidelines.general ? truncateIfNeeded(guidelines.general, 1000) : null,
         project: guidelines.project ? truncateIfNeeded(guidelines.project, 1000) : null
       }
     };

     // Rest of the function...
     const fullPrompt = `System: You are a helpful AI coding assistant. Your task is to respond to the user's request based on the provided context. Provide concise and accurate answers. Do not output the code from the context unless explicitly asked to.

     Context from editor:
     ---
     ${JSON.stringify(fullContext, null, 2)}
     ---

     User Request: ${userRequest}

     AI Response:`;
     // ...
   }

   function truncateIfNeeded(text: string, maxLength: number): string {
     if (text.length <= maxLength) {
       return text;
     }

     // Simple truncation for now; could be improved with summarization
     return text.substring(0, maxLength) + '... [truncated]';
   }
   ```

2. Update the `_handleMessage` method in `ChatPanel` to include guidelines:
   ```typescript
   private async _handleMessage(text: string) {
     try {
       // Get context from active editor
       const contextInfo = await getActiveEditorContext();

       // Get guidelines
       const guidelines = await readGuidelines();

       // Show thinking indicator
       const thinkingMessage: ChatMessage = {
         id: `thinking-${Date.now()}`,
         role: 'assistant',
         content: 'Thinking...',
         timestamp: Date.now()
       };
       this.messages.push(thinkingMessage);
       this._onDidChangeTreeData.fire();

       // Get response from Ollama with guidelines
       const aiResponseText = await sendToOllama(text, contextInfo || {}, guidelines);

       // Rest of the method...
     } catch (error) {
       // Error handling...
     }
   }
   ```

**Expected Output**:
- Guidelines included in the context sent to Ollama
- Token budget respected by truncating guidelines if needed

**Verification**:
1. Create test guideline files in the workspace root
2. Open the chat panel
3. Send a message
4. Verify that the response reflects the content of the guideline files

## Completion Criteria

Phase 3 is considered complete when:
- The extension has a functional native VS Code chat view
- Commands for sending and receiving messages are working
- The extension can read and process contextual guideline files
- Guideline content is included in the context sent to the LLM
- Token budgeting is implemented to ensure the prompt fits within the LLM's context window
