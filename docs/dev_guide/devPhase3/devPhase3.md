# Development Phase 3: Basic UI (Native Chat View) & Contextual Guidelines

## Overview

Phase 3 focuses on creating a basic user interface using VS Code's native UI components and implementing contextual guidelines to provide more relevant AI responses. This phase builds on the previous phases by adding a more interactive UI and incorporating user-defined context.

## Goals

- Create a native VS Code tree view for chat with an input field and message display
- Implement commands for sending and receiving messages
- Add functionality to read and process contextual guideline files
- Integrate guideline content into the context sent to the LLM

## Reference Materials

### Internal References
- [Project Structure](../../../dirStructure.md)
- [File Relationships](../../../fileRelations.md)
- [VS Code Native UI Best Practices](../../../knowledge_base/best_practices/vscode_native_ui_best_practices.md)
- [TypeScript Best Practices](../../../knowledge_base/best_practices/typescript_best_practices.md)
- [General Guidelines](../../../x10sion_general_guidelines.md)
- [Project Guidelines](../../../x10sion_project_guidelines.md)
- [Test Method](../../../testMethod.md)

### External References (May 2025)
- [VS Code Tree View API Documentation](https://code.visualstudio.com/api/extension-guides/tree-view)
- [VS Code Extension API Documentation](https://code.visualstudio.com/api/references/vscode-api)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Marked.js Documentation](https://marked.js.org/) - For markdown parsing

## Technical Details

### Tree View API

VS Code's Tree View API allows extensions to create custom UI using native VS Code components. The main components are:

- `vscode.window.createTreeView`: Creates a new tree view
- `TreeDataProvider`: Provides data for the tree view
- `TreeItem`: Represents an item in the tree view
- `vscode.commands`: Used to register commands for user interaction

### Contextual Guidelines

Contextual guidelines are user-defined markdown files that provide additional context to the LLM. The extension will:

- Read `x10sion_general_guidelines.md` and `x10sion_project_guidelines.md` from the workspace root
- Process the content of these files
- Include the content in the context sent to the LLM
- Handle token budgeting to ensure the prompt fits within the LLM's context window

## Implementation Steps

1. Create a native VS Code tree view for chat with message display
2. Implement commands for sending and receiving messages
3. Add functionality to read and process contextual guideline files
4. Integrate guideline content into the context sent to the LLM

## Testing

Each implementation step can be tested by:

1. Running the extension and opening the chat view
2. Sending messages using the commands and verifying responses
3. Creating test guideline files and verifying that they are read correctly
4. Checking that guideline content is included in the context sent to the LLM

## Next Steps

After completing Phase 3, the project will move on to [Phase 4: Rudimentary Local RAG (Text-Based)](../devPhase4/devPhase4.md), which focuses on implementing a rudimentary local RAG system for retrieving relevant information from markdown files.

For detailed tasks in this phase, refer to [Phase 3 Tasks](./devPhase3_tasks.md).

## Resource Considerations

Phase 3 has minimal additional resource requirements due to using native UI components:
- Memory usage: ~20MB (minimal increase from Phase 2)
- CPU usage: Low, as native UI components are more efficient
- Context window: The guideline integration is designed to be efficient for LLMs with 4K context windows
- VRAM requirements: Compatible with 8GB VRAM GPUs

## Test Results

For test results of this phase, see [Phase 3 Test Results](../../../testResults/phase3/phase3_full_testResult.md).

## Timestamp

Last updated: May 19, 2025
