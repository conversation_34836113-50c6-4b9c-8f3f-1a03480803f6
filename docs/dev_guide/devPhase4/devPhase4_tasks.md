# Development Phase 4 Tasks

This document outlines the specific tasks for Phase 4 of the X10sion project, broken down into small, manageable steps suitable for an 8GB VRAM LLM with a 4K context window.

## Task 4.1: Implement Markdown Indexing

This task is broken down into smaller sub-tasks to make it more manageable.

### Task 4.1.1: Create Function to Find Markdown Files

**Description**: Create a function to find all markdown files in the workspace.

**Steps**:
1. Create a function that uses `vscode.workspace.findFiles` to locate markdown files:
   ```typescript
   async function findMarkdownFiles(): Promise<vscode.Uri[]> {
     const workspaceFolders = vscode.workspace.workspaceFolders;
     if (!workspaceFolders) {
       return [];
     }

     const markdownFiles = await vscode.workspace.findFiles('**/*.md', '**/node_modules/**');
     console.log(`Found ${markdownFiles.length} markdown files`);
     return markdownFiles;
   }
   ```

**Expected Output**:
- A function that returns an array of URIs for markdown files in the workspace

**Verification**:
1. Call the function and log the result
2. Verify that it finds the expected markdown files

### Task 4.1.2: Define TextChunk Interface

**Description**: Define an interface for text chunks that will be used for indexing.

**Steps**:
1. Create an interface that defines the structure of a text chunk:
   ```typescript
   interface TextChunk {
     text: string;       // The content of the chunk
     source: string;     // The source file path
     embedding?: number[]; // Optional embedding vector (added later)
   }
   ```

**Expected Output**:
- An interface that defines the structure of text chunks

**Verification**:
1. Use the interface in subsequent code
2. Verify that TypeScript type checking works correctly

### Task 4.1.3: Create Function to Read and Chunk Markdown Files

**Description**: Create a function that reads markdown files and splits them into overlapping chunks.

**Steps**:
1. Create a function that reads file content and splits it into chunks:
   ```typescript
   async function chunkMarkdownFiles(files: vscode.Uri[]): Promise<TextChunk[]> {
     const chunks: TextChunk[] = [];
     const targetChunkSize = 300; // Target token size
     const overlapSize = 50; // Overlap size

     for (const file of files) {
       try {
         const content = await vscode.workspace.fs.readFile(file);
         const text = new TextDecoder().decode(content);

         // Simple chunking by paragraphs for now
         const paragraphs = text.split(/\n\s*\n/);
         let currentChunk = '';

         for (const paragraph of paragraphs) {
           // If adding this paragraph would exceed the target size, create a new chunk
           if (currentChunk.length + paragraph.length > targetChunkSize && currentChunk.length > 0) {
             chunks.push({
               text: currentChunk,
               source: file.fsPath
             });

             // Start a new chunk with overlap
             const words = currentChunk.split(/\s+/);
             currentChunk = words.slice(-overlapSize).join(' ');
           }

           currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
         }

         // Add the last chunk if it's not empty
         if (currentChunk.trim().length > 0) {
           chunks.push({
             text: currentChunk,
             source: file.fsPath
           });
         }
       } catch (error) {
         console.error(`Error reading file ${file.fsPath}:`, error);
       }
     }

     console.log(`Created ${chunks.length} chunks from ${files.length} files`);
     return chunks;
   }
   ```

**Expected Output**:
- A function that reads markdown files and returns an array of text chunks

**Verification**:
1. Call the function with test files
2. Verify that it creates chunks of appropriate size
3. Verify that chunks have overlap as expected

### Task 4.1.4: Create Command to Test Indexing

**Description**: Create a command that tests the markdown indexing functionality.

**Steps**:
1. Create a command that finds markdown files and chunks them:
   ```typescript
   const testIndexingCommand = vscode.commands.registerCommand('x10sion.testIndexing', async () => {
     const files = await findMarkdownFiles();
     if (files.length === 0) {
       vscode.window.showInformationMessage('No markdown files found in the workspace.');
       return;
     }

     vscode.window.withProgress({
       location: vscode.ProgressLocation.Notification,
       title: "Indexing markdown files...",
       cancellable: false
     }, async (progress) => {
       progress.report({ increment: 0 });

       const chunks = await chunkMarkdownFiles(files);

       progress.report({ increment: 100 });

       vscode.window.showInformationMessage(`Indexed ${chunks.length} chunks from ${files.length} files. Check Debug Console for details.`);
       console.log('Sample chunks:', chunks.slice(0, 3));
     });
   });

   context.subscriptions.push(testIndexingCommand);
   ```

2. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testIndexing",
         "title": "X10sion: Test Markdown Indexing"
       }
     ]
   }
   ```

**Expected Output**:
- A command that tests the indexing process
- A notification showing the number of chunks created
- Sample chunks logged to the Debug Console

**Verification**:
1. Run the "X10sion: Test Markdown Indexing" command
2. Verify that it shows a progress notification
3. Verify that it shows a success notification with the number of chunks
4. Verify that sample chunks are logged to the Debug Console

## Task 4.2: Implement Embedding Generation

This task is broken down into smaller sub-tasks to make it more manageable.

### Task 4.2.1: Create Function to Generate Single Embedding

**Description**: Create a function that generates an embedding for a single text chunk using Ollama's embedding endpoint.

**Steps**:
1. Create a function that sends a request to Ollama's embedding endpoint:
   ```typescript
   async function generateEmbedding(text: string): Promise<number[] | null> {
     const ollamaEndpoint = 'http://127.0.0.1:11434/api/embeddings';
     const modelName = 'gemma3:4b-it-q4_K_M'; // or another suitable model

     const requestBody = {
       model: modelName,
       prompt: text
     };

     try {
       const response = await fetch(ollamaEndpoint, {
         method: 'POST',
         body: JSON.stringify(requestBody),
         headers: { 'Content-Type': 'application/json' }
       });

       if (!response.ok) {
         throw new Error(`Status: ${response.status} ${response.statusText}`);
       }

       const responseData = await response.json();
       console.log('Ollama Embedding Response:', responseData);

       if (responseData && Array.isArray(responseData.embedding)) {
         return responseData.embedding;
       } else {
         console.error('Invalid embedding response:', responseData);
         return null;
       }
     } catch (error) {
       console.error('Error generating embedding:', error);
       return null;
     }
   }
   ```

**Expected Output**:
- A function that generates an embedding for a single text chunk

**Verification**:
1. Call the function with a test string
2. Verify that it returns an array of numbers (the embedding)
3. Verify error handling by testing with invalid input

### Task 4.2.2: Create Function to Generate Embeddings for Multiple Chunks

**Description**: Create a function that generates embeddings for multiple text chunks.

**Steps**:
1. Create a function that processes an array of chunks and generates embeddings for each:
   ```typescript
   async function generateEmbeddings(chunks: TextChunk[]): Promise<TextChunk[]> {
     const chunksWithEmbeddings: TextChunk[] = [];

     for (let i = 0; i < chunks.length; i++) {
       const chunk = chunks[i];
       const embedding = await generateEmbedding(chunk.text);

       if (embedding) {
         chunksWithEmbeddings.push({
           ...chunk,
           embedding
         });
       }

       // Log progress every 10 chunks
       if ((i + 1) % 10 === 0 || i === chunks.length - 1) {
         console.log(`Generated embeddings for ${i + 1}/${chunks.length} chunks`);
       }
     }

     return chunksWithEmbeddings;
   }
   ```

**Expected Output**:
- A function that generates embeddings for multiple text chunks

**Verification**:
1. Call the function with an array of test chunks
2. Verify that it returns an array of chunks with embeddings
3. Verify that progress is logged appropriately

### Task 4.2.3: Create Command to Test Embedding Generation

**Description**: Create a command that tests the embedding generation functionality.

**Steps**:
1. Create a command that finds markdown files, chunks them, and generates embeddings:
   ```typescript
   const testEmbeddingsCommand = vscode.commands.registerCommand('x10sion.testEmbeddings', async () => {
     const files = await findMarkdownFiles();
     if (files.length === 0) {
       vscode.window.showInformationMessage('No markdown files found in the workspace.');
       return;
     }

     vscode.window.withProgress({
       location: vscode.ProgressLocation.Notification,
       title: "Generating embeddings...",
       cancellable: false
     }, async (progress) => {
       progress.report({ increment: 0, message: "Chunking files..." });

       const chunks = await chunkMarkdownFiles(files);

       progress.report({ increment: 50, message: "Generating embeddings..." });

       // Limit to 10 chunks for testing
       const testChunks = chunks.slice(0, 10);
       const chunksWithEmbeddings = await generateEmbeddings(testChunks);

       progress.report({ increment: 100 });

       vscode.window.showInformationMessage(`Generated embeddings for ${chunksWithEmbeddings.length} chunks. Check Debug Console for details.`);
       console.log('Sample chunks with embeddings:', chunksWithEmbeddings.slice(0, 3));
     });
   });

   context.subscriptions.push(testEmbeddingsCommand);
   ```

2. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testEmbeddings",
         "title": "X10sion: Test Embeddings"
       }
     ]
   }
   ```

**Expected Output**:
- A command that tests the embedding generation process
- A notification showing the number of embeddings generated
- Sample chunks with embeddings logged to the Debug Console

**Verification**:
1. Ensure Ollama is running with a model that supports embeddings
2. Run the "X10sion: Test Embeddings" command
3. Verify that it shows a progress notification
4. Verify that it shows a success notification with the number of embeddings
5. Verify that sample chunks with embeddings are logged to the Debug Console

## Task 4.3: Implement Similarity Search

This task is broken down into smaller sub-tasks to make it more manageable.

### Task 4.3.1: Create Function to Calculate Cosine Similarity

**Description**: Create a function that calculates the cosine similarity between two vectors.

**Steps**:
1. Create a function that implements the cosine similarity formula:
   ```typescript
   function cosineSimilarity(a: number[], b: number[]): number {
     if (a.length !== b.length) {
       throw new Error('Vectors must have the same length');
     }

     let dotProduct = 0;
     let normA = 0;
     let normB = 0;

     for (let i = 0; i < a.length; i++) {
       dotProduct += a[i] * b[i];
       normA += a[i] * a[i];
       normB += b[i] * b[i];
     }

     return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
   }
   ```

**Expected Output**:
- A function that calculates the cosine similarity between two vectors

**Verification**:
1. Call the function with test vectors
2. Verify that it returns a value between -1 and 1
3. Verify that identical vectors return 1
4. Verify that orthogonal vectors return 0
5. Verify error handling for vectors of different lengths

### Task 4.3.2: Create Function to Retrieve Relevant Chunks

**Description**: Create a function that retrieves the most relevant chunks for a given query.

**Steps**:
1. Create a function that generates an embedding for the query and finds the most similar chunks:
   ```typescript
   async function retrieveRelevantChunks(query: string, chunks: TextChunk[], topK: number = 3): Promise<TextChunk[]> {
     const queryEmbedding = await generateEmbedding(query);

     if (!queryEmbedding) {
       console.error('Failed to generate embedding for query');
       return [];
     }

     // Calculate similarity for each chunk
     const chunksWithSimilarity = chunks
       .filter(chunk => chunk.embedding !== undefined)
       .map(chunk => ({
         chunk,
         similarity: cosineSimilarity(queryEmbedding, chunk.embedding!)
       }));

     // Sort by similarity (descending) and take top K
     const topChunks = chunksWithSimilarity
       .sort((a, b) => b.similarity - a.similarity)
       .slice(0, topK)
       .map(item => item.chunk);

     return topChunks;
   }
   ```

**Expected Output**:
- A function that retrieves the most relevant chunks for a given query

**Verification**:
1. Call the function with a test query and chunks
2. Verify that it returns the expected number of chunks
3. Verify that the chunks are sorted by similarity
4. Verify error handling for empty chunks or failed embedding generation

### Task 4.3.3: Create Command to Test Retrieval

**Description**: Create a command that tests the similarity search functionality.

**Steps**:
1. Create a command that gets a query from the user and retrieves relevant chunks:
   ```typescript
   const testRetrievalCommand = vscode.commands.registerCommand('x10sion.testRetrieval', async () => {
     // Get query from user
     const query = await vscode.window.showInputBox({
       prompt: "Enter a query to test retrieval",
       placeHolder: "e.g., How do I register a command in VS Code?",
       ignoreFocusOut: true
     });

     if (!query) {
       return;
     }

     vscode.window.withProgress({
       location: vscode.ProgressLocation.Notification,
       title: "Retrieving relevant chunks...",
       cancellable: false
     }, async (progress) => {
       progress.report({ increment: 0, message: "Finding markdown files..." });

       const files = await findMarkdownFiles();
       if (files.length === 0) {
         vscode.window.showInformationMessage('No markdown files found in the workspace.');
         return;
       }

       progress.report({ increment: 20, message: "Chunking files..." });

       const chunks = await chunkMarkdownFiles(files);

       progress.report({ increment: 40, message: "Generating embeddings..." });

       const chunksWithEmbeddings = await generateEmbeddings(chunks);

       progress.report({ increment: 80, message: "Retrieving relevant chunks..." });

       const relevantChunks = await retrieveRelevantChunks(query, chunksWithEmbeddings);

       progress.report({ increment: 100 });

       vscode.window.showInformationMessage(`Retrieved ${relevantChunks.length} relevant chunks. Check Debug Console for details.`);
       console.log('Query:', query);
       console.log('Relevant chunks:', relevantChunks);
     });
   });

   context.subscriptions.push(testRetrievalCommand);
   ```

2. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testRetrieval",
         "title": "X10sion: Test Retrieval"
       }
     ]
   }
   ```

**Expected Output**:
- A command that tests the retrieval process
- A notification showing the number of relevant chunks retrieved
- Relevant chunks logged to the Debug Console

**Verification**:
1. Run the "X10sion: Test Retrieval" command
2. Enter a query related to the content of your markdown files
3. Verify that it shows a progress notification
4. Verify that it shows a success notification with the number of chunks
5. Verify that relevant chunks are logged to the Debug Console

## Task 4.4: Integrate RAG into Context

This task is broken down into smaller sub-tasks to make it more manageable.

### Task 4.4.1: Create RAG System Class

**Description**: Create a class to manage the RAG system.

**Steps**:
1. Create a class with methods for indexing and retrieving content:
   ```typescript
   class RAGSystem {
     private chunks: TextChunk[] = [];
     private isIndexed: boolean = false;

     async indexWorkspace(): Promise<void> {
       const files = await findMarkdownFiles();
       if (files.length === 0) {
         console.log('No markdown files found in the workspace.');
         return;
       }

       const chunks = await chunkMarkdownFiles(files);
       this.chunks = await generateEmbeddings(chunks);
       this.isIndexed = true;

       console.log(`Indexed ${this.chunks.length} chunks from ${files.length} files.`);
     }

     async retrieveRelevantContent(query: string, topK: number = 3): Promise<string | null> {
       if (!this.isIndexed || this.chunks.length === 0) {
         await this.indexWorkspace();
       }

       if (!this.isIndexed || this.chunks.length === 0) {
         return null;
       }

       const relevantChunks = await retrieveRelevantChunks(query, this.chunks, topK);

       if (relevantChunks.length === 0) {
         return null;
       }

       // Combine chunks into a single string with source information
       const combinedContent = relevantChunks.map(chunk => {
         return `Source: ${chunk.source}\n\n${chunk.text}`;
       }).join('\n\n---\n\n');

       return combinedContent;
     }
   }
   ```

**Expected Output**:
- A class that manages the RAG system
- Methods for indexing the workspace and retrieving relevant content

**Verification**:
1. Create an instance of the class
2. Call the indexWorkspace method
3. Verify that chunks are indexed
4. Call the retrieveRelevantContent method with a test query
5. Verify that relevant content is returned

### Task 4.4.2: Create Helper Function for Token Budgeting

**Description**: Create a helper function to truncate text if it exceeds a certain length.

**Steps**:
1. Create a function that truncates text if it exceeds a specified length:
   ```typescript
   function truncateIfNeeded(text: string, maxLength: number): string {
     if (text.length <= maxLength) {
       return text;
     }

     // Simple truncation for now; could be improved with summarization
     return text.substring(0, maxLength) + '... [truncated]';
   }
   ```

**Expected Output**:
- A function that truncates text if it exceeds a specified length

**Verification**:
1. Call the function with text shorter than the maximum length
2. Verify that the text is returned unchanged
3. Call the function with text longer than the maximum length
4. Verify that the text is truncated and has the truncation marker

### Task 4.4.3: Update sendToOllama Function to Include RAG Content

**Description**: Update the sendToOllama function to include RAG content in the context sent to Ollama.

**Steps**:
1. Update the function signature and implementation to include RAG content:
   ```typescript
   async function sendToOllama(
     userRequest: string,
     contextPayload: EditorContext,
     guidelines?: { general: string | null, project: string | null },
     ragContent?: string | null
   ): Promise<string | null> {
     // Read guidelines if not provided
     if (!guidelines) {
       guidelines = await readGuidelines();
     }

     // Create a context object that includes guidelines and RAG content
     const fullContext = {
       ...contextPayload,
       guidelines: {
         general: guidelines.general ? truncateIfNeeded(guidelines.general, 1000) : null,
         project: guidelines.project ? truncateIfNeeded(guidelines.project, 1000) : null
       },
       ragContent: ragContent ? truncateIfNeeded(ragContent, 1500) : null
     };

     // Rest of the function...
     const fullPrompt = `System: You are a helpful AI coding assistant. Your task is to respond to the user's request based on the provided context. Provide concise and accurate answers. Do not output the code from the context unless explicitly asked to.

     Context from editor:
     ---
     ${JSON.stringify(fullContext, null, 2)}
     ---

     User Request: ${userRequest}

     AI Response:`;
     // ...
   }
   ```

**Expected Output**:
- An updated sendToOllama function that includes RAG content in the context

**Verification**:
1. Call the function with test inputs including RAG content
2. Verify that the prompt includes the RAG content
3. Verify that the RAG content is truncated if it exceeds the maximum length

### Task 4.4.4: Integrate RAG System with Chat Panel

**Description**: Update the chat panel to use the RAG system when handling messages.

**Steps**:
1. Create an instance of the RAG system in the extension activation:
   ```typescript
   export function activate(context: vscode.ExtensionContext) {
     // Create RAG system
     const ragSystem = new RAGSystem();

     // Rest of the activation code...
   }
   ```

2. Update the chat panel's message handler to use the RAG system:
   ```typescript
   private async _handleMessage(text: string) {
     try {
       // Get context from active editor
       const contextInfo = await getActiveEditorContext();

       // Get guidelines
       const guidelines = await readGuidelines();

       // Show thinking indicator
       this._panel.webview.postMessage({
         command: 'addMessage',
         text: 'Thinking...',
         isUser: false
       });

       // Get relevant content from RAG system
       const ragContent = await ragSystem.retrieveRelevantContent(text);

       // Get response from Ollama with guidelines and RAG content
       const aiResponseText = await sendToOllama(text, contextInfo || {}, guidelines, ragContent);

       // Rest of the method...
     } catch (error) {
       // Error handling...
     }
   }
   ```

**Expected Output**:
- An updated chat panel that uses the RAG system when handling messages

**Verification**:
1. Open the chat panel
2. Send a message related to the content of your markdown files
3. Verify that the RAG system is used to retrieve relevant content
4. Verify that the response includes information from the markdown files

### Task 4.4.5: Create Command to Test RAG Integration

**Description**: Create a command that tests the RAG integration.

**Steps**:
1. Create a command that tests the RAG integration:
   ```typescript
   const testRAGCommand = vscode.commands.registerCommand('x10sion.testRAG', async () => {
     // Get query from user
     const query = await vscode.window.showInputBox({
       prompt: "Enter a query to test RAG integration",
       placeHolder: "e.g., How do I register a command in VS Code?",
       ignoreFocusOut: true
     });

     if (!query) {
       return;
     }

     vscode.window.withProgress({
       location: vscode.ProgressLocation.Notification,
       title: "Testing RAG integration...",
       cancellable: false
     }, async (progress) => {
       progress.report({ increment: 0, message: "Retrieving relevant content..." });

       // Get relevant content from RAG system
       const ragContent = await ragSystem.retrieveRelevantContent(query);

       progress.report({ increment: 50, message: "Sending to Ollama..." });

       // Get context from active editor
       const contextInfo = await getActiveEditorContext();

       // Get guidelines
       const guidelines = await readGuidelines();

       // Get response from Ollama with guidelines and RAG content
       const aiResponseText = await sendToOllama(query, contextInfo || {}, guidelines, ragContent);

       progress.report({ increment: 100 });

       if (typeof aiResponseText === 'string') {
         vscode.window.showInformationMessage(`AI response: ${aiResponseText}`);
       } else {
         vscode.window.showErrorMessage('Failed to get response from Ollama.');
       }

       console.log('RAG content:', ragContent);
       console.log('AI response:', aiResponseText);
     });
   });

   context.subscriptions.push(testRAGCommand);
   ```

2. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testRAG",
         "title": "X10sion: Test RAG Integration"
       }
     ]
   }
   ```

**Expected Output**:
- A command that tests the RAG integration
- A notification showing the AI response
- RAG content and AI response logged to the Debug Console

**Verification**:
1. Run the "X10sion: Test RAG Integration" command
2. Enter a query related to the content of your markdown files
3. Verify that it shows a progress notification
4. Verify that it shows the AI response
5. Verify that the RAG content and AI response are logged to the Debug Console

## Completion Criteria

Phase 4 is considered complete when:
- The extension can scan and index markdown files in the workspace
- The extension can generate embeddings for chunks and store them in memory
- The extension can retrieve relevant chunks based on a query
- Retrieved content is included in the context sent to the LLM
- Token budgeting is implemented to ensure the prompt fits within the LLM's context window
