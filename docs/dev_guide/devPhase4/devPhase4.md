# Development Phase 4: Rudimentary Local RAG (Text-Based)

## Overview

Phase 4 focuses on implementing a rudimentary Retrieval-Augmented Generation (RAG) system that can retrieve relevant information from markdown files in the workspace. This system will enhance the AI's responses by providing additional context from documentation and other text sources.

## Goals

- Implement functionality to scan and index markdown files in the workspace
- Create a simple in-memory vector store for embeddings
- Implement similarity search to retrieve relevant content
- Integrate retrieved content into the context sent to the LLM

## Reference Materials

### Internal References
- [Project Structure](../../../dirStructure.md)
- [File Relationships](../../../fileRelations.md)
- [RAG Implementation Best Practices](../../../knowledge_base/best_practices/rag_implementation_best_practices.md)
- [Optimization Techniques](../../../knowledge_base/architecture/optimization_techniques.md)
- [WebAssembly Integration](../../../knowledge_base/architecture/webassembly_integration.md)
- [Test Method](../../../testMethod.md)

### External References (May 2025)
- [Ollama API Documentation](https://github.com/ollama/ollama/blob/main/docs/api.md)
- [VS Code Extension API Documentation](https://code.visualstudio.com/api/references/vscode-api)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Marked.js Documentation](https://marked.js.org/) - For markdown parsing
- [Vector Search Libraries](https://github.com/topics/vector-search) - For similarity search

## Technical Details

### Document Indexing

The extension will scan specified markdown files in the workspace and split them into overlapping chunks. Each chunk will be small enough to be used as context for the LLM while still containing enough information to be useful.

### Embedding Generation

The extension will use Ollama's embedding endpoint or a lightweight local embedding model to generate embeddings for each chunk. These embeddings will be stored in memory for quick retrieval.

### Similarity Search

When a user submits a query, the extension will generate an embedding for the query and calculate the cosine similarity between the query embedding and all chunk embeddings. The most similar chunks will be retrieved and included in the context sent to the LLM.

### RAG Integration

The retrieved chunks will be added to the context JSON sent to Ollama, respecting the token budget to ensure the prompt fits within the LLM's context window.

## Implementation Steps

1. Implement functionality to scan and index markdown files
2. Create a simple in-memory vector store for embeddings
3. Implement similarity search to retrieve relevant content
4. Integrate retrieved content into the context sent to the LLM

## Testing

Each implementation step can be tested by:

1. Creating test markdown files with various content
2. Running the indexing process and verifying that chunks are created
3. Running test queries and verifying that relevant chunks are retrieved
4. Checking that retrieved content is included in the context sent to the LLM

## Next Steps

After completing Phase 4, the project will move on to [Phase 5: Smarter Context & Agentic Foundations](../devPhase5/devPhase5.md), which focuses on implementing smarter context gathering and agentic foundations.

For detailed tasks in this phase, refer to [Phase 4 Tasks](./devPhase4_tasks.md).

## Resource Considerations

Phase 4 has increased resource requirements due to the RAG system:
- Memory usage: ~50MB (significant increase from Phase 3 due to vector storage)
- CPU usage: Moderate, especially during indexing and similarity search
- Context window: The RAG system is designed to be efficient for LLMs with 4K context windows
- VRAM requirements: Compatible with 8GB VRAM GPUs, with optimizations for vector operations

## Test Results

For test results of this phase, see [Phase 4 Test Results](../../../testResults/phase4/phase4_full_testResult.md).

## Timestamp

Last updated: May 19, 2025
