# Development Phase 15: User Onboarding & LLM Recommendation

*Last Updated: May 25, 2025*

## Overview

Phase 15 focuses on implementing intelligent user onboarding and LLM recommendation systems for X10sion. This phase introduces specialized AI agents for user intent analysis, hardware analysis, and LLM recommendations, along with enhanced dynamic LLM management capabilities.

## Reference Materials

### Internal References
- [Project Structure](../../../dirStructure.md)
- [File Relationships](../../../fileRelations.md)
- [AI Agent Architecture](../../../knowledge_base/architecture/ai_agents.md)
- [Hardware Analysis Best Practices](../../../knowledge_base/best_practices/vscode_extension_best_practices.md)
- [Test Method](../../../testMethod.md)

### External References (May 2025)
- [Google ADK Documentation](https://google.github.io/adk-docs/)
- [System Information API](https://www.npmjs.com/package/systeminformation)
- [Ollama Model Management](https://ollama.com/library)
- [LM Studio API Documentation](https://lmstudio.ai/docs)
- [TypeScript 5.5+ Documentation](https://www.typescriptlang.org/docs/)

## Goals

The primary goals of Phase 15 are:

1. **UserIntentAnalysisAgent**: Engage non-technical users to clarify business ideas and generate structured requirements
2. **HardwareAnalysisAgent**: Ethically detect local system hardware with user consent
3. **LLMRecommendationAgent**: Recommend optimal local LLMs based on hardware specs and project requirements
4. **Dynamic LLM Management**: Enhanced Project System Router for LLM loading/unloading based on task requirements

## Technical Details

### UserIntentAnalysisAgent

The user intent analysis agent provides:

1. **Business Idea Clarification**: Interactive dialogue to understand user goals
2. **Requirement Generation**: Convert business ideas into structured technical requirements
3. **Project Scope Definition**: Help users define realistic project scope
4. **Technology Recommendations**: Suggest appropriate technologies based on requirements

### HardwareAnalysisAgent

The hardware analysis agent includes:

1. **Ethical Hardware Detection**: Detect CPU, RAM, GPU/VRAM with explicit user consent
2. **Performance Profiling**: Analyze system performance capabilities
3. **Resource Monitoring**: Real-time monitoring of system resources
4. **Privacy Compliance**: Ensure all hardware detection respects user privacy

### LLMRecommendationAgent

The LLM recommendation agent provides:

1. **Hardware-Based Recommendations**: Suggest optimal LLMs based on available hardware
2. **Project-Specific Suggestions**: Recommend models based on project requirements
3. **Tiered Recommendations**: Provide multiple options (fast, balanced, advanced)
4. **Performance Predictions**: Estimate performance for different model configurations

### Enhanced Project System Router

The enhanced router includes:

1. **Dynamic LLM Management**: Load/unload LLMs based on task requirements
2. **Resource-Aware Scheduling**: Optimize LLM usage based on available resources
3. **Task-Model Matching**: Automatically select optimal models for specific tasks
4. **Performance Optimization**: Minimize resource usage while maximizing performance

## Implementation Steps

1. **UserIntentAnalysisAgent**: Implement conversational agent for requirement gathering
2. **HardwareAnalysisAgent**: Create ethical hardware detection with user consent
3. **LLMRecommendationAgent**: Build recommendation engine with tiered suggestions
4. **Dynamic LLM Management**: Enhance Project System Router with dynamic capabilities
5. **Integration & Testing**: Comprehensive testing and integration validation

## Success Criteria

1. **User Intent Analysis**: Successfully gather and structure user requirements
2. **Hardware Analysis**: Accurately detect hardware with proper consent mechanisms
3. **LLM Recommendations**: Provide accurate, helpful model recommendations
4. **Dynamic Management**: Efficiently manage LLM loading/unloading
5. **User Experience**: Smooth onboarding experience for non-technical users

## Next Steps

After completing Phase 15, the project will move to Phase 16, which will focus on:

1. **Security & Compliance Agent**: Continuous security analysis and compliance monitoring
2. **Automated Architecture & Design Prototyping**: AI-assisted architectural decision making
3. **Advanced Security Features**: Enhanced security analysis and vulnerability detection

## Resource Considerations

This phase is designed to be manageable for LLMs with limited resources:
- Memory usage: ~280MB baseline, 450MB peak during hardware analysis
- CPU usage: 30% average, 85% peak during LLM recommendation generation
- Context window: Components designed to fit within 4K token context window
- VRAM requirements: Compatible with 8GB VRAM GPUs with dynamic management

For detailed tasks in this phase, refer to [Phase 15 Tasks](./devPhase15_tasks.md).
