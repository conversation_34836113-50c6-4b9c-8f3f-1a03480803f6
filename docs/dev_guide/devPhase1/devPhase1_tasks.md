# Development Phase 1 Tasks

This document outlines the specific tasks for Phase 1 of the X10sion project. These tasks are designed to be manageable for LLMs with limited context windows (8GB VRAM, 4K context).

## Reference Materials

### Internal References
- [Project Structure](../../../dirStructure.md)
- [File Relationships](../../../fileRelations.md)
- [VS Code Extension Best Practices](../../../knowledge_base/best_practices/vscode_extension_best_practices.md)
- [TypeScript Best Practices](../../../knowledge_base/best_practices/typescript_best_practices.md)
- [TypeScript Patterns](../../../knowledge_base/code_patterns/typescript_patterns.md)
- [Test Method](../../../testMethod.md)

### External References (May 2025)
- [VS Code Extension API Documentation](https://code.visualstudio.com/api/references/vscode-api)
- [VS Code TextEditor API](https://code.visualstudio.com/api/references/vscode-api#TextEditor)
- [VS Code TextDocument API](https://code.visualstudio.com/api/references/vscode-api#TextDocument)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)

## Task 1.1: Get and Display Selected Text

**Description**: Modify the command to get and display selected text from the active editor.

**Steps**:
1. Access the active editor using `vscode.window.activeTextEditor`
2. Check if an editor is active; if not, show an appropriate message
3. Get the current selection using `editor.selection`
4. Get the selected text using `document.getText(selection)`
5. Display the selected text in a notification

**Code Example**:
```typescript
const editor = vscode.window.activeTextEditor;
if (!editor) {
  vscode.window.showInformationMessage('No active editor found.');
  return;
}

const selection = editor.selection;
const selectedText = editor.document.getText(selection);

if (selectedText.length === 0) {
  vscode.window.showInformationMessage('No text selected.');
} else {
  vscode.window.showInformationMessage(`Selected text: ${selectedText}`);
}
```

**Expected Output**:
- If no editor is active: "No active editor found."
- If no text is selected: "No text selected."
- If text is selected: "Selected text: [selected text]"

**Verification**:
1. Open a file in VS Code
2. Select some text
3. Run the command
4. Verify that the selected text is displayed in a notification

## Task 1.2: Get and Display Full Content

**Description**: Modify the command to get and display the full content of the active file.

**Steps**:
1. Access the active editor using `vscode.window.activeTextEditor`
2. Check if an editor is active; if not, show an appropriate message
3. Get the document using `editor.document`
4. Get the full text content using `document.getText()`
5. Display the full content (or a preview) in a notification or log it to the console

**Code Example**:
```typescript
const editor = vscode.window.activeTextEditor;
if (!editor) {
  vscode.window.showInformationMessage('No active editor found.');
  return;
}

const document = editor.document;
const fullContent = document.getText();

// For brevity in the notification, show only the first 50 characters
const preview = fullContent.length > 50 ? `${fullContent.substring(0, 50)}...` : fullContent;
vscode.window.showInformationMessage(`File content (preview): ${preview}`);

// Log the full content to the console
console.log('Full file content:', fullContent);
```

**Expected Output**:
- If no editor is active: "No active editor found."
- If an editor is active: "File content (preview): [preview of content]"
- Full content logged to the console

**Verification**:
1. Open a file in VS Code
2. Run the command
3. Verify that a preview of the content is displayed in a notification
4. Check the Debug Console to see the full content

## Task 1.3: Get and Display File Metadata

**Description**: Modify the command to get and display the file path and language ID of the active file.

**Steps**:
1. Access the active editor using `vscode.window.activeTextEditor`
2. Check if an editor is active; if not, show an appropriate message
3. Get the document using `editor.document`
4. Get the file path using `document.uri.fsPath`
5. Get the language ID using `document.languageId`
6. Display the file path and language ID in a notification

**Code Example**:
```typescript
const editor = vscode.window.activeTextEditor;
if (!editor) {
  vscode.window.showInformationMessage('No active editor found.');
  return;
}

const document = editor.document;
const filePath = document.uri.fsPath;
const languageId = document.languageId;

vscode.window.showInformationMessage(`File: ${filePath}\nLanguage: ${languageId}`);
```

**Expected Output**:
- If no editor is active: "No active editor found."
- If an editor is active: "File: [file path]\nLanguage: [language ID]"

**Verification**:
1. Open a file in VS Code
2. Run the command
3. Verify that the file path and language ID are displayed in a notification

## Task 1.4: Assemble Context into JSON

**Description**: Create a helper function to assemble gathered context (selected text, file content, path, language) into a simple JSON object and log it.

**Steps**:
1. Create a helper function that takes the active editor as input
2. Extract the selected text, full content, file path, and language ID
3. Assemble these into a JSON object
4. Return the JSON object
5. Log the JSON object to the console when the command is run

**Code Example**:
```typescript
function getActiveEditorContext() {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return null;
  }

  const document = editor.document;
  const selection = editor.selection;
  const selectedText = document.getText(selection);
  const fullContent = document.getText();
  const filePath = document.uri.fsPath;
  const languageId = document.languageId;

  return {
    filePath,
    languageId,
    selectedText,
    fullContent
  };
}

// In the command handler:
const contextInfo = getActiveEditorContext();
if (contextInfo) {
  console.log('Editor context:', JSON.stringify(contextInfo, null, 2));
  vscode.window.showInformationMessage('Context gathered. Check Debug Console.');
} else {
  vscode.window.showInformationMessage('No active editor found.');
}
```

**Expected Output**:
- If no editor is active: "No active editor found."
- If an editor is active: "Context gathered. Check Debug Console."
- JSON object logged to the console with the structure:
  ```json
  {
    "filePath": "...",
    "languageId": "...",
    "selectedText": "...",
    "fullContent": "..."
  }
  ```

**Verification**:
1. Open a file in VS Code
2. Select some text
3. Run the command
4. Verify that "Context gathered. Check Debug Console." is displayed
5. Check the Debug Console to see the JSON object with the gathered context

## Completion Criteria

Phase 1 is considered complete when:
- The command can get and display selected text from the active editor
- The command can get and display the full content of the active file
- The command can get and display the file path and language ID
- The command can assemble the gathered context into a JSON object and log it
- All edge cases (no active editor, no selection) are handled appropriately

## Next Steps

After completing all tasks in Phase 1, proceed to [Phase 2: Basic Local LLM Interaction](../devPhase2/devPhase2.md), which focuses on implementing communication with Ollama for local LLM inference.

## Test Results

For test results of this phase, see [Phase 1 Test Results](../../../testResults/phase1/phase1_full_testResult.md).

## Resource Considerations

These tasks are designed to be manageable for LLMs with limited resources:
- Memory usage: ~20MB
- CPU usage: Negligible for small files, may increase for very large files
- Context window: Tasks are broken down to fit within a 4K token context window
- VRAM requirements: Compatible with 8GB VRAM GPUs

## Timestamp

Last updated: May 19, 2025
