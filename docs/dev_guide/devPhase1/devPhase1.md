# Development Phase 1: Core Editor Context Gathering

## Overview

Phase 1 focuses on gathering context from the active editor in VS Code. This context includes the selected text, the full content of the active file, and metadata such as the file path and language ID. This information will be used in later phases to provide context-aware AI assistance.

## Goals

- Access and display selected text from the active editor
- Access and display the full content of the active file
- Access and display metadata about the active file (path, language ID)
- Assemble gathered context into a structured JSON object
- Establish the EditorContext interface for future phases

## Reference Materials

### Internal References
- [Project Structure](../../../dirStructure.md)
- [File Relationships](../../../fileRelations.md)
- [VS Code Extension Best Practices](../../../knowledge_base/best_practices/vscode_extension_best_practices.md)
- [TypeScript Best Practices](../../../knowledge_base/best_practices/typescript_best_practices.md)
- [TypeScript Patterns](../../../knowledge_base/code_patterns/typescript_patterns.md)
- [Test Method](../../../testMethod.md)

### External References (May 2025)
- [VS Code Extension API Documentation](https://code.visualstudio.com/api/references/vscode-api)
- [VS Code TextEditor API](https://code.visualstudio.com/api/references/vscode-api#TextEditor)
- [VS Code TextDocument API](https://code.visualstudio.com/api/references/vscode-api#TextDocument)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)

## Technical Details

### Editor Context

VS Code provides access to the active editor through the `vscode.window.activeTextEditor` API. This object provides access to:

- The document being edited (`editor.document`)
- The current selection (`editor.selection`)
- The editor's view column (`editor.viewColumn`)

### Document Content

The document object provides access to:

- The full text content (`document.getText()`)
- The selected text (`document.getText(selection)`)
- The file path (`document.uri.fsPath`)
- The language ID (`document.languageId`)

### Context Assembly

The gathered context is assembled into a structured JSON object that will be used in later phases to provide context to the AI model. This object includes:

- The selected text (if any)
- The full content of the active file
- The file path
- The language ID

## Implementation Steps

1. Modify the command to get and display selected text
2. Modify the command to get and display the full content of the active file
3. Modify the command to get and display the file path and language ID
4. Create a helper function to assemble the gathered context into a JSON object

## Testing

Each implementation step can be tested by:

1. Opening a file in VS Code
2. Selecting text (for the first step)
3. Running the command
4. Verifying that the expected information is displayed or logged

## Next Steps

After completing Phase 1, the project will move on to [Phase 2: Basic Local LLM Interaction](../devPhase2/devPhase2.md), which focuses on basic local LLM interaction via Ollama.

For detailed tasks in this phase, refer to [Phase 1 Tasks](./devPhase1_tasks.md).

## Resource Considerations

Phase 1 has minimal additional resource requirements:
- Memory usage: ~20MB (slight increase from Phase 0)
- CPU usage: Negligible for small files, may increase for very large files
- Context window: The gathered context is structured to be efficient for LLMs with 4K context windows
- VRAM requirements: Compatible with 8GB VRAM GPUs

## Test Results

For test results of this phase, see [Phase 1 Test Results](../../../testResults/phase1/phase1_full_testResult.md).

## Timestamp

Last updated: May 19, 2025
