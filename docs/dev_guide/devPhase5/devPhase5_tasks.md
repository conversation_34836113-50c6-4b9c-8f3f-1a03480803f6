# Development Phase 5 Tasks

This document outlines the specific tasks for Phase 5 of the X10sion project.

## Task 5.1: Implement File System Watcher

**Description**: Implement `vscode.workspace.createFileSystemWatcher` to monitor changes to `.md` guideline files and potentially other RAG sources. Re-index on change (debounced).

**Steps**:
1. Create a file system watcher for markdown files:
   ```typescript
   class RAGSystem {
     private chunks: TextChunk[] = [];
     private isIndexed: boolean = false;
     private watcher: vscode.FileSystemWatcher | undefined;
     private indexingTimeout: NodeJS.Timeout | undefined;

     constructor() {
       this.setupWatcher();
     }

     private setupWatcher() {
       // Watch for changes to markdown files
       this.watcher = vscode.workspace.createFileSystemWatcher('**/*.md');

       // Re-index on file creation
       this.watcher.onDidCreate(() => {
         this.debouncedReindex();
       });

       // Re-index on file change
       this.watcher.onDidChange(() => {
         this.debouncedReindex();
       });

       // Re-index on file deletion
       this.watcher.onDidDelete(() => {
         this.debouncedReindex();
       });

       console.log('File system watcher set up for markdown files');
     }

     private debouncedReindex() {
       // Clear existing timeout
       if (this.indexingTimeout) {
         clearTimeout(this.indexingTimeout);
       }

       // Set new timeout to re-index after 2 seconds of inactivity
       this.indexingTimeout = setTimeout(() => {
         console.log('Re-indexing due to file changes');
         this.indexWorkspace();
         this.indexingTimeout = undefined;
       }, 2000);
     }

     // Rest of the RAG system implementation...
   }
   ```

2. Add a command to test the file system watcher:
   ```typescript
   const testWatcherCommand = vscode.commands.registerCommand('x10sion.testWatcher', () => {
     vscode.window.showInformationMessage('File system watcher is active. Make changes to markdown files to trigger re-indexing.');
     console.log('File system watcher is active');
   });

   context.subscriptions.push(testWatcherCommand);
   ```

3. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testWatcher",
         "title": "X10sion: Test File System Watcher"
       }
     ]
   }
   ```

**Expected Output**:
- A file system watcher that monitors changes to markdown files
- Debounced re-indexing when files change
- A command to test the file system watcher

**Verification**:
1. Run the "X10sion: Test File System Watcher" command
2. Make changes to markdown files in the workspace
3. Verify that re-indexing is triggered (check Debug Console for logs)

## Task 5.2: Integrate Tree-sitter for Basic Code Understanding

**Description**: Integrate `tree-sitter` for the active file's language. Extract top-level function/class names.

**Steps**:
1. Add tree-sitter as a dependency:
   ```bash
   npm install tree-sitter tree-sitter-typescript tree-sitter-javascript
   ```

2. Create a function to extract top-level function and class names:
   ```typescript
   import * as Parser from 'tree-sitter';
   import * as TreeSitterTypescript from 'tree-sitter-typescript';
   import * as TreeSitterJavascript from 'tree-sitter-javascript';

   interface CodeSymbol {
     type: 'function' | 'class' | 'method' | 'other';
     name: string;
     startLine: number;
     endLine: number;
   }

   async function extractCodeSymbols(text: string, languageId: string): Promise<CodeSymbol[]> {
     const parser = new Parser();

     // Set the language based on the file type
     switch (languageId) {
       case 'typescript':
         parser.setLanguage(TreeSitterTypescript.typescript);
         break;
       case 'javascript':
         parser.setLanguage(TreeSitterJavascript);
         break;
       default:
         console.log(`Language ${languageId} not supported for symbol extraction`);
         return [];
     }

     // Parse the code
     const tree = parser.parse(text);
     const rootNode = tree.rootNode;

     // Extract symbols
     const symbols: CodeSymbol[] = [];

     // Helper function to process a node
     function processNode(node: Parser.SyntaxNode) {
       let type: CodeSymbol['type'] = 'other';
       let name = '';

       // Check node type
       if (node.type === 'function_declaration' || node.type === 'function') {
         type = 'function';
         // Find the function name
         for (let i = 0; i < node.childCount; i++) {
           const child = node.child(i);
           if (child && child.type === 'identifier') {
             name = text.substring(child.startIndex, child.endIndex);
             break;
           }
         }
       } else if (node.type === 'class_declaration' || node.type === 'class') {
         type = 'class';
         // Find the class name
         for (let i = 0; i < node.childCount; i++) {
           const child = node.child(i);
           if (child && child.type === 'identifier') {
             name = text.substring(child.startIndex, child.endIndex);
             break;
           }
         }
       } else if (node.type === 'method_definition') {
         type = 'method';
         // Find the method name
         for (let i = 0; i < node.childCount; i++) {
           const child = node.child(i);
           if (child && (child.type === 'property_identifier' || child.type === 'identifier')) {
             name = text.substring(child.startIndex, child.endIndex);
             break;
           }
         }
       }

       // Add symbol if we found a name
       if (name) {
         symbols.push({
           type,
           name,
           startLine: node.startPosition.row,
           endLine: node.endPosition.row
         });
       }

       // Process children recursively
       for (let i = 0; i < node.childCount; i++) {
         const child = node.child(i);
         if (child) {
           processNode(child);
         }
       }
     }

     // Start processing from the root
     processNode(rootNode);

     return symbols;
   }
   ```

3. Add a command to test symbol extraction:
   ```typescript
   const testSymbolsCommand = vscode.commands.registerCommand('x10sion.testSymbols', async () => {
     const editor = vscode.window.activeTextEditor;
     if (!editor) {
       vscode.window.showInformationMessage('No active editor found.');
       return;
     }

     const document = editor.document;
     const text = document.getText();
     const languageId = document.languageId;

     vscode.window.withProgress({
       location: vscode.ProgressLocation.Notification,
       title: "Extracting code symbols...",
       cancellable: false
     }, async (progress) => {
       progress.report({ increment: 0 });

       const symbols = await extractCodeSymbols(text, languageId);

       progress.report({ increment: 100 });

       vscode.window.showInformationMessage(`Extracted ${symbols.length} symbols. Check Debug Console for details.`);
       console.log('Extracted symbols:', symbols);
     });
   });

   context.subscriptions.push(testSymbolsCommand);
   ```

4. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testSymbols",
         "title": "X10sion: Test Symbol Extraction"
       }
     ]
   }
   ```

**Expected Output**:
- A function that extracts top-level function and class names from code
- A command to test symbol extraction

**Verification**:
1. Open a TypeScript or JavaScript file
2. Run the "X10sion: Test Symbol Extraction" command
3. Verify that symbols are extracted and logged to the Debug Console

## Task 5.3: Implement Code Chunking for RAG

**Description**: If RAG is extended to code, use `tree-sitter` to chunk code at function/class boundaries for indexing.

**Steps**:
1. Create a function to chunk code at function/class boundaries:
   ```typescript
   async function chunkCodeFile(file: vscode.Uri): Promise<TextChunk[]> {
     try {
       const content = await vscode.workspace.fs.readFile(file);
       const text = new TextDecoder().decode(content);

       // Get the language ID based on file extension
       const extension = file.fsPath.split('.').pop()?.toLowerCase();
       let languageId = 'text';

       if (extension === 'ts' || extension === 'tsx') {
         languageId = 'typescript';
       } else if (extension === 'js' || extension === 'jsx') {
         languageId = 'javascript';
       }

       // Extract symbols
       const symbols = await extractCodeSymbols(text, languageId);

       // Create chunks based on symbols
       const chunks: TextChunk[] = [];

       for (const symbol of symbols) {
         // Get the lines for this symbol
         const lines = text.split('\n');
         const symbolLines = lines.slice(symbol.startLine, symbol.endLine + 1);
         const symbolText = symbolLines.join('\n');

         chunks.push({
           text: symbolText,
           source: `${file.fsPath}:${symbol.name}`,
           metadata: {
             type: symbol.type,
             name: symbol.name,
             startLine: symbol.startLine,
             endLine: symbol.endLine
           }
         });
       }

       // If no symbols were found, create a single chunk for the whole file
       if (chunks.length === 0) {
         chunks.push({
           text,
           source: file.fsPath
         });
       }

       return chunks;
     } catch (error) {
       console.error(`Error chunking code file ${file.fsPath}:`, error);
       return [];
     }
   }
   ```

2. Update the RAG system to include code files:
   ```typescript
   class RAGSystem {
     // Existing code...

     async indexWorkspace(): Promise<void> {
       // Find markdown files
       const markdownFiles = await findMarkdownFiles();

       // Find code files (TypeScript and JavaScript)
       const codeFiles = await vscode.workspace.findFiles('**/*.{ts,js}', '**/node_modules/**');

       console.log(`Found ${markdownFiles.length} markdown files and ${codeFiles.length} code files`);

       // Chunk markdown files
       const markdownChunks = await chunkMarkdownFiles(markdownFiles);

       // Chunk code files
       let codeChunks: TextChunk[] = [];
       for (const file of codeFiles) {
         const chunks = await chunkCodeFile(file);
         codeChunks = codeChunks.concat(chunks);
       }

       console.log(`Created ${markdownChunks.length} markdown chunks and ${codeChunks.length} code chunks`);

       // Generate embeddings for all chunks
       const allChunks = [...markdownChunks, ...codeChunks];
       this.chunks = await generateEmbeddings(allChunks);
       this.isIndexed = true;

       console.log(`Indexed ${this.chunks.length} total chunks`);
     }

     // Rest of the RAG system implementation...
   }
   ```

3. Add a command to test code chunking:
   ```typescript
   const testCodeChunkingCommand = vscode.commands.registerCommand('x10sion.testCodeChunking', async () => {
     const editor = vscode.window.activeTextEditor;
     if (!editor) {
       vscode.window.showInformationMessage('No active editor found.');
       return;
     }

     const document = editor.document;
     const uri = document.uri;

     vscode.window.withProgress({
       location: vscode.ProgressLocation.Notification,
       title: "Chunking code file...",
       cancellable: false
     }, async (progress) => {
       progress.report({ increment: 0 });

       const chunks = await chunkCodeFile(uri);

       progress.report({ increment: 100 });

       vscode.window.showInformationMessage(`Created ${chunks.length} chunks. Check Debug Console for details.`);
       console.log('Code chunks:', chunks);
     });
   });

   context.subscriptions.push(testCodeChunkingCommand);
   ```

4. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testCodeChunking",
         "title": "X10sion: Test Code Chunking"
       }
     ]
   }
   ```

**Expected Output**:
- A function that chunks code at function/class boundaries
- Integration of code chunking into the RAG system
- A command to test code chunking

**Verification**:
1. Open a TypeScript or JavaScript file
2. Run the "X10sion: Test Code Chunking" command
3. Verify that chunks are created and logged to the Debug Console

## Task 5.4: Define Tool Structure

**Description**: Define a simple structure for "tools" (e.g., a JSON object describing tool name, purpose, input schema). Start with a "read_file_content" tool.

**Steps**:
1. Define interfaces for tools:
   ```typescript
   interface ToolParameter {
     name: string;
     type: 'string' | 'number' | 'boolean' | 'object' | 'array';
     description: string;
     required: boolean;
   }

   interface Tool {
     name: string;
     description: string;
     parameters: ToolParameter[];
     execute: (params: Record<string, any>) => Promise<any>;
   }
   ```

2. Create a tool registry:
   ```typescript
   class ToolRegistry {
     private tools: Map<string, Tool> = new Map();

     registerTool(tool: Tool) {
       this.tools.set(tool.name, tool);
       console.log(`Registered tool: ${tool.name}`);
     }

     getTool(name: string): Tool | undefined {
       return this.tools.get(name);
     }

     getAllTools(): Tool[] {
       return Array.from(this.tools.values());
     }

     getToolsSchema(): any {
       const schema: Record<string, any> = {};

       for (const tool of this.tools.values()) {
         schema[tool.name] = {
           description: tool.description,
           parameters: {
             type: 'object',
             properties: {}
           }
         };

         for (const param of tool.parameters) {
           schema[tool.name].parameters.properties[param.name] = {
             type: param.type,
             description: param.description
           };
         }

         schema[tool.name].parameters.required = tool.parameters
           .filter(param => param.required)
           .map(param => param.name);
       }

       return schema;
     }

     async executeTool(name: string, params: Record<string, any>): Promise<any> {
       const tool = this.getTool(name);
       if (!tool) {
         throw new Error(`Tool not found: ${name}`);
       }

       return await tool.execute(params);
     }
   }
   ```

3. Create a "read_file_content" tool:
   ```typescript
   const readFileContentTool: Tool = {
     name: 'read_file_content',
     description: 'Read the content of a file in the workspace',
     parameters: [
       {
         name: 'path',
         type: 'string',
         description: 'The path to the file, relative to the workspace root',
         required: true
       }
     ],
     execute: async (params: Record<string, any>) => {
       const { path } = params;

       if (!path || typeof path !== 'string') {
         throw new Error('Path parameter is required and must be a string');
       }

       const workspaceFolders = vscode.workspace.workspaceFolders;
       if (!workspaceFolders) {
         throw new Error('No workspace folder is open');
       }

       const rootPath = workspaceFolders[0].uri;
       const filePath = vscode.Uri.joinPath(rootPath, path);

       try {
         const content = await vscode.workspace.fs.readFile(filePath);
         return new TextDecoder().decode(content);
       } catch (error) {
         throw new Error(`Failed to read file: ${error.message}`);
       }
     }
   };
   ```

4. Create an instance of the tool registry and register the tool:
   ```typescript
   export function activate(context: vscode.ExtensionContext) {
     // Create tool registry
     const toolRegistry = new ToolRegistry();

     // Register tools
     toolRegistry.registerTool(readFileContentTool);

     // Rest of the activation code...
   }
   ```

5. Add a command to test the tool:
   ```typescript
   const testToolCommand = vscode.commands.registerCommand('x10sion.testTool', async () => {
     // Get file path from user
     const path = await vscode.window.showInputBox({
       prompt: "Enter a file path to read",
       placeHolder: "e.g., README.md",
       ignoreFocusOut: true
     });

     if (!path) {
       return;
     }

     vscode.window.withProgress({
       location: vscode.ProgressLocation.Notification,
       title: "Executing tool...",
       cancellable: false
     }, async (progress) => {
       progress.report({ increment: 0 });

       try {
         const result = await toolRegistry.executeTool('read_file_content', { path });

         progress.report({ increment: 100 });

         vscode.window.showInformationMessage(`Tool executed successfully. Check Debug Console for result.`);
         console.log('Tool result:', result);
       } catch (error) {
         progress.report({ increment: 100 });

         vscode.window.showErrorMessage(`Tool execution failed: ${error.message}`);
         console.error('Tool execution failed:', error);
       }
     });
   });

   context.subscriptions.push(testToolCommand);
   ```

6. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testTool",
         "title": "X10sion: Test Tool Execution"
       }
     ]
   }
   ```

**Expected Output**:
- Interfaces for defining tools
- A tool registry for managing tools
- A "read_file_content" tool
- A command to test tool execution

**Verification**:
1. Run the "X10sion: Test Tool Execution" command
2. Enter a file path (e.g., "README.md")
3. Verify that the tool executes and the file content is logged to the Debug Console

## Task 5.5: Improve Prompt Enhancement Agent

**Description**: Improve the Prompt Enhancement Agent to be aware of available tools and include tool usage instructions in the prompt to the main LLM if the user's query suggests a tool might be needed.

**Steps**:
1. Create a function to enhance prompts with tool information:
   ```typescript
   async function enhancePrompt(
     userRequest: string,
     contextPayload: EditorContext,
     guidelines?: { general: string | null, project: string | null },
     ragContent?: string | null
   ): Promise<string> {
     // Read guidelines if not provided
     if (!guidelines) {
       guidelines = await readGuidelines();
     }

     // Create a context object that includes guidelines and RAG content
     const fullContext = {
       ...contextPayload,
       guidelines: {
         general: guidelines.general ? truncateIfNeeded(guidelines.general, 1000) : null,
         project: guidelines.project ? truncateIfNeeded(guidelines.project, 1000) : null
       },
       ragContent: ragContent ? truncateIfNeeded(ragContent, 1500) : null
     };

     // Check if the user request might need a tool
     const mightNeedTool = checkIfMightNeedTool(userRequest);

     // Get tool schema if needed
     const toolSchema = mightNeedTool ? toolRegistry.getToolsSchema() : null;

     // Create the prompt
     let prompt = `System: You are a helpful AI coding assistant. Your task is to respond to the user's request based on the provided context. Provide concise and accurate answers. Do not output the code from the context unless explicitly asked to.

     Context from editor:
     ---
     ${JSON.stringify(fullContext, null, 2)}
     ---
     `;

     // Add tool information if needed
     if (mightNeedTool && toolSchema) {
       prompt += `
     Available tools:
     ---
     ${JSON.stringify(toolSchema, null, 2)}
     ---

     If the user's request requires using a tool, you can call the tool using the following format:
     TOOL: <tool_name>
     PARAMS: <JSON object with parameters>

     For example:
     TOOL: read_file_content
     PARAMS: {"path": "README.md"}

     After calling a tool, wait for the result before continuing.
     `;
     }

     // Add user request
     prompt += `
     User Request: ${userRequest}

     AI Response:`;

     return prompt;
   }

   function checkIfMightNeedTool(userRequest: string): boolean {
     // Simple heuristic: check if the request contains keywords that might indicate a need for a tool
     const toolKeywords = [
       'read', 'file', 'content', 'open', 'show', 'display', 'get',
       'what is in', 'what does', 'can you show', 'can you read'
     ];

     const lowerRequest = userRequest.toLowerCase();

     return toolKeywords.some(keyword => lowerRequest.includes(keyword));
   }
   ```

2. Update the `sendToOllama` function to use the enhanced prompt:
   ```typescript
   async function sendToOllama(
     userRequest: string,
     contextPayload: EditorContext,
     guidelines?: { general: string | null, project: string | null },
     ragContent?: string | null
   ): Promise<string | null> {
     // Generate enhanced prompt
     const fullPrompt = await enhancePrompt(userRequest, contextPayload, guidelines, ragContent);

     // Rest of the function...
     const requestBody = {
       model: modelName,
       prompt: fullPrompt,
       stream: false
     };
     // ...
   }
   ```

3. Add a command to test the enhanced prompt:
   ```typescript
   const testEnhancedPromptCommand = vscode.commands.registerCommand('x10sion.testEnhancedPrompt', async () => {
     // Get query from user
     const query = await vscode.window.showInputBox({
       prompt: "Enter a query to test prompt enhancement",
       placeHolder: "e.g., Can you read the README.md file?",
       ignoreFocusOut: true
     });

     if (!query) {
       return;
     }

     vscode.window.withProgress({
       location: vscode.ProgressLocation.Notification,
       title: "Generating enhanced prompt...",
       cancellable: false
     }, async (progress) => {
       progress.report({ increment: 0 });

       // Get context from active editor
       const contextInfo = await getActiveEditorContext();

       // Get guidelines
       const guidelines = await readGuidelines();

       // Get relevant content from RAG system
       const ragContent = await ragSystem.retrieveRelevantContent(query);

       // Generate enhanced prompt
       const enhancedPrompt = await enhancePrompt(query, contextInfo || {}, guidelines, ragContent);

       progress.report({ increment: 100 });

       vscode.window.showInformationMessage(`Enhanced prompt generated. Check Debug Console for details.`);
       console.log('Enhanced prompt:', enhancedPrompt);
     });
   });

   context.subscriptions.push(testEnhancedPromptCommand);
   ```

4. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.testEnhancedPrompt",
         "title": "X10sion: Test Enhanced Prompt"
       }
     ]
   }
   ```

**Expected Output**:
- A function to enhance prompts with tool information
- Integration of tool awareness into the prompt enhancement process
- A command to test the enhanced prompt

**Verification**:
1. Run the "X10sion: Test Enhanced Prompt" command
2. Enter a query that might need a tool (e.g., "Can you read the README.md file?")
3. Verify that the enhanced prompt includes tool information and is logged to the Debug Console

## Task 5.6: Implement Human-in-the-Loop System

**Description**: Implement a human-in-the-loop system that allows for human intervention in AI operations, with configurable intervention levels based on action types.

**Steps**:
1. Define interfaces for human-in-the-loop functionality:
   ```typescript
   // Intervention levels for human-in-the-loop
   export enum InterventionLevel {
       NONE = 'none',               // No human intervention required
       NOTIFICATION = 'notification', // Notify human but proceed
       APPROVAL = 'approval',       // Require human approval before proceeding
       GUIDANCE = 'guidance',       // Request human guidance/input
       TAKEOVER = 'takeover'        // Human takes over the task completely
   }

   // Types of actions that might require human intervention
   export enum ActionType {
       CODE_GENERATION = 'code_generation',
       CODE_MODIFICATION = 'code_modification',
       FILE_SYSTEM_ACCESS = 'file_system_access',
       EXTERNAL_API_CALL = 'external_api_call',
       SENSITIVE_DATA_ACCESS = 'sensitive_data_access',
       SECURITY_CRITICAL = 'security_critical',
       RESOURCE_INTENSIVE = 'resource_intensive',
       UNCERTAIN_OUTPUT = 'uncertain_output'
   }

   // Interface for human intervention request
   export interface InterventionRequest {
       id: string;
       timestamp: number;
       actionType: ActionType;
       description: string;
       suggestedAction?: string;
       alternatives?: string[];
       context?: any;
       level: InterventionLevel;
       timeout?: number; // Timeout in milliseconds
   }

   // Interface for human intervention response
   export interface InterventionResponse {
       requestId: string;
       timestamp: number;
       approved: boolean;
       feedback?: string;
       modifiedAction?: string;
       selectedAlternative?: number;
   }

   // Human-in-the-Loop Agent capabilities
   export interface HumanInTheLoopCapabilities {
       defaultInterventionLevel: InterventionLevel;
       actionTypeSettings: Map<ActionType, InterventionLevel>;
       timeoutMs: number;
       allowAutoApprovalForLowRisk: boolean;
       collectFeedback: boolean;
       adaptiveMode: boolean; // Adjust intervention levels based on feedback
   }
   ```

2. Create a human-in-the-loop agent that extends the base agent:
   ```typescript
   export class HumanInTheLoopAgent extends BaseAgent {
       private pendingInterventions: Map<string, InterventionRequest> = new Map();
       private interventionResponses: Map<string, InterventionResponse> = new Map();
       private capabilities: HumanInTheLoopCapabilities;
       private disposables: vscode.Disposable[] = [];

       constructor(
           workerPool: WorkerPool,
           llmMonitor: LLMMonitor,
           llmProvider: LanguageModelProvider,
           capabilities: HumanInTheLoopCapabilities
       ) {
           super(
               'human-in-the-loop',
               'Human-in-the-Loop Agent',
               'Manages human intervention in AI agent operations',
               workerPool,
               llmMonitor,
               llmProvider,
               {}
           );

           this.capabilities = capabilities;
           this.initialize();
       }

       // Initialize the agent
       private initialize(): void {
           // Register commands for human intervention
           this.disposables.push(
               vscode.commands.registerCommand('x10sion.approveAction', async (requestId: string) => {
                   await this.handleApproval(requestId, true);
               }),
               vscode.commands.registerCommand('x10sion.rejectAction', async (requestId: string) => {
                   await this.handleApproval(requestId, false);
               }),
               vscode.commands.registerCommand('x10sion.provideGuidance', async (requestId: string) => {
                   await this.handleGuidance(requestId);
               })
           );
       }

       // Request human intervention for an action
       public async requestIntervention(
           actionType: ActionType,
           description: string,
           suggestedAction?: string,
           alternatives?: string[],
           context?: any,
           level?: InterventionLevel
       ): Promise<InterventionResponse> {
           // Implementation details...
       }

       // Other methods for handling interventions...
   }
   ```

3. Create a configuration provider for the human-in-the-loop agent:
   ```typescript
   export function getDefaultHumanInTheLoopConfig(): HumanInTheLoopCapabilities {
       // Get user settings
       const config = vscode.workspace.getConfiguration('x10sion.humanInTheLoop');

       // Create action type settings map with default values
       const actionTypeSettings = new Map<ActionType, InterventionLevel>();

       // Set default intervention levels for different action types
       actionTypeSettings.set(ActionType.CODE_GENERATION,
           getInterventionLevelFromConfig(config, 'codeGeneration', InterventionLevel.NOTIFICATION));

       actionTypeSettings.set(ActionType.CODE_MODIFICATION,
           getInterventionLevelFromConfig(config, 'codeModification', InterventionLevel.APPROVAL));

       // More action types...

       return {
           defaultInterventionLevel: getInterventionLevelFromConfig(
               config,
               'defaultLevel',
               InterventionLevel.APPROVAL
           ),
           actionTypeSettings,
           timeoutMs: config.get<number>('timeoutMs', 30000), // Default 30 seconds
           allowAutoApprovalForLowRisk: config.get<boolean>('allowAutoApprovalForLowRisk', false),
           collectFeedback: config.get<boolean>('collectFeedback', true),
           adaptiveMode: config.get<boolean>('adaptiveMode', true)
       };
   }
   ```

4. Create a VS Code TreeView for managing human-in-the-loop interactions:
   ```typescript
   export class HumanInTheLoopDataProvider implements vscode.TreeDataProvider<vscode.TreeItem> {
       private _onDidChangeTreeData = new vscode.EventEmitter<vscode.TreeItem | undefined>();
       readonly onDidChangeTreeData = this._onDidChangeTreeData.event;

       private pendingInterventions: Map<string, InterventionRequest> = new Map();
       private completedInterventions: Map<string, { request: InterventionRequest, response: InterventionResponse }> = new Map();

       // Methods for managing interventions and providing tree data...
   }

   export function registerHumanInTheLoopView(context: vscode.ExtensionContext): HumanInTheLoopDataProvider {
       const dataProvider = new HumanInTheLoopDataProvider();

       // Register tree view
       const treeView = vscode.window.createTreeView('x10sionHumanInTheLoopView', {
           treeDataProvider: dataProvider,
           showCollapseAll: true
       });

       // Register commands
       context.subscriptions.push(
           vscode.commands.registerCommand('x10sion.respondToIntervention', async (requestId: string) => {
               // Implementation details...
           }),

           vscode.commands.registerCommand('x10sion.clearCompletedInterventions', () => {
               dataProvider.clearCompletedInterventions();
           }),

           treeView
       );

       return dataProvider;
   }
   ```

5. Update the package.json file to include the necessary configurations and view contributions:
   ```json
   "contributes": {
       "configuration": {
           "title": "X10sion",
           "properties": {
               "x10sion.humanInTheLoop.defaultLevel": {
                   "type": "string",
                   "enum": ["none", "notification", "approval", "guidance", "takeover"],
                   "default": "approval",
                   "description": "Default intervention level for AI actions"
               },
               // More configuration properties...
           }
       },
       "views": {
           "x10sion": [
               {
                   "id": "x10sionHumanInTheLoopView",
                   "name": "Human-in-the-Loop",
                   "icon": "$(person)",
                   "contextualTitle": "Human-in-the-Loop"
               }
           ]
       },
       "commands": [
           {
               "command": "x10sion.approveAction",
               "title": "X10sion: Approve AI Action",
               "icon": "$(check)"
           },
           // More commands...
       ]
   }
   ```

6. Update the extension.ts file to initialize the human-in-the-loop agent:
   ```typescript
   export function activate(context: vscode.ExtensionContext) {
       // Initialize core services
       const llmMonitor = new LLMMonitor();
       const workerPool = new WorkerPool();
       const llmProvider = {} as LanguageModelProvider; // Placeholder

       // Initialize human-in-the-loop agent
       const humanInTheLoopConfig = getDefaultHumanInTheLoopConfig();
       const humanInTheLoopAgent = new HumanInTheLoopAgent(
           workerPool,
           llmMonitor,
           llmProvider,
           humanInTheLoopConfig
       );

       // Create providers
       const humanInTheLoopViewProvider = registerHumanInTheLoopView(context);

       // Register commands for human-in-the-loop functionality
       // Implementation details...
   }
   ```

7. Create documentation for the human-in-the-loop functionality:
   ```markdown
   # Human-in-the-Loop AI in X10sion

   ## Overview

   The Human-in-the-Loop (HITL) system in X10sion provides a framework for human intervention in AI agent operations. This feature ensures that AI actions can be monitored, approved, guided, or taken over by humans when necessary, creating a collaborative environment between AI and human developers.

   ## Key Features

   ### 1. Multi-Level Intervention

   The HITL system supports five levels of intervention:

   - **None**: No human intervention required, AI proceeds autonomously
   - **Notification**: Human is notified of AI actions but they proceed automatically
   - **Approval**: Human approval is required before AI actions are executed
   - **Guidance**: Human provides guidance or input to direct AI actions
   - **Takeover**: Human takes complete control of the task

   // More documentation...
   ```

**Expected Output**:
- A human-in-the-loop agent that manages intervention requests and responses
- A configuration system for setting intervention levels based on action types
- A VS Code TreeView for managing human-in-the-loop interactions
- Commands for approving, rejecting, and providing guidance for AI actions
- Documentation for the human-in-the-loop functionality

**Verification**:
1. Run the "X10sion: Test Human-in-the-Loop" command
2. Verify that an intervention request is created and displayed in the Human-in-the-Loop view
3. Approve, reject, or provide guidance for the intervention request
4. Verify that the intervention is completed and the response is displayed in the view

## Completion Criteria

Phase 5 is considered complete when:
- The extension has a file system watcher that monitors changes to markdown files
- The extension can extract top-level function and class names from code
- The extension can chunk code at function/class boundaries for RAG
- The extension has a tool registry and a "read_file_content" tool
- The prompt enhancement agent is aware of available tools and includes tool usage instructions in the prompt when needed
- The extension has a human-in-the-loop system that allows for human intervention in AI operations
