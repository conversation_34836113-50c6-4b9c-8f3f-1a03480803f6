# Development Phase 5: Smarter Context & Agentic Foundations

## Overview

Phase 5 focuses on implementing smarter context gathering and laying the foundations for agentic capabilities. This includes monitoring changes to files, using tree-sitter for better code understanding, and defining a structure for tools that the AI can use.

## Goals

- Implement a file system watcher to monitor changes to markdown files
- Integrate tree-sitter for better code understanding
- Implement code chunking based on function/class boundaries
- Define a structure for tools
- Improve the prompt enhancement agent to be aware of available tools
- Implement human-in-the-loop capabilities for AI operations

## Reference Materials

### Internal References
- [Project Structure](../../../dirStructure.md)
- [File Relationships](../../../fileRelations.md)
- [Background Worker System](../../../knowledge_base/architecture/background_worker_system.md)
- [AI Agent Security Best Practices](../../../knowledge_base/security/ai_agent_security_best_practices.md)
- [RAG Implementation Best Practices](../../../knowledge_base/best_practices/rag_implementation_best_practices.md)
- [Test Method](../../../testMethod.md)

### External References (May 2025)
- [VS Code FileSystemWatcher API](https://code.visualstudio.com/api/references/vscode-api#FileSystemWatcher)
- [Tree-sitter Documentation](https://tree-sitter.github.io/tree-sitter/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Function Calling in LLMs](https://platform.openai.com/docs/guides/function-calling) - For tool interfaces
- [OWASP Top 10 for LLM Applications (2025)](https://strobes.co/blog/owasp-top-10-risk-mitigations-for-llms-and-gen-ai-apps-2025/) - For security best practices
- [IEEE Standards for Human-AI Collaboration (2024)](https://standards.ieee.org/standard/2846-2024.html) - For human-in-the-loop best practices
- [ACM Guidelines for Responsible AI Development (2025)](https://www.acm.org/code-of-ethics) - For ethical AI development

## Technical Details

### File System Watcher

VS Code provides the `vscode.workspace.createFileSystemWatcher` API to monitor changes to files. This will be used to detect changes to markdown files and re-index them for the RAG system.

### Tree-sitter Integration

Tree-sitter is a parser generator tool and an incremental parsing library. It can be used to parse code into an abstract syntax tree (AST), which provides a better understanding of the code structure. This will be used to extract function and class names, and to chunk code at function/class boundaries.

### Tool Definition

A simple structure for tools will be defined, including the tool name, purpose, and input schema. This will be used to define tools that the AI can use to perform actions, such as reading file content.

### Prompt Enhancement Agent

The prompt enhancement agent will be improved to be aware of available tools and include tool usage instructions in the prompt to the main LLM if the user's query suggests a tool might be needed.

### Human-in-the-Loop System

A human-in-the-loop system will be implemented to allow for human intervention in AI operations. This system will support multiple levels of intervention, from simple notifications to full human takeover, and will be configurable based on the type of action being performed. The system will include a VS Code TreeView for managing intervention requests and responses.

## Implementation Steps

1. Implement a file system watcher to monitor changes to markdown files
2. Integrate tree-sitter for better code understanding
3. Implement code chunking based on function/class boundaries
4. Define a structure for tools
5. Improve the prompt enhancement agent to be aware of available tools
6. Implement a human-in-the-loop system for AI operations

## Testing

Each implementation step can be tested by:

1. Making changes to markdown files and verifying that they are re-indexed
2. Opening files in different languages and verifying that function and class names are extracted
3. Verifying that code is chunked at function/class boundaries
4. Defining test tools and verifying that they can be used
5. Testing the prompt enhancement agent with queries that suggest tool usage
6. Testing the human-in-the-loop system with different intervention levels and action types

## Next Steps

After completing Phase 5, the project will move on to [Phase 6: Parallel Processing & MCP Foundation](../devPhase6/devPhase6.md), which focuses on implementing parallel processing capabilities and laying the foundation for the Model Context Protocol (MCP).

For detailed tasks in this phase, refer to [Phase 5 Tasks](./devPhase5_tasks.md).

## Resource Considerations

Phase 5 has increased resource requirements due to the tree-sitter integration, file system watcher, and human-in-the-loop system:
- Memory usage: ~70MB (increase from Phase 4 due to tree-sitter, AST storage, and human-in-the-loop components)
- CPU usage: Moderate to high, especially during code parsing, AST generation, and human intervention processing
- Context window: The smarter context gathering is designed to be efficient for LLMs with 4K context windows
- VRAM requirements: Compatible with 8GB VRAM GPUs, with optimizations for AST operations
- UI resources: The human-in-the-loop system requires additional UI resources for the TreeView and intervention dialogs

## Test Results

For test results of this phase, see [Phase 5 Test Results](../../../testResults/phase5/phase5_full_testResult.md).

## Timestamp

Last updated: May 19, 2025
