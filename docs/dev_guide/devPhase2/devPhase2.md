# Development Phase 2: Basic Local LLM Interaction (via Ollama)

## Overview

Phase 2 focuses on implementing basic interaction with a local LLM via Ollama. This includes sending prompts to Ollama, parsing the responses, enhancing prompts with context, and securely storing configuration.

## Goals

- Implement communication with Ollama API
- Parse and display LLM responses
- Enhance prompts with context and user input
- Securely store Ollama configuration

## Reference Materials

### Internal References
- [Project Structure](../../../dirStructure.md)
- [File Relationships](../../../fileRelations.md)
- [Ollama Integration Best Practices](../../../knowledge_base/best_practices/ollama_integration_best_practices.md)
- [Ollama Security Best Practices](../../../knowledge_base/security/ollama_security_best_practices.md)
- [JavaScript Patterns](../../../knowledge_base/code_patterns/javascript_patterns.md)
- [Test Method](../../../testMethod.md)

### External References (May 2025)
- [Ollama API Documentation](https://github.com/ollama/ollama/blob/main/docs/api.md)
- [VS Code Extension API Documentation](https://code.visualstudio.com/api/references/vscode-api)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Node.js Fetch API Documentation](https://nodejs.org/api/fetch.html)

## Technical Details

### Ollama API

Ollama provides a simple API for interacting with local LLMs. The main endpoint for generating text is:

```
POST http://localhost:11434/api/generate
```

The request body should include:
- `model`: The name of the model to use (e.g., `gemma3:4b-it-q4_K_M`)
- `prompt`: The prompt to send to the model
- `stream`: Whether to stream the response (we'll use `false` for simplicity)

### HTTP Requests

We'll use the `node-fetch` library to make HTTP requests to the Ollama API. This library provides a simple interface for making HTTP requests and handling responses.

### Prompt Enhancement

We'll create a function that takes the user's raw input and the gathered context from Phase 1, then wraps it in a predefined prompt template before sending it to Ollama. This will help the LLM understand the context and provide more relevant responses.

### Secure Storage

VS Code provides two mechanisms for storing configuration:
- `vscode.workspace.getConfiguration()` for regular settings
- `vscode.SecretStorage` for sensitive information

We'll use these to securely store the Ollama URL and other configuration.

## Implementation Steps

1. Implement a function to send prompts to Ollama and handle responses
2. Parse Ollama responses and display them to the user
3. Enhance prompts with context and user input
4. Implement secure storage for Ollama configuration

## Testing

Each implementation step can be tested by:

1. Running the command with appropriate input
2. Verifying that the expected output is displayed or logged
3. Checking error handling for various failure scenarios

## Next Steps

After completing Phase 2, the project will move on to [Phase 3: Basic UI (Native Chat View) & Contextual Guidelines](../devPhase3/devPhase3.md), which focuses on creating a basic UI with native VS Code components and implementing contextual guidelines.

For detailed tasks in this phase, refer to [Phase 2 Tasks](./devPhase2_tasks.md).

## Resource Considerations

Phase 2 has minimal additional resource requirements:
- Memory usage: ~25MB (slight increase from Phase 1 due to Ollama client)
- CPU usage: Negligible for small prompts, increases with prompt size and response length
- Context window: The prompt enhancement is designed to be efficient for LLMs with 4K context windows
- VRAM requirements: Compatible with 8GB VRAM GPUs

## Test Results

For test results of this phase, see [Phase 2 Test Results](../../../testResults/phase2/phase2_full_testResult.md).

## Timestamp

Last updated: May 19, 2025
