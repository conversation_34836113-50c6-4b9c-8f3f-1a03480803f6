# Development Phase 2 Tasks

This document outlines the specific tasks for Phase 2 of the X10sion project.

## Task 2.1: Implement Ollama API Communication

**Description**: Implement a function to send a hardcoded prompt and the context JSON (from Task 1.4) to a local Ollama API endpoint.

**Steps**:
1. Add `node-fetch` as a dependency:
   ```bash
   npm install node-fetch
   ```
2. Create a function to send prompts to Ollama:
   ```typescript
   async function sendToOllama(promptText: string, contextPayload: any) {
     const ollamaEndpoint = 'http://127.0.0.1:11434/api/generate';
     const modelName = 'gemma3:4b-it-q4_K_M'; // or another suitable model
     const fullPrompt = `Context: ${JSON.stringify(contextPayload, null, 2)}
     
     User Request: ${promptText}
     
     Response:`;
     
     const requestBody = {
       model: modelName,
       prompt: fullPrompt,
       stream: false
     };
     
     try {
       const response = await fetch(ollamaEndpoint, {
         method: 'POST',
         body: JSON.stringify(requestBody),
         headers: { 'Content-Type': 'application/json' }
       });
       
       if (!response.ok) {
         throw new Error(`Status: ${response.status} ${response.statusText}`);
       }
       
       const responseData = await response.json();
       console.log('Ollama Response:', responseData);
       return responseData;
     } catch (error) {
       console.error('Error calling Ollama:', error);
       return null;
     }
   }
   ```
3. Update the command to call this function with a hardcoded prompt and the context from Task 1.4

**Expected Output**:
- Successful API call: Ollama response logged to the console
- Failed API call: Error logged to the console

**Verification**:
1. Ensure Ollama is running locally with the specified model
2. Open a file in VS Code
3. Run the command
4. Check the Debug Console to see the Ollama response or error

## Task 2.2: Parse and Display Ollama Response

**Description**: Parse the Ollama response and display the generated text in a VS Code notification or output panel.

**Steps**:
1. Define an interface for the Ollama response:
   ```typescript
   interface OllamaResponse {
     response: string;
     // Add other fields as needed
   }
   ```
2. Update the `sendToOllama` function to parse the response:
   ```typescript
   const responseData = await response.json() as OllamaResponse;
   console.log('Ollama Response:', responseData);
   return responseData.response;
   ```
3. Update the command to display the response:
   ```typescript
   const aiResponseText = await sendToOllama(promptText, contextInfo);
   if (typeof aiResponseText === 'string') {
     vscode.window.showInformationMessage(`AI Says: ${aiResponseText}`);
   } else {
     vscode.window.showErrorMessage('Failed to get response from Ollama. Check Debug Console.');
   }
   ```

**Expected Output**:
- Successful API call: AI response displayed in a notification
- Failed API call: Error message displayed in a notification

**Verification**:
1. Ensure Ollama is running locally with the specified model
2. Open a file in VS Code
3. Run the command
4. Verify that the AI response is displayed in a notification

## Task 2.3: Implement Prompt Enhancement

**Description**: Create a function that takes the user's raw input and the gathered context, then wraps it in a predefined prompt template before sending to Ollama.

**Steps**:
1. Define an interface for the editor context:
   ```typescript
   interface EditorContext {
     filePath: string | null;
     languageId: string | null;
     selectedText: string | null;
     fileContent: string | null;
   }
   ```
2. Update the `getActiveEditorContext` function to return this interface:
   ```typescript
   async function getActiveEditorContext(): Promise<EditorContext | null> {
     // Existing implementation, but with the new return type
   }
   ```
3. Update the `sendToOllama` function to use a better prompt template:
   ```typescript
   async function sendToOllama(userRequest: string, contextPayload: EditorContext): Promise<string | null> {
     // ...
     const fullPrompt = `System: You are a helpful AI coding assistant. Your task is to respond to the user's request based on the provided context. Provide concise and accurate answers. Do not output the code from the context unless explicitly asked to.
     
     Context from editor:
     ---
     ${JSON.stringify(contextPayload, null, 2)}
     ---
     
     User Request: ${userRequest}
     
     AI Response:`;
     // ...
   }
   ```
4. Add user input to the command:
   ```typescript
   const userQuery = await vscode.window.showInputBox({
     prompt: "What would you like to ask the AI about the current file/selection?",
     placeHolder: "e.g., Explain this code, suggest improvements, write documentation...",
     ignoreFocusOut: true,
   });
   
   if (userQuery === undefined || userQuery.trim() === "") {
     return;
   }
   
   const aiResponseText = await sendToOllama(userQuery, contextInfo);
   ```
5. Add progress indication:
   ```typescript
   vscode.window.withProgress({
     location: vscode.ProgressLocation.Notification,
     title: "X10sion: Asking AI...",
     cancellable: false
   }, async (progress) => {
     // Existing logic
   });
   ```

**Expected Output**:
- Input box for user query
- Progress notification while waiting for response
- AI response displayed in a notification

**Verification**:
1. Ensure Ollama is running locally with the specified model
2. Open a file in VS Code
3. Run the command
4. Enter a query in the input box
5. Verify that a progress notification is shown
6. Verify that the AI response is displayed in a notification

## Task 2.4: Implement Secure Storage for Ollama URL

**Description**: Implement secure storage for Ollama URL using `vscode.SecretStorage` or extension settings.

**Steps**:
1. Add configuration to `package.json`:
   ```json
   "contributes": {
     "configuration": {
       "title": "X10sion",
       "properties": {
         "x10sion.ollamaUrl": {
           "type": "string",
           "default": "http://127.0.0.1:11434",
           "description": "URL of the Ollama API endpoint"
         },
         "x10sion.defaultModel": {
           "type": "string",
           "default": "gemma3:4b-it-q4_K_M",
           "description": "Default model to use for Ollama requests"
         }
       }
     }
   }
   ```
2. Update the `sendToOllama` function to use the configuration:
   ```typescript
   async function sendToOllama(userRequest: string, contextPayload: EditorContext): Promise<string | null> {
     const config = vscode.workspace.getConfiguration('x10sion');
     const ollamaBaseUrl = config.get<string>('ollamaUrl') || 'http://127.0.0.1:11434';
     const ollamaEndpoint = `${ollamaBaseUrl}/api/generate`;
     const modelName = config.get<string>('defaultModel') || 'gemma3:4b-it-q4_K_M';
     // ...
   }
   ```
3. Add a command to set the Ollama URL:
   ```typescript
   const setOllamaUrlCommand = vscode.commands.registerCommand('x10sion.setOllamaUrl', async () => {
     const config = vscode.workspace.getConfiguration('x10sion');
     const currentUrl = config.get<string>('ollamaUrl') || 'http://127.0.0.1:11434';
     
     const newUrl = await vscode.window.showInputBox({
       prompt: "Enter the Ollama API URL",
       value: currentUrl,
       placeHolder: "e.g., http://127.0.0.1:11434",
       ignoreFocusOut: true,
     });
     
     if (newUrl !== undefined && newUrl.trim() !== "") {
       await config.update('ollamaUrl', newUrl, vscode.ConfigurationTarget.Global);
       vscode.window.showInformationMessage(`Ollama URL updated to: ${newUrl}`);
     }
   });
   
   context.subscriptions.push(setOllamaUrlCommand);
   ```
4. Register the command in `package.json`:
   ```json
   "contributes": {
     "commands": [
       {
         "command": "x10sion.setOllamaUrl",
         "title": "X10sion: Set Ollama API URL"
       }
     ]
   }
   ```

**Expected Output**:
- Command to set Ollama URL
- Configuration stored in VS Code settings
- Ollama URL used in API requests

**Verification**:
1. Run the "X10sion: Set Ollama API URL" command
2. Enter a new URL
3. Verify that the URL is updated in the settings
4. Run the main command to verify that the new URL is used

## Completion Criteria

Phase 2 is considered complete when:
- The extension can send prompts to Ollama and receive responses
- The extension can parse and display Ollama responses
- The extension can enhance prompts with context and user input
- The extension can securely store and use Ollama configuration
- All error cases are handled appropriately
