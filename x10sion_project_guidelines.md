# X10sion Project Guidelines

This document provides project-specific guidelines for the X10sion AI assistant to follow when responding to user queries about this project.

## Project Overview

X10sion is a local-first AI coding co-pilot for VS Code that aims to provide intelligent assistance while respecting user privacy and system resources. The extension uses local LLMs via Ollama to provide context-aware coding assistance.

## Project Architecture

### Core Components
- **Extension Entry Point**: `src/extension.ts` - Registers commands and activates the extension
- **Editor Context**: Gathers information from the active editor
- **LLM Interaction**: Communicates with Ollama for AI responses
- **UI Components**: Provides user interface for interaction
- **RAG System**: Retrieves relevant information from documentation
- **MCP Server/Client**: Implements Model Context Protocol for standardized AI interaction
- **AI Agents**: Specialized agents for various software development tasks
- **Parallel Processing**: Worker thread pool for CPU-intensive tasks
- **Real-time Monitoring**: System to detect and address issues with LLM outputs

### Development Phases
The project follows a granular, phased approach:
1. **Phase 0**: Project setup and basic "Hello World"
2. **Phase 1**: Core editor context gathering
3. **Phase 2**: Basic local LLM interaction via Ollama
4. **Phase 3**: Basic UI and contextual guidelines
5. **Phase 4**: Rudimentary local RAG
6. **Phase 5**: Smarter context and agentic foundations
7. **Phase 6**: MCP and AI Agents implementation

## Coding Standards

### TypeScript Guidelines
- Use TypeScript's type system effectively
- Define interfaces for data structures
- Use async/await for asynchronous operations
- Follow the TypeScript style guide

### VS Code Extension Guidelines
- Use VS Code's extension API appropriately
- Handle disposables correctly to prevent memory leaks
- Use VS Code's built-in UI components when possible
- Follow VS Code's extension guidelines

### Project-Specific Patterns
- Use dependency injection for testability
- Separate concerns between UI, context gathering, and LLM interaction
- Use progressive enhancement to add features in phases
- Prioritize performance and resource efficiency

## Resource Considerations

### Target Environment
- The extension should work well on systems with limited resources
- Target 8GB VRAM for LLM operations
- Use quantized models (q4) for better performance
- Optimize token usage to stay within 4k context windows

### Performance Guidelines
- Avoid blocking the UI thread
- Use debouncing for file system operations
- Implement efficient chunking for RAG
- Monitor and log token usage

## Optimization Techniques

### Lazy Loading
- Defer loading of non-critical components until needed
- Use specific activation events in package.json
- Implement dynamic imports for feature modules
- Lazy register commands and views

### Just-in-Time Initialization
- Initialize components only when needed
- Dispose of unused components after a period of inactivity
- Implement resource pooling for frequently used components
- Use tiered initialization for progressive enhancement

### Memory Optimization
- Use efficient data structures to minimize memory footprint
- Implement caching with TTL (Time To Live) for frequently accessed data
- Avoid unnecessary object creation and cloning
- Use WeakMap and WeakSet for object references

### Incremental Processing
- Break large tasks into smaller chunks
- Process chunks asynchronously to avoid blocking the UI thread
- Implement cancellation for long-running tasks
- Provide progress indicators for user feedback

### Resource Efficiency
- Monitor CPU and memory usage to adjust behavior
- Implement backoff strategies for resource-intensive tasks
- Schedule non-critical tasks during idle periods
- Use worker threads for CPU-intensive operations

## Testing Strategy

### Test Coverage
- Unit tests for core functionality
- Integration tests for LLM interaction
- UI tests for webview components
- Resource constraint testing

### Test Documentation
- Document test results in the `testResults/` directory
- Organize test results by development phase
- Include performance metrics in test results

## Documentation Standards

### Code Documentation
- Use JSDoc comments for functions and classes
- Document parameters, return values, and exceptions
- Explain complex logic with inline comments

### User Documentation
- Provide clear installation instructions
- Document configuration options
- Include examples of common use cases
- Explain limitations and requirements

## Contribution Guidelines

### Pull Requests
- Follow the project's development phases
- Include tests for new functionality
- Update documentation as needed
- Respect resource constraints

### Issue Reporting
- Provide clear steps to reproduce issues
- Include relevant context information
- Specify the environment details

## Security Considerations

### Data Security
- **Local-First**: All processing happens locally, no data is sent to external servers
- **Privacy**: User code and context never leaves their machine
- **Resource Awareness**: Optimized for low-resource environments

### Security Best Practices
- **Ollama Security**: Ensure Ollama API is never exposed directly to the internet (CVE-2024-37032)
- **Authentication**: Implement proper authentication for any exposed services
- **Secure MCP Implementation**: Follow MCP security best practices for resources, tools, and agents
- **Input Validation**: Validate all inputs to prevent injection attacks
- **Sandboxing**: Isolate AI agent execution to prevent unauthorized access
- **Regular Updates**: Keep dependencies updated to address security vulnerabilities

## Background Worker System

### Just-in-Time Workers
- Initialize workers only when needed to minimize resource usage
- Terminate idle workers after a period of inactivity
- Use worker pools for efficient task distribution
- Monitor worker performance and resource usage

### Task Prioritization
- Prioritize tasks based on importance and resource availability
- Defer low-priority tasks when system resources are constrained
- Schedule documentation updates during idle periods
- Ensure critical tasks are executed promptly

### Resource Monitoring
- Monitor CPU and memory usage to optimize worker behavior
- Adjust worker count based on available resources
- Implement backoff strategies for resource-intensive tasks
- Provide feedback on resource usage to the user

## AI Agent File Management

### File Creation Guidelines
- Verify file existence before creating new files
- Check for similar files to prevent duplication
- Use templates for consistent file structure
- Register new files in a central registry

### Content Management
- Prefer incremental updates over complete rewrites
- Validate content against established rules
- Add timestamps to track changes
- Maintain change logs for significant modifications

### Terminal Monitoring
- Capture terminal output in real-time
- Analyze output for errors and warnings
- Provide context-aware assistance based on terminal output
- Monitor task execution and capture results

### Documentation Maintenance
- Automatically update dirStructure.md with timestamps
- Keep fileRelations.md synchronized with codebase changes
- Use background workers for documentation updates
- Perform updates during idle periods to minimize impact

## Dependency Management

### Update Strategy
- Schedule regular dependency updates (monthly)
- Use `npm-check-updates` (ncu) for automated updates
- Prioritize security-related updates
- Test thoroughly after updates

### Update Process
1. Check for updates: `ncu`
2. Update dependencies: `ncu -u`
3. Install updated dependencies: `npm install`
4. Run tests: `npm test`
5. Fix any issues
6. Commit changes with detailed message

### Security Considerations
- Scan dependencies for vulnerabilities: `npm audit`
- Review new dependencies before adding them
- Minimize the number of dependencies
- Document dependency decisions

## Versioning and Releases

### Semantic Versioning
- Follow semantic versioning (MAJOR.MINOR.PATCH)
- Document changes in CHANGELOG.md
- Tag releases in the repository

### Release Process
- Test thoroughly before release
- Update documentation for new features
- Verify compatibility with different VS Code versions
- Conduct security review before each release
- Ensure all dependencies are up-to-date and secure

## Current AGI-Like Implementation

### Current AGI-Like Architecture

- **Modular Design**: Implemented using VS Code's extension API and TypeScript interfaces
- **Progressive Enhancement**: Implemented using feature detection and capability tiers
- **Safety First**: Implemented using content policies and execution boundaries
- **User Control**: Implemented using confirmation dialogs and action previews
- **Ethical Considerations**: Implemented using ethical frameworks and guidelines

### Current Implementation Roadmap

1. **Foundation Phase (Completed)**: Implemented agent framework, MCP, parallel processing, and monitoring
2. **Enhanced Capabilities Phase (Current)**: Implementing agent memory, MCP resources/tools, parallel processing optimization
3. **Advanced Integration Phase (Next 3-6 Months)**: Implementing multi-agent collaboration, contextual understanding
4. **Comprehensive AGI-Like System (Next 6-12 Months)**: Implementing autonomous workflows, human-agent collaboration

### Current Safety Implementation

- **Intent Verification**: Implemented using confirmation dialogs and action previews
- **Explainable Actions**: Implemented using detailed explanations and reasoning traces
- **Reversible Operations**: Implemented using undo functionality and operation history
- **Sandboxed Execution**: Implemented using isolated execution environments
- **Output Validation**: Implemented using content filtering and validation rules
- **Monitoring**: Implemented using statistical methods and pattern recognition
- **Intervention Mechanisms**: Implemented using automated checks and human-in-the-loop options

### Multi-Agent Implementation

- **Specialized Agent Teams**: Implemented using multiple specialized agents with defined roles
- **Role-Based Collaboration**: Implemented using agent role definitions and coordination
- **Consensus Mechanisms**: Implemented using voting and conflict resolution algorithms
- **Collaborative Problem Solving**: Implemented using sequential and parallel agent execution
- **Human-Agent Teams**: Implemented using interactive workflows and feedback mechanisms

## Timestamp

Last updated: May 19, 2025
