# X10sion Testing Methodology

*Last Updated: May 25, 2025*

This document outlines the testing methodology for the X10sion project, providing guidelines on how to test each phase and feature, what to expect, and where to store test results. This methodology follows May 2025 best practices for AI agent testing and resource-constrained environments.

## General Testing Principles

### Test Early, Test Often

- Write tests alongside code, not after
- Run tests after each significant change
- Automate tests where possible

### Test Types

1. **Unit Tests**: Test individual functions and components in isolation
2. **Integration Tests**: Test interactions between components
3. **End-to-End Tests**: Test the complete user flow
4. **Manual Tests**: Test user experience and edge cases

### Resource Constraint Testing

Since X10sion targets systems with limited resources (8GB VRAM, 4K context window), all tests should be run in a constrained environment:

- Use quantized models (q4) for testing
- Monitor token usage to ensure it stays within limits
- Test performance on lower-end hardware

## Test Organization

### Directory Structure

Tests should be organized in the following directory structure:

```
x10sion/
├── src/
│   └── test/                      # Unit and integration tests
│       ├── suite/                 # Test suites
│       │   ├── extension.test.ts  # Extension tests
│       │   ├── editor.test.ts     # Editor context tests
│       │   ├── ollama.test.ts     # Ollama API tests
│       │   ├── mcp/               # MCP tests
│       │   │   ├── server.test.ts # MCP server tests
│       │   │   └── client.test.ts # MCP client tests
│       │   ├── agents/            # Agent tests
│       │   │   ├── base-agent.test.ts # Base agent tests
│       │   │   └── ...            # Specific agent tests
│       │   ├── parallel/          # Parallel processing tests
│       │   │   ├── worker-pool.test.ts # Worker pool tests
│       │   │   └── background-worker.test.ts # Background worker tests
│       │   ├── file-management/   # File management tests
│       │   │   ├── file-registry.test.ts # File registry tests
│       │   │   └── content-manager.test.ts # Content manager tests
│       │   ├── monitoring/        # Monitoring tests
│       │   │   ├── llm-monitor.test.ts # LLM monitor tests
│       │   │   └── terminal-monitor.test.ts # Terminal monitor tests
│       │   ├── optimization/      # Optimization tests
│       │   │   ├── lazy-loader.test.ts # Lazy loading tests
│       │   │   ├── memory-optimizer.test.ts # Memory optimization tests
│       │   │   └── incremental-processor.test.ts # Incremental processing tests
│       │   └── ...                # Other test files
│       └── runTest.ts             # Test runner
├── knowledge_base/
│   ├── examples/                  # Example files for testing and documentation
│   │   ├── example_queries.md     # Example queries for testing
│   │   └── example_responses.md   # Example expected responses
│   └── ...                        # Other knowledge base files
├── testResults/                   # Test results
│   ├── phase0/                    # Phase 0 test results
│   │   ├── phase0_task1_testResult.md  # Task 1 test results
│   │   └── phase0_full_testResult.md   # Full phase test results
│   ├── phase1/                    # Phase 1 test results
│   │   ├── phase1_task1_testResult.md  # Task 1 test results
│   │   └── phase1_full_testResult.md   # Full phase test results
│   ├── ...                        # Phases 2-13 test results
│   ├── phase14/                   # Phase 14 test results (Configuration Layer)
│   │   └── phase14_full_testResult.md  # Full phase test results
│   ├── phase15/                   # Phase 15 test results (User Onboarding & LLM Recommendation)
│   │   └── phase15_full_testResult.md  # Full phase test results
│   ├── phase16/                   # Phase 16 test results (Advanced Security & Compliance)
│   │   └── phase16_full_testResult.md  # Full phase test results
│   └── phase17/                   # Phase 17 test results (Ethical Information Gathering)
│       └── phase17_full_testResult.md  # Full phase test results
```

### Test Result Documentation

For each task, create a test result document in the corresponding phase directory:

```markdown
# Task X.Y Test Results

## Test Environment
- OS: [OS name and version]
- VS Code: [VS Code version]
- Ollama: [Ollama version]
- Model: [Model name and quantization]
- Hardware: [CPU, RAM, GPU, VRAM]

## Test Cases
1. [Test case description]
   - Expected: [Expected result]
   - Actual: [Actual result]
   - Status: [PASS/FAIL]
   - Notes: [Any notes or observations]

## Issues Found
- [Issue description]
  - Severity: [HIGH/MEDIUM/LOW]
  - Steps to reproduce: [Steps]
  - Possible solution: [Solution]

## Performance Metrics
- Token usage: [Number of tokens]
- Response time: [Time in seconds]
- Memory usage: [Memory in MB]
- CPU usage: [CPU percentage]

## Optimization Metrics
- Initial load time: [Time in milliseconds]
- Initial memory footprint: [Memory in MB]
- Memory usage over time: [Graph or description]
- UI responsiveness during tasks: [Description]
- Background worker efficiency: [Description]

## Conclusion
[Overall assessment of the task]
```

## Phase-Specific Testing Guidelines

### Phase 0: Project Setup & "Hello World"

**What to Test**:
- Extension activation
- Command registration
- Command execution
- Notification display

**Test Methods**:
- Run the extension in a new VS Code window
- Execute the command from the Command Palette
- Verify that the notification is displayed

**Expected Results**:
- Extension activates without errors
- Command is registered and appears in the Command Palette
- Notification is displayed with the expected message

**Test Results Location**: `testResults/phase0/phase0_full_testResult.md`

### Phase 1: Core Editor Context Gathering

**What to Test**:
- Selection retrieval
- File content retrieval
- File metadata retrieval
- Context assembly

**Test Methods**:
- Open a file and select text
- Run the command
- Check the debug console for the logged context
- Test with different file types and selections

**Expected Results**:
- Selected text is correctly retrieved
- File content is correctly retrieved
- File metadata is correctly retrieved
- Context is correctly assembled into a JSON object

**Test Results Location**: `testResults/phase1/phase1_full_testResult.md`

### Phase 2: Basic Local LLM Interaction

**What to Test**:
- Ollama API communication
- Response parsing
- Prompt enhancement
- Configuration storage

**Test Methods**:
- Ensure Ollama is running with a supported model
- Run the command with different inputs
- Check the response in the notification
- Test error handling by intentionally causing errors

**Expected Results**:
- Ollama API requests are sent correctly
- Responses are parsed and displayed correctly
- Enhanced prompts improve response quality
- Configuration is stored securely

**Test Results Location**: `testResults/phase2/phase2_full_testResult.md`

### Phase 3: Basic UI & Contextual Guidelines

**What to Test**:
- Native chat view creation
- Command-based communication
- Guideline file reading
- Guideline integration

**Test Methods**:
- Open the chat view
- Send messages using commands and check responses
- Create guideline files and verify they are read
- Check that guidelines influence responses

**Expected Results**:
- Native chat view opens and displays correctly
- Messages are sent and responses are received
- Guideline files are read correctly
- Guidelines influence the AI's responses

**Test Results Location**: `testResults/phase3/phase3_full_testResult.md`

### Phase 4: Rudimentary Local RAG

**What to Test**:
- Markdown file indexing
- Embedding generation
- Similarity search
- RAG integration

**Test Methods**:
- Create test markdown files
- Run indexing and check chunks
- Generate embeddings and check results
- Run queries and check retrieved chunks
- Check that RAG content influences responses

**Expected Results**:
- Markdown files are indexed correctly
- Embeddings are generated correctly
- Relevant chunks are retrieved for queries
- RAG content influences the AI's responses

**Test Results Location**: `testResults/phase4/phase4_full_testResult.md`

### Phase 5: Smarter Context & Agentic Foundations

**What to Test**:
- File system watcher
- Tree-sitter integration
- Code chunking
- Tool definition
- Prompt enhancement

**Test Methods**:
- Modify markdown files and check re-indexing
- Open code files and check symbol extraction
- Check code chunking results
- Test tool execution
- Check enhanced prompts with tool awareness

**Expected Results**:
- File changes trigger re-indexing
- Symbols are extracted correctly
- Code is chunked at function/class boundaries
- Tools are executed correctly
- Enhanced prompts include tool instructions when needed

**Test Results Location**: `testResults/phase5/phase5_full_testResult.md`

### Phase 6: MCP and AI Agents

**What to Test**:
- Parallel processing
- MCP server and client
- Resource, tool, prompt, and agent registration
- AI agent framework
- Core AI agents
- Real-time monitoring
- Terminal output monitoring
- AI agent file management
- Background worker system
- Optimization techniques

**Test Methods**:
- Run CPU-intensive tasks and check parallel execution
- Test MCP server and client communication
- Register and access resources, tools, prompts, and agents
- Create and run agents with different inputs
- Intentionally cause issues to test monitoring
- Test with both small and large LLMs
- Run terminal commands and check output capture
- Create and modify files to test file management
- Test background worker system with documentation updates
- Measure memory usage and performance with optimization techniques

**Expected Results**:
- Tasks are executed in parallel across CPU cores
- MCP server and client communicate correctly
- Resources, tools, prompts, and agents are registered and accessible
- Agents produce correct and helpful outputs
- Monitoring detects and addresses issues
- System works with both small and large LLMs
- Terminal output is captured and analyzed correctly
- Files are managed correctly without duplication
- Background workers update documentation efficiently
- Optimization techniques reduce memory usage and improve performance

**Test Results Location**: `testResults/phase6/phase6_full_testResult.md`

## Token Usage Monitoring

For all tests involving LLM interaction, monitor and log token usage:

1. **Prompt Tokens**: Count tokens in the prompt sent to the LLM
2. **Response Tokens**: Count tokens in the response from the LLM
3. **Total Tokens**: Sum of prompt and response tokens

Use a simple token counting function:

```typescript
function countTokens(text: string): number {
  // Simple approximation: 1 token ≈ 4 characters
  return Math.ceil(text.length / 4);
}
```

For more accurate token counting, consider using a tokenizer library that matches the LLM's tokenization.

## Performance Testing

For each phase, measure and log performance metrics:

1. **Response Time**: Time from sending the request to receiving the response
2. **Memory Usage**: Memory used by the extension
3. **CPU Usage**: CPU used by the extension

Use VS Code's built-in performance tools and Node.js performance APIs:

```typescript
const startTime = performance.now();
// Operation to measure
const endTime = performance.now();
const duration = endTime - startTime;
console.log(`Operation took ${duration}ms`);
```

## Optimization Testing

For testing optimization techniques, use the following approach:

### Lazy Loading Testing

1. **Module Load Time**: Measure the time it takes to load modules with and without lazy loading
2. **Initial Memory Usage**: Measure memory usage at startup with and without lazy loading
3. **Command Registration Time**: Measure the time it takes to register commands with and without lazy registration

```typescript
// Example: Testing lazy loading
const memoryBefore = process.memoryUsage().heapUsed;
// Load module
const memoryAfter = process.memoryUsage().heapUsed;
console.log(`Memory increase: ${(memoryAfter - memoryBefore) / 1024 / 1024} MB`);
```

### Memory Optimization Testing

1. **Memory Usage Over Time**: Monitor memory usage over time with and without memory optimization
2. **Cache Hit Rate**: Measure the cache hit rate for frequently accessed data
3. **Garbage Collection Frequency**: Monitor garbage collection frequency with and without memory optimization

```typescript
// Example: Testing memory optimization
let lastMemoryUsage = process.memoryUsage().heapUsed;
const memoryUsageHistory = [];

// Set up interval to monitor memory usage
const memoryMonitorInterval = setInterval(() => {
  const currentMemoryUsage = process.memoryUsage().heapUsed;
  const delta = currentMemoryUsage - lastMemoryUsage;
  memoryUsageHistory.push({
    timestamp: Date.now(),
    usage: currentMemoryUsage,
    delta
  });
  lastMemoryUsage = currentMemoryUsage;
}, 1000);

// After test, analyze memory usage history
clearInterval(memoryMonitorInterval);
```

### Incremental Processing Testing

1. **UI Responsiveness**: Measure UI responsiveness during large tasks with and without incremental processing
2. **Task Completion Time**: Measure the total time to complete tasks with and without incremental processing
3. **Memory Spikes**: Monitor memory spikes during large tasks with and without incremental processing

```typescript
// Example: Testing incremental processing
const largeData = new Array(10000).fill(0).map((_, i) => ({ id: i, value: `Item ${i}` }));

// Without incremental processing
const startTimeWithout = performance.now();
processLargeDataSet(largeData);
const endTimeWithout = performance.now();
console.log(`Without incremental processing: ${endTimeWithout - startTimeWithout}ms`);

// With incremental processing
const startTimeWith = performance.now();
await processLargeDataSetIncrementally(largeData);
const endTimeWith = performance.now();
console.log(`With incremental processing: ${endTimeWith - startTimeWith}ms`);
```

### Background Worker Testing

1. **Resource Usage**: Measure CPU and memory usage with and without background workers
2. **Task Completion Time**: Measure the time it takes to complete tasks with and without background workers
3. **UI Responsiveness**: Measure UI responsiveness during background tasks

```typescript
// Example: Testing background workers
const startTime = performance.now();
// Run task with background worker
const endTime = performance.now();
console.log(`Task with background worker took ${endTime - startTime}ms`);
```

## Conclusion

Following this testing methodology will ensure that X10sion is thoroughly tested and meets the requirements for running on systems with limited resources. By documenting test results in a structured way, we can track progress and identify issues early in the development process.
