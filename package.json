{"name": "x10sion", "displayName": "x10sion", "description": "local-first AI developer", "version": "0.0.1", "type": "commonjs", "engines": {"vscode": "^1.100.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"configuration": {"title": "X10sion", "properties": {"x10sion.ollamaApiUrl": {"type": "string", "default": "http://localhost:11434", "description": "URL for the Ollama API"}, "x10sion.ollamaModel": {"type": "string", "default": "llama3", "description": "Model to use for Ollama API calls"}, "x10sion.humanInTheLoop.defaultLevel": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "approval", "description": "Default intervention level for AI actions"}, "x10sion.humanInTheLoop.timeoutMs": {"type": "number", "default": 30000, "description": "Timeout in milliseconds for human intervention requests"}, "x10sion.humanInTheLoop.allowAutoApprovalForLowRisk": {"type": "boolean", "default": false, "description": "Automatically approve low-risk actions on timeout"}, "x10sion.humanInTheLoop.collectFeedback": {"type": "boolean", "default": true, "description": "Collect feedback from users on interventions"}, "x10sion.humanInTheLoop.adaptiveMode": {"type": "boolean", "default": true, "description": "Adjust intervention levels based on user feedback"}, "x10sion.humanInTheLoop.interventionLevels.codeGeneration": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "notification", "description": "Intervention level for code generation"}, "x10sion.humanInTheLoop.interventionLevels.codeModification": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "approval", "description": "Intervention level for code modification"}, "x10sion.humanInTheLoop.interventionLevels.fileSystemAccess": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "approval", "description": "Intervention level for file system access"}, "x10sion.humanInTheLoop.interventionLevels.externalApiCall": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "approval", "description": "Intervention level for external API calls"}, "x10sion.humanInTheLoop.interventionLevels.sensitiveDataAccess": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "takeover", "description": "Intervention level for sensitive data access"}, "x10sion.humanInTheLoop.interventionLevels.securityCritical": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "takeover", "description": "Intervention level for security-critical operations"}, "x10sion.humanInTheLoop.interventionLevels.resourceIntensive": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "approval", "description": "Intervention level for resource-intensive operations"}, "x10sion.humanInTheLoop.interventionLevels.uncertainOutput": {"type": "string", "enum": ["none", "notification", "approval", "guidance", "takeover"], "default": "guidance", "description": "Intervention level for uncertain AI outputs"}}}, "commands": [{"command": "x10sion.askAi", "title": "X10sion: Ask AI (with context from active file)"}, {"command": "x10sion.openChat", "title": "X10sion: Open Chat View"}, {"command": "x10sion.sendChatMessage", "title": "X10sion: Send Chat Message"}, {"command": "x10sion.clearChat", "title": "X10sion: Clear Chat", "icon": "$(clear-all)"}, {"command": "x10sion.copyMessageToClipboard", "title": "X10sion: Copy Message to Clipboard"}, {"command": "x10sion.refreshMcpComponents", "title": "X10sion: Refresh MCP Components", "icon": "$(refresh)"}, {"command": "x10sion.addMcpComponent", "title": "X10sion: Add MCP Component", "icon": "$(add)"}, {"command": "x10sion.removeMcpComponent", "title": "X10sion: Remove MCP Component", "icon": "$(trash)"}, {"command": "x10sion.toggleMcpComponent", "title": "X10sion: Toggle MCP Component", "icon": "$(check)"}, {"command": "x10sion.helloWorld", "title": "X10sion: Ask AI (Legacy)"}, {"command": "x10sion.handleInterventionRequest", "title": "X10sion: Handle Human Intervention Request"}, {"command": "x10sion.approveAction", "title": "X10sion: Approve AI Action", "icon": "$(check)"}, {"command": "x10sion.rejectAction", "title": "X10sion: Reject AI Action", "icon": "$(x)"}, {"command": "x10sion.provideGuidance", "title": "X10sion: Provide Guidance to AI", "icon": "$(lightbulb)"}, {"command": "x10sion.clearCompletedInterventions", "title": "X10sion: Clear Completed Interventions", "icon": "$(clear-all)"}, {"command": "x10sion.testHumanInTheLoop", "title": "X10sion: Test Human-in-the-Loop", "icon": "$(beaker)"}, {"command": "x10sion.testAgentFramework", "title": "X10sion: Test Agent Framework", "icon": "$(beaker)"}, {"command": "x10sion.testOllamaMonitor", "title": "X10sion: Test Ollama Monitor", "icon": "$(beaker)"}, {"command": "x10sion.showTerminalMonitorStats", "title": "X10sion: Show Terminal Monitor Statistics", "icon": "$(terminal)"}], "viewsContainers": {"activitybar": [{"id": "x10sion", "title": "X10sion", "icon": "$(hubot)"}]}, "views": {"x10sion": [{"id": "x10sionChat", "name": "Cha<PERSON>", "icon": "$(comment)", "contextualTitle": "X10sion Chat"}, {"id": "x10sionMcpComponents", "name": "MCP Components", "icon": "$(hubot)", "contextualTitle": "MCP Components"}, {"id": "x10sionHumanInTheLoopView", "name": "Human-in-the-Loop", "icon": "$(person)", "contextualTitle": "Human-in-the-Loop"}]}, "menus": {"view/title": [{"command": "x10sion.sendChatMessage", "when": "view == x10sionChat", "group": "navigation"}, {"command": "x10sion.clearChat", "when": "view == x10sionChat", "group": "navigation"}, {"command": "x10sion.refreshMcpComponents", "when": "view == x10sionMcpComponents", "group": "navigation"}, {"command": "x10sion.addMcpComponent", "when": "view == x10sionMcpComponents", "group": "navigation"}, {"command": "x10sion.clearCompletedInterventions", "when": "view == x10sionHumanInTheLoopView", "group": "navigation"}, {"command": "x10sion.testHumanInTheLoop", "when": "view == x10sionHumanInTheLoopView", "group": "navigation"}, {"command": "x10sion.testAgentFramework", "when": "view == x10sionHumanInTheLoopView", "group": "navigation"}, {"command": "x10sion.testOllamaMonitor", "when": "view == x10sionHumanInTheLoopView", "group": "navigation"}], "view/item/context": [{"command": "x10sion.copyMessageToClipboard", "when": "view == x10sionChat && viewItem =~ /^chatMessage-/", "group": "inline"}, {"command": "x10sion.toggleMcpComponent", "when": "view == x10sionMcpComponents && viewItem =~ /^component-/", "group": "inline"}, {"command": "x10sion.removeMcpComponent", "when": "view == x10sionMcpComponents && viewItem =~ /^component-/", "group": "inline"}, {"command": "x10sion.approveAction", "when": "view == x10sionHumanInTheLoopView && viewItem == 'pendingIntervention'", "group": "inline"}, {"command": "x10sion.rejectAction", "when": "view == x10sionHumanInTheLoopView && viewItem == 'pendingIntervention'", "group": "inline"}, {"command": "x10sion.provideGuidance", "when": "view == x10sionHumanInTheLoopView && viewItem == 'pendingIntervention'", "group": "inline"}], "editor/context": [{"command": "x10sion.askAi", "group": "x10sion"}, {"command": "x10sion.openChat", "group": "x10sion"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test", "test:agent-framework": "vscode-test --extensionDevelopmentPath=. --extensionTestsPath=./out/test/suite/agent-framework.test.js", "test:system": "vscode-test --extensionDevelopmentPath=. --extensionTestsPath=./out/test/system/full-system-test.js"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/sinon": "^17.0.4", "@types/vscode": "^1.100.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "eslint": "^9.25.1", "sinon": "^20.0.0", "typescript": "^5.8.3"}, "dependencies": {"node-fetch": "^3.3.2"}}