# X10sion VS Code Extension - Quick Start Guide

*Last Updated: May 25, 2025*

## Welcome to X10sion

X10sion is a revolutionary local-first AI coding co-pilot for VS Code that delivers intelligent, context-aware assistance while respecting user privacy and system resources. This guide will help you get started with the X10sion extension development.

## What's in the folder

* This folder contains all of the files necessary for the X10sion extension.
* `package.json` - Extension manifest with metadata, commands, and dependencies for the AI agent framework.
* `src/extension.ts` - Main entry point that initializes the AI agent system, MCP server/client, and all core functionality.
* `src/agents/` - AI agent implementations including code generation, prompt enhancement, and specialized agents.
* `src/mcp/` - Model Context Protocol implementation for standardized AI-context communication.
* `src/parallel/` - Parallel processing system optimized for multi-core systems and resource efficiency.
* `src/monitoring/` - Real-time monitoring systems for LLM outputs and terminal activity.
* `docs/dev_guide/` - Comprehensive development guides for all 17 phases of development.
* `knowledge_base/` - RAG knowledge base with best practices, patterns, and examples.
* `testResults/` - Test results organized by development phase for comprehensive validation.

## Prerequisites (May 2025)

Before getting started, ensure you have:

* **VS Code**: Version 1.100+ (April 2025 release or later)
* **Node.js**: Version 18.0+ with npm
* **TypeScript**: Version 5.5+ with enhanced ES module support
* **Ollama or LM Studio**: For local LLM inference
  * Recommended models: DeepSeek-R1, Qwen 3, Llama 3.3, Gemma 3
* **Docker**: For Qdrant and PostgreSQL (optional for advanced features)
* **System Requirements**: 8GB RAM minimum, 16GB recommended

## Get up and running straight away

* Press `F5` to open a new window with your extension loaded.
* Run your command from the command palette by pressing (`Ctrl+Shift+P` or `Cmd+Shift+P` on Mac) and typing `X10sion`.
* Set breakpoints in your code inside `src/extension.ts` to debug your extension.
* Find output from your extension in the debug console and VS Code output panel.

## X10sion Development Workflow

### Phase-Based Development
X10sion follows a 17-phase development approach optimized for 8GB VRAM LLMs:

1. **Phases 0-7**: Core foundation (completed)
2. **Phases 8-9**: AI Agent Framework (in progress)
3. **Phases 10-13**: Advanced features and optimization
4. **Phases 14-17**: Enterprise features and ethical AI

### Testing Strategy
* Run `npm test` for unit tests
* Use `npm run test:phase` for phase-specific testing
* Check `testResults/` for comprehensive test documentation
* Follow the testing methodology in `testMethod.md`

## Make changes

* You can relaunch the extension from the debug toolbar after changing code in `src/extension.ts`.
* You can also reload (`Ctrl+R` or `Cmd+R` on Mac) the VS Code window with your extension to load your changes.
* Use `npm run compile` to compile TypeScript changes.
* Use `npm run watch` for continuous compilation during development.

## Explore the API

* You can open the full set of our API when you open the file `node_modules/@types/vscode/index.d.ts`.
* Review `src/mcp/types.ts` for Model Context Protocol type definitions.
* Check `src/agents/base-agent.ts` for the AI agent framework API.
* Explore `knowledge_base/best_practices/` for implementation guidelines.

## Run tests

* Install the [Extension Test Runner](https://marketplace.visualstudio.com/items?itemName=ms-vscode.extension-test-runner)
* Run the "watch" task via the **Tasks: Run Task** command. Make sure this is running, or tests might not be discovered.
* Open the Testing view from the activity bar and click the "Run Test" button, or use the hotkey `Ctrl/Cmd + ; A`
* See the output of the test result in the Test Results view.
* Make changes to `src/test/extension.test.ts` or create new test files inside the `test` folder.
  * The provided test runner will only consider files matching the name pattern `**.test.ts`.
  * You can create folders inside the `test` folder to structure your tests any way you want.

## X10sion Specific Features

### AI Agent Development
* Create new agents by extending `BaseAgent` class
* Register agents with the `AgentSystem`
* Use the `AgentOrchestrator` for complex workflows
* Follow modular architecture patterns (300-500 lines per file)

### MCP Integration
* Implement resources in `src/mcp/resources/`
* Create tools in `src/mcp/tools/`
* Add prompts in `src/mcp/prompts/`
* Use the MCP client/server for standardized communication

### Performance Optimization
* Target 8GB VRAM systems with 4K context windows
* Use parallel processing for CPU-intensive tasks
* Implement lazy loading for non-critical components
* Monitor resource usage with built-in monitoring systems

## Go further

* [Follow UX guidelines](https://code.visualstudio.com/api/ux-guidelines/overview) to create extensions that seamlessly integrate with VS Code's native interface and patterns.
* Review X10sion's modular architecture in `dirStructure.md` for best practices.
* Study the AI agent framework in `docs/dev_guide/devPhase8/` for advanced agent development.
* Explore the knowledge base in `knowledge_base/` for comprehensive documentation.
* Follow the development phases in `docs/dev_guide/` for structured implementation.
* Reduce the extension size and improve the startup time by [bundling your extension](https://code.visualstudio.com/api/working-with-extensions/bundling-extension).
* [Publish your extension](https://code.visualstudio.com/api/working-with-extensions/publishing-extension) on the VS Code extension marketplace.
* Automate builds by setting up [Continuous Integration](https://code.visualstudio.com/api/working-with-extensions/continuous-integration).

## X10sion Resources

* **Documentation**: Complete guides in `docs/dev_guide/`
* **Testing**: Methodology and results in `testMethod.md` and `testResults/`
* **Architecture**: Detailed structure in `dirStructure.md` and `fileRelations.md`
* **Guidelines**: Development standards in `x10sion_*_guidelines.md`
* **Knowledge Base**: Best practices and patterns in `knowledge_base/`

## Support & Community

* **Issues**: Report bugs and feature requests through GitHub issues
* **Documentation**: Comprehensive guides and API references
* **Testing**: Follow the testing methodology for quality assurance
* **Performance**: Optimize for resource-constrained environments

---

**X10sion** - *Transforming software development through local-first AI agents*

*For detailed development information, see README.md and the comprehensive documentation in docs/dev_guide/*
