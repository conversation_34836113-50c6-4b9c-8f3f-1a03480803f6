import * as vscode from 'vscode';
import { EditorContext } from './types.js';
import { sendToOllama } from './ollama.js';

/**
 * Chat message interface
 */
interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
    isError?: boolean;
}

/**
 * Chat message tree item
 */
class ChatMessageTreeItem extends vscode.TreeItem {
    constructor(
        public readonly message: ChatMessage
    ) {
        super(
            message.content,
            vscode.TreeItemCollapsibleState.None
        );

        // Set icon based on role
        if (message.role === 'user') {
            this.iconPath = new vscode.ThemeIcon('account');
        } else if (message.role === 'assistant') {
            this.iconPath = new vscode.ThemeIcon('hubot');
        } else {
            this.iconPath = new vscode.ThemeIcon('info');
        }

        // Set tooltip with timestamp
        const date = new Date(message.timestamp);
        this.tooltip = `${message.role.charAt(0).toUpperCase() + message.role.slice(1)} - ${date.toLocaleTimeString()}`;

        // Set description with timestamp
        this.description = date.toLocaleTimeString();

        // Set context value for context menu filtering
        this.contextValue = `chatMessage-${message.role}`;

        // Set command to copy message to clipboard
        this.command = {
            title: 'Copy Message',
            command: 'x10sion.copyMessageToClipboard',
            arguments: [message.content]
        };
    }
}

/**
 * Chat view provider
 */
export class ChatViewProvider implements vscode.TreeDataProvider<ChatMessageTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<ChatMessageTreeItem | undefined | null | void> = new vscode.EventEmitter<ChatMessageTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<ChatMessageTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private messages: ChatMessage[] = [];
    private isProcessing: boolean = false;
    private statusBarItem: vscode.StatusBarItem;
    private treeView: vscode.TreeView<ChatMessageTreeItem> | undefined;

    constructor(context: vscode.ExtensionContext) {
        // Initialize with a welcome message
        this.messages.push({
            id: 'welcome',
            role: 'assistant',
            content: 'Hello! I\'m X10sion AI. How can I help you with your code today?',
            timestamp: Date.now()
        });

        // Create status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.statusBarItem.text = '$(comment) Chat';
        this.statusBarItem.tooltip = 'X10sion Chat';
        this.statusBarItem.command = 'x10sion.focusChatInput';
        this.statusBarItem.show();
        context.subscriptions.push(this.statusBarItem);

        // Register commands
        context.subscriptions.push(
            vscode.commands.registerCommand('x10sion.sendChatMessage', async () => {
                await this.sendMessage();
            }),
            vscode.commands.registerCommand('x10sion.clearChat', () => {
                this.clearChat();
            }),
            vscode.commands.registerCommand('x10sion.copyMessageToClipboard', (text: string) => {
                vscode.env.clipboard.writeText(text);
                vscode.window.showInformationMessage('Message copied to clipboard');
            }),
            vscode.commands.registerCommand('x10sion.focusChatInput', () => {
                this.focusInput();
            })
        );
    }

    setTreeView(treeView: vscode.TreeView<ChatMessageTreeItem>) {
        this.treeView = treeView;
    }

    getTreeItem(element: ChatMessageTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: ChatMessageTreeItem): Thenable<ChatMessageTreeItem[]> {
        if (element) {
            return Promise.resolve([]);
        } else {
            return Promise.resolve(
                this.messages.map(message => new ChatMessageTreeItem(message))
            );
        }
    }

    async sendMessage() {
        if (this.isProcessing) {
            vscode.window.showInformationMessage('Please wait for the current message to be processed');
            return;
        }

        const userInput = await vscode.window.showInputBox({
            prompt: 'Type your message',
            placeHolder: 'Ask me anything about your code...',
            ignoreFocusOut: true
        });

        if (!userInput || userInput.trim() === '') {
            return;
        }

        // Add user message
        const userMessage: ChatMessage = {
            id: `user-${Date.now()}`,
            role: 'user',
            content: userInput,
            timestamp: Date.now()
        };
        this.messages.push(userMessage);
        this._onDidChangeTreeData.fire();

        // Add thinking message
        const thinkingMessage: ChatMessage = {
            id: `thinking-${Date.now()}`,
            role: 'assistant',
            content: 'Thinking...',
            timestamp: Date.now()
        };
        this.messages.push(thinkingMessage);
        this._onDidChangeTreeData.fire();

        // Scroll to bottom
        if (this.treeView) {
            this.treeView.reveal(new ChatMessageTreeItem(thinkingMessage), { select: false, focus: false });
        }

        // Set processing state
        this.isProcessing = true;
        this.statusBarItem.text = '$(sync~spin) Processing...';

        try {
            // Get editor context
            const contextInfo = await vscode.commands.executeCommand<EditorContext>('x10sion.getActiveEditorContext');

            // Send to Ollama
            const response = await sendToOllama(userInput, contextInfo || {
                filePath: null,
                languageId: null,
                selectedText: null,
                fileContent: null
            });

            // Remove thinking message
            this.messages = this.messages.filter(m => m.id !== thinkingMessage.id);

            // Add assistant message
            const assistantMessage: ChatMessage = {
                id: `assistant-${Date.now()}`,
                role: 'assistant',
                content: response || 'Sorry, I couldn\'t process your request.',
                timestamp: Date.now(),
                isError: !response
            };
            this.messages.push(assistantMessage);
            this._onDidChangeTreeData.fire();

            // Scroll to bottom
            if (this.treeView) {
                this.treeView.reveal(new ChatMessageTreeItem(assistantMessage), { select: false, focus: false });
            }
        } catch (error) {
            // Remove thinking message
            this.messages = this.messages.filter(m => m.id !== thinkingMessage.id);

            // Add error message
            const errorMessage: ChatMessage = {
                id: `error-${Date.now()}`,
                role: 'assistant',
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: Date.now(),
                isError: true
            };
            this.messages.push(errorMessage);
            this._onDidChangeTreeData.fire();
        } finally {
            // Reset processing state
            this.isProcessing = false;
            this.statusBarItem.text = '$(comment) Chat';
        }
    }

    clearChat() {
        // Keep only the welcome message
        this.messages = [this.messages[0]];
        this._onDidChangeTreeData.fire();
    }

    focusInput() {
        vscode.commands.executeCommand('x10sion.sendChatMessage');
    }

    refresh() {
        this._onDidChangeTreeData.fire();
    }
}
