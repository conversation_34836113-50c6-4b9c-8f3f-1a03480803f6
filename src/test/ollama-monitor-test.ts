/**
 * Test script for LLM Monitor with Ollama
 *
 * This script tests the LLM Monitor with the Ollama LLM provider.
 */

import * as vscode from 'vscode';
import { LLMMonitor, LLMIssue, LLMIntervention } from '../monitoring/llm-monitor';
import { OllamaProvider } from '../llm/providers/ollama-provider';

/**
 * Run the Ollama monitor test
 */
export async function runOllamaMonitorTest() {
    // Create output channel for logs
    const outputChannel = vscode.window.createOutputChannel('Ollama Monitor Test');
    outputChannel.show();
    outputChannel.appendLine('Starting Ollama Monitor Test...');

    try {
        // Create Ollama provider
        const ollamaProvider = new OllamaProvider({
            baseUrl: 'http://localhost:11434',
            defaultModel: 'gemma3:4b-it-q4_K_M'
        });

        outputChannel.appendLine('Checking if Ollama is available...');
        const isAvailable = await ollamaProvider.isAvailable();

        if (!isAvailable) {
            outputChannel.appendLine('❌ Ollama is not available. Please make sure the Ollama server is running.');
            return;
        }

        outputChannel.appendLine('✅ Ollama is available.');

        // Get available models
        outputChannel.appendLine('Getting available models...');
        const models = await ollamaProvider.getAvailableModels();
        outputChannel.appendLine(`Available models: ${models.join(', ')}`);

        // Create LLM Monitor
        outputChannel.appendLine('Creating LLM Monitor...');
        const monitor = new LLMMonitor({
            enableIssueDetection: true,
            enableInterventions: true,
            debugMode: true
        });

        // Test with valid prompt
        outputChannel.appendLine('\n--- Testing with valid prompt ---');
        await testPrompt(
            monitor,
            ollamaProvider,
            'Explain what TypeScript is in 2-3 sentences.',
            outputChannel
        );

        // Test with prompt that might cause hallucination
        outputChannel.appendLine('\n--- Testing with potential hallucination prompt ---');
        await testPrompt(
            monitor,
            ollamaProvider,
            'Tell me about the fictional programming language called FluxScript that was invented in 2023.',
            outputChannel
        );

        // Test with prompt that might cause repetition
        outputChannel.appendLine('\n--- Testing with potential repetition prompt ---');
        await testPrompt(
            monitor,
            ollamaProvider,
            'Write a poem where each line starts with "The code".',
            outputChannel
        );

        // Test with complex prompt
        outputChannel.appendLine('\n--- Testing with complex prompt ---');
        await testPrompt(
            monitor,
            ollamaProvider,
            'Explain quantum computing, its applications in cryptography, and how it relates to blockchain technology. Include technical details about qubits, superposition, and entanglement.',
            outputChannel
        );

        outputChannel.appendLine('\nOllama Monitor Test completed successfully.');
    } catch (error) {
        outputChannel.appendLine(`❌ Error: ${error instanceof Error ? error.message : String(error)}`);
        if (error instanceof Error && error.stack) {
            outputChannel.appendLine(error.stack);
        }
    }
}

/**
 * Test a prompt with the LLM Monitor
 */
async function testPrompt(
    monitor: LLMMonitor,
    provider: OllamaProvider,
    prompt: string,
    outputChannel: vscode.OutputChannel
) {
    outputChannel.appendLine(`Prompt: "${prompt}"`);

    // Track request
    const requestId = monitor.trackRequest(
        provider.getDefaultModel(),
        prompt,
        { temperature: 0.7, maxTokens: 500 }
    );

    try {
        // Generate completion
        const startTime = Date.now();
        const completion = await provider.generateCompletion(prompt, {
            temperature: 0.7,
            maxTokens: 500
        });
        const endTime = Date.now();

        // Track response
        const responseId = monitor.trackResponse(
            requestId,
            provider.getDefaultModel(),
            completion.text,
            endTime - startTime,
            completion.usage?.totalTokens
        );

        // Get issues from monitor's internal state
        const issues = monitor.getAllIssues().filter(issue => issue.responseId === responseId);

        // Log results
        outputChannel.appendLine(`Response: "${completion.text.substring(0, 100)}${completion.text.length > 100 ? '...' : ''}"`);
        outputChannel.appendLine(`Response time: ${endTime - startTime}ms`);
        outputChannel.appendLine(`Token usage: ${completion.usage?.totalTokens || 'unknown'}`);

        if (issues.length > 0) {
            outputChannel.appendLine(`Issues detected: ${issues.length}`);
            issues.forEach((issue: LLMIssue, index: number) => {
                outputChannel.appendLine(`  Issue ${index + 1}: ${issue.type} (${issue.severity}) - ${issue.description}`);
            });

            // Get interventions
            const interventions = monitor.getAllInterventions().filter((intervention: LLMIntervention) =>
                issues.some((issue: LLMIssue) => issue.id === intervention.issueId)
            );

            if (interventions.length > 0) {
                outputChannel.appendLine(`Interventions applied: ${interventions.length}`);
                interventions.forEach((intervention: LLMIntervention, index: number) => {
                    outputChannel.appendLine(`  Intervention ${index + 1}: ${intervention.strategy} - ${intervention.description}`);
                });
            }
        } else {
            outputChannel.appendLine('No issues detected.');
        }
    } catch (error) {
        // Track error
        monitor.trackError(
            requestId,
            provider.getDefaultModel(),
            error instanceof Error ? error : new Error(String(error))
        );

        outputChannel.appendLine(`Error: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Register the command
 */
export function registerOllamaMonitorTestCommand(context: vscode.ExtensionContext) {
    const disposable = vscode.commands.registerCommand('x10sion.testOllamaMonitor', runOllamaMonitorTest);
    context.subscriptions.push(disposable);
}
