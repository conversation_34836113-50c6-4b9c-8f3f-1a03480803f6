import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import { PromptEnhancementAgent } from '../../agents/prompt-enhancement-agent';
import { CodeGenerationAgent } from '../../agents/code-generation-agent';
import { CodeAnalysisAgent } from '../../agents/code-analysis-agent';
import { MockLLMProvider } from '../mocks/mock-llm-provider';
import { WorkerPool } from '../../parallel/worker-pool';
import { LLMMonitor } from '../../monitoring/llm-monitor';

suite('Full System Test Suite', () => {
    let sandbox: sinon.SinonSandbox;
    let llmProvider: MockLLMProvider;
    let workerPool: WorkerPool;
    let llmMonitor: LLMMonitor;

    setup(() => {
        sandbox = sinon.createSandbox();

        // Mock VS Code APIs
        sandbox.stub(vscode.window, 'showInformationMessage').resolves();
        sandbox.stub(vscode.window, 'showErrorMessage').resolves();

        // Create mock LLM provider
        llmProvider = new MockLLMProvider();

        // Create worker pool
        workerPool = new WorkerPool(4);

        // Create LLM monitor
        llmMonitor = {
            registerAgent: sandbox.stub(),
            unregisterAgent: sandbox.stub()
        } as unknown as LLMMonitor;
    });

    teardown(() => {
        workerPool.dispose();
        sandbox.restore();
    });

    test('Phase 0-9 Integration Test', async () => {
        // Create agents
        const promptEnhancementAgent = new PromptEnhancementAgent(
            'prompt-enhancement',
            workerPool,
            llmMonitor,
            llmProvider
        );

        const codeGenerationAgent = new CodeGenerationAgent(
            'code-generation',
            workerPool,
            llmMonitor,
            llmProvider
        );

        const codeAnalysisAgent = new CodeAnalysisAgent(
            'code-analysis',
            workerPool,
            llmMonitor,
            llmProvider
        );

        // Test prompt enhancement agent
        const enhancedPrompt = await promptEnhancementAgent.execute('Generate a function to add two numbers', {
            contextItems: [
                {
                    type: 'text',
                    content: 'Use TypeScript for all code examples.',
                    metadata: { relevance: 0.9 }
                }
            ]
        });

        // The result is an EnhancedPrompt object, so we need to convert it to a string for testing
        const enhancedPromptStr = JSON.stringify(enhancedPrompt);
        assert.ok(enhancedPromptStr.includes('TypeScript'), 'Enhanced prompt should include context');

        // Test code generation agent
        const generatedCode = await codeGenerationAgent.execute('Create a function to add two numbers', {
            language: 'typescript'
        });

        assert.ok(generatedCode.code.includes('function add'), 'Generated code should include an add function');

        // Test code analysis agent
        const analysisResult = await codeAnalysisAgent.execute(JSON.stringify({
            code: 'function add(a, b) { return a + b; }',
            language: 'typescript'
        }));

        assert.ok(analysisResult.issues.length >= 0, 'Analysis result should include issues array');
    });
});
