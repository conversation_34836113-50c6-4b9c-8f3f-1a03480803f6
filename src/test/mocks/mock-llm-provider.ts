import { LanguageModelProvider, CompletionOptions, CompletionResult, EmbeddingResult } from '../../llm/providers/base-provider';

/**
 * Mock LLM provider for testing
 */
export class MockLLMProvider implements LanguageModelProvider {
    private name: string = 'mock-provider';
    private models: string[] = ['mock-model'];

    /**
     * Get the name of the provider
     */
    getName(): string {
        return this.name;
    }

    /**
     * Check if the provider is available
     */
    async isAvailable(): Promise<boolean> {
        return true;
    }

    /**
     * Get the default model
     */
    getDefaultModel(): string {
        return this.models[0];
    }

    /**
     * Get available models
     */
    async getAvailableModels(): Promise<string[]> {
        return this.models;
    }

    /**
     * Generate a completion
     */
    async generateCompletion(prompt: string, options?: CompletionOptions): Promise<CompletionResult> {
        // Generate different responses based on the prompt content
        if (prompt.includes('add two numbers')) {
            return {
                text: `\`\`\`typescript
function add(a: number, b: number): number {
    return a + b;
}
\`\`\`

This function takes two parameters, both of type number, and returns their sum.`,
                model: 'mock-model',
                usage: {
                    promptTokens: 10,
                    completionTokens: 50,
                    totalTokens: 60
                },
                finishReason: 'stop'
            };
        } else if (prompt.includes('analyze')) {
            return {
                text: `{
  "issues": [
    {
      "type": "style",
      "severity": "info",
      "message": "Consider adding explicit type annotations",
      "line": 1,
      "column": 14
    }
  ],
  "metrics": [
    {
      "name": "complexity",
      "value": 1,
      "description": "Cyclomatic complexity"
    }
  ],
  "summary": "Simple function with good structure",
  "recommendations": [
    "Add explicit type annotations for parameters"
  ],
  "codeQualityScore": 8
}`,
                model: 'mock-model',
                usage: {
                    promptTokens: 15,
                    completionTokens: 100,
                    totalTokens: 115
                },
                finishReason: 'stop'
            };
        } else if (prompt.includes('test')) {
            return {
                text: `\`\`\`typescript
describe('add function', () => {
  it('should add two numbers correctly', () => {
    expect(add(2, 3)).toBe(5);
    expect(add(-1, 1)).toBe(0);
    expect(add(0, 0)).toBe(0);
  });
});
\`\`\``,
                model: 'mock-model',
                usage: {
                    promptTokens: 12,
                    completionTokens: 80,
                    totalTokens: 92
                },
                finishReason: 'stop'
            };
        } else if (prompt.includes('documentation')) {
            return {
                text: `# Add Function

A simple function to add two numbers together.

## Parameters

- \`a\` (number): The first number
- \`b\` (number): The second number

## Returns

- (number): The sum of a and b

## Example

\`\`\`typescript
const sum = add(5, 3); // 8
\`\`\``,
                model: 'mock-model',
                usage: {
                    promptTokens: 15,
                    completionTokens: 90,
                    totalTokens: 105
                },
                finishReason: 'stop'
            };
        } else {
            // Default response
            return {
                text: "I'm a mock LLM provider for testing purposes.",
                model: 'mock-model',
                usage: {
                    promptTokens: 5,
                    completionTokens: 10,
                    totalTokens: 15
                },
                finishReason: 'stop'
            };
        }
    }

    /**
     * Generate an embedding
     */
    async generateEmbedding(text: string): Promise<EmbeddingResult> {
        // Generate a simple mock embedding (32-dimensional vector)
        const embedding = Array(32).fill(0).map(() => Math.random());

        return {
            embedding,
            model: 'mock-model',
            usage: {
                promptTokens: text.split(' ').length,
                totalTokens: text.split(' ').length
            }
        };
    }
}
