/**
 * File Content Resource Tests for X10sion
 *
 * This file contains tests for the file content resource.
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import * as path from 'path';
import { X10sionMcpServer } from '../../mcp/server.js';
import { fileContentResource, registerFileContentResource } from '../../mcp/resources/file-content.js';

suite('File Content Resource Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let server: X10sionMcpServer;
    // We'll use a modified version of the resource for testing
    let testFileContentResource: any;
    let mockFs: {
        readFile: sinon.SinonStub;
        writeFile: sinon.SinonStub;
    };
    let mockWorkspaceFolders: vscode.WorkspaceFolder[];

    setup(() => {
        sandbox = sinon.createSandbox();
        server = new X10sionMcpServer({
            logging: {
                level: 'error', // Minimize logging during tests
                format: 'text'
            }
        });

        // Create mock functions
        mockFs = {
            readFile: sandbox.stub().resolves(Buffer.from('Test file content')),
            writeFile: sandbox.stub().resolves()
        };

        // Create mock workspace folders
        mockWorkspaceFolders = [
            {
                uri: vscode.Uri.file('/workspace'),
                name: 'workspace',
                index: 0
            }
        ];

        // Create a modified version of the resource for testing
        testFileContentResource = { ...fileContentResource };

        // Override the getData method to use our mocks
        testFileContentResource.getData = async (params?: any) => {
            try {
                // Validate parameters
                if (!params || !params.filePath) {
                    throw new Error('File path is required');
                }

                const filePath = params.filePath;

                // Resolve the file path
                let resolvedPath = filePath;
                if (!path.isAbsolute(filePath)) {
                    // If the path is relative, resolve it against the workspace root
                    if (!mockWorkspaceFolders || mockWorkspaceFolders.length === 0) {
                        throw new Error('No workspace folder is open');
                    }

                    resolvedPath = path.join(mockWorkspaceFolders[0].uri.fsPath, filePath);
                }

                // Create a URI for the file
                const uri = vscode.Uri.file(resolvedPath);

                // Read the file content using our mock
                const content = await mockFs.readFile(uri);

                // Convert the content to a string
                const contentStr = Buffer.from(content).toString('utf-8');

                // Get the language ID based on the file extension
                const languageId = path.extname(resolvedPath).toLowerCase() === '.ts' ? 'typescript' :
                                   path.extname(resolvedPath).toLowerCase() === '.js' ? 'javascript' : null;

                return {
                    filePath: resolvedPath,
                    content: contentStr,
                    languageId
                };
            } catch (error) {
                throw new Error(`Failed to get file content: ${error instanceof Error ? error.message : String(error)}`);
            }
        };

        // Override the update method to use our mocks
        testFileContentResource.update = async (data: any) => {
            try {
                // Validate parameters
                if (!data || !data.filePath || data.content === undefined) {
                    throw new Error('File path and content are required');
                }

                const filePath = data.filePath;
                const content = data.content;

                // Resolve the file path
                let resolvedPath = filePath;
                if (!path.isAbsolute(filePath)) {
                    // If the path is relative, resolve it against the workspace root
                    if (!mockWorkspaceFolders || mockWorkspaceFolders.length === 0) {
                        throw new Error('No workspace folder is open');
                    }

                    resolvedPath = path.join(mockWorkspaceFolders[0].uri.fsPath, filePath);
                }

                // Create a URI for the file
                const uri = vscode.Uri.file(resolvedPath);

                // Convert the content to a buffer
                const contentBuffer = Buffer.from(content, 'utf-8');

                // Write the file content using our mock
                await mockFs.writeFile(uri, contentBuffer);
            } catch (error) {
                throw new Error(`Failed to update file content: ${error instanceof Error ? error.message : String(error)}`);
            }
        };
    });

    teardown(async () => {
        sandbox.restore();
        server.dispose();
    });

    test('Should register file content resource', async () => {
        await server.start();

        // Register our test resource instead of the real one
        const resourceId = await server.registerResource(testFileContentResource);
        assert.strictEqual(resourceId as unknown as string, 'file-content');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'file-content');
    });

    test('Should get file content with absolute path', async () => {
        await server.start();
        await server.registerResource(testFileContentResource);

        const resource = await server.getComponent('file-content');
        assert.ok(resource);

        // Call getData method with absolute path
        const data = await (resource as any).getData({ filePath: '/workspace/test.ts' });

        // Verify the data
        assert.strictEqual(data.filePath, '/workspace/test.ts');
        assert.strictEqual(data.content, 'Test file content');
        assert.strictEqual(data.languageId, 'typescript');

        // Verify the readFile call
        assert.strictEqual(mockFs.readFile.callCount, 1);
        const uri = mockFs.readFile.firstCall.args[0];
        assert.strictEqual(uri.fsPath, '/workspace/test.ts');
    });

    test('Should get file content with relative path', async () => {
        await server.start();
        await server.registerResource(testFileContentResource);

        const resource = await server.getComponent('file-content');
        assert.ok(resource);

        // Call getData method with relative path
        const data = await (resource as any).getData({ filePath: 'test.js' });

        // Verify the data
        assert.strictEqual(data.filePath, path.join('/workspace', 'test.js'));
        assert.strictEqual(data.content, 'Test file content');
        assert.strictEqual(data.languageId, 'javascript');

        // Verify the readFile call
        assert.strictEqual(mockFs.readFile.callCount, 1);
        const uri = mockFs.readFile.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'test.js'));
    });

    test('Should handle errors when getting file content', async () => {
        // Make the stub throw an error
        mockFs.readFile.rejects(new Error('File not found'));

        await server.start();
        await server.registerResource(testFileContentResource);

        const resource = await server.getComponent('file-content');
        assert.ok(resource);

        // Call getData method and expect it to throw
        try {
            await (resource as any).getData({ filePath: 'test.js' });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to get file content: File not found');
        }

        assert.strictEqual(mockFs.readFile.callCount, 1);
    });

    test('Should update file content', async () => {
        await server.start();
        await server.registerResource(testFileContentResource);

        const resource = await server.getComponent('file-content');
        assert.ok(resource);

        // Call update method
        await (resource as any).update({
            filePath: 'test.js',
            content: 'Updated content'
        });

        // Verify the writeFile call
        assert.strictEqual(mockFs.writeFile.callCount, 1);
        const uri = mockFs.writeFile.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'test.js'));

        const content = mockFs.writeFile.firstCall.args[1];
        assert.deepStrictEqual(content, Buffer.from('Updated content', 'utf-8'));
    });

    test('Should handle errors when updating file content', async () => {
        // Make the stub throw an error
        mockFs.writeFile.rejects(new Error('Permission denied'));

        await server.start();
        await server.registerResource(testFileContentResource);

        const resource = await server.getComponent('file-content');
        assert.ok(resource);

        // Call update method and expect it to throw
        try {
            await (resource as any).update({
                filePath: 'test.js',
                content: 'Updated content'
            });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to update file content: Permission denied');
        }

        assert.strictEqual(mockFs.writeFile.callCount, 1);
    });

    test('Should require file path parameter', async () => {
        await server.start();
        await server.registerResource(testFileContentResource);

        const resource = await server.getComponent('file-content');
        assert.ok(resource);

        // Call getData method without file path and expect it to throw
        try {
            await (resource as any).getData({});
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to get file content: File path is required');
        }

        assert.strictEqual(mockFs.readFile.callCount, 0);
    });

    test('Should require file path and content for update', async () => {
        await server.start();
        await server.registerResource(testFileContentResource);

        const resource = await server.getComponent('file-content');
        assert.ok(resource);

        // Call update method without file path and expect it to throw
        try {
            await (resource as any).update({});
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to update file content: File path and content are required');
        }

        // Call update method without content and expect it to throw
        try {
            await (resource as any).update({ filePath: 'test.js' });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to update file content: File path and content are required');
        }

        assert.strictEqual(mockFs.writeFile.callCount, 0);
    });
});
