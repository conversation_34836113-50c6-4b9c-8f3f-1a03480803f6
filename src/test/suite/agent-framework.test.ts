import * as assert from 'assert';
import * as sinon from 'sinon';
import * as vscode from 'vscode';
import { X10sionAgentSystem } from '../../agents/framework/agent-system.js';
import { AgentFactory, AgentType } from '../../agents/framework/agent-factory.js';
import { AgentOrchestrator, WorkflowStatus } from '../../agents/framework/agent-orchestrator.js';
import { WorkerPool } from '../../parallel/worker-pool.js';
import { LLMMonitor } from '../../monitoring/llm-monitor.js';
import { BaseAgent } from '../../agents/base-agent.js';
import { LanguageModelProvider } from '../../llm/providers/base-provider.js';

suite('Agent Framework Test Suite', () => {
    let workerPool: WorkerPool;
    let llmMonitor: LLMMonitor;
    let llmProvider: LanguageModelProvider;
    let agentSystem: X10sionAgentSystem;
    let agentFactory: AgentFactory;
    let agentOrchestrator: AgentOrchestrator;
    let sandbox: sinon.SinonSandbox;

    setup(() => {
        sandbox = sinon.createSandbox();

        // Create mock worker pool
        workerPool = {
            execute: sandbox.stub().resolves('test result'),
            dispose: sandbox.stub()
        } as unknown as WorkerPool;

        // Create mock LLM monitor
        llmMonitor = {
            onEvent: sandbox.stub().returns({ dispose: sandbox.stub() }),
            registerAgent: sandbox.stub(),
            unregisterAgent: sandbox.stub(),
            dispose: sandbox.stub()
        } as unknown as LLMMonitor;

        // Create mock LLM provider
        llmProvider = {
            getName: sandbox.stub().returns('test-provider'),
            isAvailable: true,
            execute: sandbox.stub().resolves('test response'),
            dispose: sandbox.stub()
        } as unknown as LanguageModelProvider;

        // Create agent system
        agentSystem = new X10sionAgentSystem(workerPool, llmMonitor, {
            debugMode: true
        });

        // Create agent factory
        agentFactory = new AgentFactory(agentSystem, workerPool, llmMonitor);

        // Create agent orchestrator
        agentOrchestrator = new AgentOrchestrator(agentSystem, workerPool);
    });

    teardown(() => {
        sandbox.restore();
        agentSystem.dispose();
        agentOrchestrator.dispose();
    });

    test('Agent System - Register and Get LLM Provider', () => {
        // Register LLM provider
        agentSystem.registerLLMProvider(llmProvider);

        // Get LLM provider
        const provider = agentSystem.getLLMProvider('test-provider');

        // Verify
        assert.strictEqual(provider, llmProvider);
    });

    test('Agent System - Get Best LLM Provider', () => {
        // Register LLM provider
        agentSystem.registerLLMProvider(llmProvider);

        // Get best LLM provider
        const provider = agentSystem.getBestLLMProvider();

        // Verify
        assert.strictEqual(provider, llmProvider);
    });

    test('Agent Factory - Create Basic Agent', () => {
        // Register LLM provider
        agentSystem.registerLLMProvider(llmProvider);

        // Create agent
        const agent = agentFactory.createAgent({
            name: 'Test Agent',
            description: 'Test agent description',
            type: AgentType.BASIC
        });

        // Verify
        assert.ok(agent instanceof BaseAgent);
        assert.strictEqual(agent.getName(), 'Test Agent');

        // Verify agent is registered with the system
        const registeredAgent = agentSystem.getAgent(agent.getId());
        assert.strictEqual(registeredAgent, agent);
    });

    test('Agent Orchestrator - Register and Execute Workflow', async () => {
        // Register LLM provider
        agentSystem.registerLLMProvider(llmProvider);

        // Create agent
        const agent = agentFactory.createAgent({
            name: 'Test Agent',
            description: 'Test agent description',
            type: AgentType.BASIC
        });

        // Mock agent execute method
        sandbox.stub(agent, 'execute').resolves('test result');

        // Register workflow
        const workflowId = agentOrchestrator.registerWorkflow({
            name: 'Test Workflow',
            description: 'Test workflow description',
            steps: [
                {
                    id: 'step1',
                    agentId: agent.getId(),
                    task: 'test_task',
                    context: { message: 'This is a test task' }
                }
            ]
        });

        // Execute workflow
        const result = await agentOrchestrator.executeWorkflow(workflowId);

        // Verify
        assert.strictEqual(result.status, 'completed');
        assert.strictEqual(result.workflowId, workflowId);
        assert.strictEqual(agentOrchestrator.getWorkflowStatus(workflowId), WorkflowStatus.COMPLETED);

        // Verify step result
        const stepResult = result.stepResults.get('step1');
        assert.ok(stepResult);
        assert.strictEqual(stepResult.status, 'completed');
        assert.strictEqual(stepResult.result, 'test result');
    });

    test('Agent Orchestrator - Execute Parallel Workflow', async () => {
        // Register LLM provider
        agentSystem.registerLLMProvider(llmProvider);

        // Create agents
        const agent1 = agentFactory.createAgent({
            name: 'Test Agent 1',
            description: 'Test agent 1 description',
            type: AgentType.BASIC
        });

        const agent2 = agentFactory.createAgent({
            name: 'Test Agent 2',
            description: 'Test agent 2 description',
            type: AgentType.BASIC
        });

        // Mock agent execute methods
        sandbox.stub(agent1, 'execute').resolves('test result 1');
        sandbox.stub(agent2, 'execute').resolves('test result 2');

        // Register workflow
        const workflowId = agentOrchestrator.registerWorkflow({
            name: 'Test Parallel Workflow',
            description: 'Test parallel workflow description',
            parallelExecution: true,
            steps: [
                {
                    id: 'step1',
                    agentId: agent1.getId(),
                    task: 'test_task_1',
                    context: { message: 'This is test task 1' }
                },
                {
                    id: 'step2',
                    agentId: agent2.getId(),
                    task: 'test_task_2',
                    context: { message: 'This is test task 2' }
                }
            ]
        });

        // Execute workflow
        const result = await agentOrchestrator.executeWorkflow(workflowId);

        // Verify
        assert.strictEqual(result.status, 'completed');
        assert.strictEqual(result.workflowId, workflowId);
        assert.strictEqual(agentOrchestrator.getWorkflowStatus(workflowId), WorkflowStatus.COMPLETED);

        // Verify step results
        const step1Result = result.stepResults.get('step1');
        assert.ok(step1Result);
        assert.strictEqual(step1Result.status, 'completed');
        assert.strictEqual(step1Result.result, 'test result 1');

        const step2Result = result.stepResults.get('step2');
        assert.ok(step2Result);
        assert.strictEqual(step2Result.status, 'completed');
        assert.strictEqual(step2Result.result, 'test result 2');
    });
});
