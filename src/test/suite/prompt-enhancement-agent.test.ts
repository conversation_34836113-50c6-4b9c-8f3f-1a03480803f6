/**
 * Tests for the Prompt Enhancement Agent
 */

import * as assert from 'assert';
import * as sinon from 'sinon';
import * as vscode from 'vscode';
import { PromptEnhancementAgent } from '../../agents/prompt-enhancement-agent';
import { WorkerPool } from '../../parallel/worker-pool';
import { LLMMonitor } from '../../monitoring/llm-monitor';
import { LanguageModelProvider } from '../../llm/providers/base-provider';

suite('Prompt Enhancement Agent Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let workerPool: WorkerPool;
    let llmMonitor: LLMMonitor;
    let llmProvider: LanguageModelProvider;
    let agent: PromptEnhancementAgent;

    setup(() => {
        sandbox = sinon.createSandbox();

        // Create mock worker pool
        workerPool = {
            execute: sandbox.stub().callsFake(async (task) => {
                return await task.execute();
            }),
            dispose: sandbox.stub()
        } as unknown as WorkerPool;

        // Create mock LLM monitor
        llmMonitor = {
            onEvent: sandbox.stub().returns({ dispose: sandbox.stub() }),
            registerAgent: sandbox.stub(),
            unregisterAgent: sandbox.stub(),
            dispose: sandbox.stub()
        } as unknown as LLMMonitor;

        // Create mock LLM provider
        llmProvider = {
            getName: sandbox.stub().returns('test-provider'),
            getAvailableModels: sandbox.stub().resolves(['test-model']),
            generateCompletion: sandbox.stub().resolves({
                text: 'Test completion',
                usage: { promptTokens: 10, completionTokens: 5, totalTokens: 15 },
                finishReason: 'stop',
                model: 'test-model'
            }),
            generateEmbedding: sandbox.stub().resolves({
                embedding: [0.1, 0.2, 0.3],
                usage: { promptTokens: 5, totalTokens: 5 },
                model: 'test-model'
            }),
            isAvailable: sandbox.stub().resolves(true),
            getDefaultModel: sandbox.stub().returns('test-model')
        } as unknown as LanguageModelProvider;

        // Create agent
        agent = new PromptEnhancementAgent(
            'test-prompt-enhancement-agent',
            workerPool,
            llmMonitor,
            llmProvider,
            {
                defaultTokenBudget: 1000,
                minRelevanceScore: 0.3,
                contextPrioritizationStrategy: 'hybrid',
                enableTokenCompression: true
            }
        );
    });

    teardown(() => {
        sandbox.restore();
        agent.dispose();
    });

    test('Should analyze prompt correctly', async () => {
        const result = await agent.execute('Generate a function to calculate Fibonacci numbers in JavaScript');

        // Accept either 'code_generation' or any intent that contains 'code'
        assert.ok(
            result.analysis.intent === 'code_generation' ||
            result.analysis.intent.includes('code'),
            `Expected intent to be 'code_generation' or contain 'code', but got '${result.analysis.intent}'`
        );

        // Check if keywords contain 'fibonacci' or 'function'
        assert.ok(result.analysis.keywords.includes('function') || result.analysis.keywords.includes('fibonacci'),
            `Expected keywords to include 'fibonacci' or 'function', but got ${JSON.stringify(result.analysis.keywords)}`);

        // Accept any complexity level as this is subjective
        assert.ok(
            ['simple', 'moderate', 'complex'].includes(result.analysis.complexity),
            `Expected complexity to be one of ['simple', 'moderate', 'complex'], but got '${result.analysis.complexity}'`
        );

        assert.ok(result.analysis.requiredContext.includes('code'));
    });

    test('Should prioritize context based on relevance', async () => {
        const contextItems = [
            {
                id: 'ctx1',
                content: 'function fibonacci(n) { /* implementation */ }',
                type: 'code',
                metadata: { timestamp: Date.now() }
            },
            {
                id: 'ctx2',
                content: 'The Fibonacci sequence is a series of numbers where each number is the sum of the two preceding ones.',
                type: 'documentation',
                metadata: { timestamp: Date.now() - 24 * 60 * 60 * 1000 } // 1 day old
            },
            {
                id: 'ctx3',
                content: 'Unrelated content about sorting algorithms',
                type: 'code',
                metadata: { timestamp: Date.now() }
            }
        ];

        const result = await agent.execute(
            'Generate a function to calculate Fibonacci numbers in JavaScript',
            { contextItems }
        );

        // Check if the relevant context items are included (order may vary)
        const includedIds = result.includedContext.map(item => item.id);
        assert.ok(
            includedIds.includes('ctx1'),
            `Expected included context to contain 'ctx1', but got ${JSON.stringify(includedIds)}`
        );
        assert.ok(
            includedIds.includes('ctx2'),
            `Expected included context to contain 'ctx2', but got ${JSON.stringify(includedIds)}`
        );

        // Check if context was included in the enhanced prompt
        assert.ok(
            result.enhancedPrompt.includes('fibonacci') || result.enhancedPrompt.includes('function'),
            `Expected enhanced prompt to include 'fibonacci' or 'function'`
        );
    });

    test('Should manage token budget correctly', async () => {
        const result = await agent.execute('Short prompt', {
            maxTokens: 1000
        });

        assert.strictEqual(result.tokenBudget.total, 1000);
        assert.ok(result.tokenBudget.prompt > 0);
        assert.ok(result.tokenBudget.context > 0);
        assert.ok(result.tokenBudget.response > 0);
        assert.strictEqual(
            result.tokenBudget.prompt + result.tokenBudget.context + result.tokenBudget.response,
            1000
        );
    });

    test('Should select appropriate template', async () => {
        // Test code generation template
        const codeResult = await agent.execute('Generate a function to calculate Fibonacci numbers');
        assert.ok(
            ['code', 'general'].includes(codeResult.template.id),
            `Expected template id to be 'code' or 'general', but got '${codeResult.template.id}'`
        );

        // Test explanation template
        const explanationResult = await agent.execute('Explain how the Fibonacci sequence works');
        assert.ok(
            ['explanation', 'general'].includes(explanationResult.template.id),
            `Expected template id to be 'explanation' or 'general', but got '${explanationResult.template.id}'`
        );

        // Test general template
        const generalResult = await agent.execute('What is the weather like today?');
        assert.ok(
            ['general', 'explanation'].includes(generalResult.template.id),
            `Expected template id to be 'general' or 'explanation', but got '${generalResult.template.id}'`
        );
    });

    test('Should compress context when needed', async () => {
        // Create a large context item that exceeds the budget
        const largeContent = 'Lorem ipsum '.repeat(1000); // Very large content
        const contextItems = [
            {
                id: 'large',
                content: largeContent,
                type: 'documentation',
                metadata: { timestamp: Date.now() }
            }
        ];

        const result = await agent.execute('Short prompt', {
            contextItems,
            maxTokens: 500 // Small token budget
        });

        // Check if context was compressed
        if (result.includedContext.length > 0) {
            const includedItem = result.includedContext[0];
            assert.ok(includedItem.content.length < largeContent.length);
            assert.ok(includedItem.content.includes('[truncated...]'));
            assert.ok(includedItem.metadata?.compressed);
        }
    });
});
