import * as assert from 'assert';
import * as sinon from 'sinon';
import * as vscode from 'vscode';
import { LLMMonitor, LLMIssueType, InterventionStrategy } from '../../monitoring/llm-monitor';
import { X10sionAgentSystem } from '../../agents/framework/agent-system';
import { BaseAgent, AgentStatus } from '../../agents/base-agent';
import { WorkerPool } from '../../parallel/worker-pool';
import { LanguageModelProvider } from '../../llm/providers/base-provider';

// Mock LLM provider for testing
class MockLLMProvider implements LanguageModelProvider {
    private name: string;
    private defaultModel: string;

    constructor(name: string = 'mock', defaultModel: string = 'mock-model') {
        this.name = name;
        this.defaultModel = defaultModel;
    }

    getName(): string {
        return this.name;
    }

    getDefaultModel(): string {
        return this.defaultModel;
    }

    async isAvailable(): Promise<boolean> {
        return true;
    }

    async getAvailableModels(): Promise<string[]> {
        return [this.defaultModel];
    }

    async generateCompletion(prompt: string, options?: any): Promise<any> {
        return {
            text: `Mock response for: ${prompt}`,
            model: this.defaultModel,
            usage: {
                promptTokens: prompt.length / 4,
                completionTokens: 10,
                totalTokens: prompt.length / 4 + 10
            }
        };
    }

    async generateEmbedding(text: string, options?: any): Promise<any> {
        return {
            embedding: [0.1, 0.2, 0.3],
            model: this.defaultModel,
            usage: {
                promptTokens: text.length / 4,
                totalTokens: text.length / 4
            }
        };
    }
}

// Test agent class
class TestAgent extends BaseAgent {
    constructor(
        id: string,
        name: string,
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider
    ) {
        super(id, name, 'Test agent', workerPool, llmMonitor, llmProvider);
    }

    async execute(task: string, context: any = {}): Promise<any> {
        // Use the sendToLLM method to test monitoring
        const prompt = `Execute task: ${task}\nContext: ${JSON.stringify(context)}`;
        const response = await this.sendToLLM(prompt);
        return { task, response };
    }
}

suite('Agent Monitor Integration Tests', () => {
    let monitor: LLMMonitor;
    let workerPool: WorkerPool;
    let agentSystem: X10sionAgentSystem;
    let mockProvider: MockLLMProvider;
    let largerMockProvider: MockLLMProvider;
    let testAgent: TestAgent;
    let sandbox: sinon.SinonSandbox;
    let showWarningStub: sinon.SinonStub;

    setup(() => {
        sandbox = sinon.createSandbox();

        // Mock VS Code APIs
        showWarningStub = sandbox.stub(vscode.window, 'showWarningMessage').resolves();

        // Create core services
        monitor = new LLMMonitor({
            enableIssueDetection: true,
            enableInterventions: true,
            debugMode: true
        });

        workerPool = new WorkerPool();

        // Create LLM providers
        mockProvider = new MockLLMProvider('mock', 'mock-model-small');
        largerMockProvider = new MockLLMProvider('mock-large', 'mock-model-large');

        // Create agent system
        agentSystem = new X10sionAgentSystem(workerPool, monitor, {
            debugMode: true
        });

        // Register providers
        agentSystem.registerLLMProvider(mockProvider);
        agentSystem.registerLLMProvider(largerMockProvider);

        // Create test agent
        testAgent = new TestAgent('test-agent', 'Test Agent', workerPool, monitor, mockProvider);

        // Register agent
        agentSystem.registerAgent(testAgent);
    });

    teardown(() => {
        agentSystem.dispose();
        monitor.dispose();
        workerPool.dispose();
        sandbox.restore();
    });

    test('Should track agent LLM requests and responses', async () => {
        // Initialize agent system
        await agentSystem.initialize();

        // Execute a task
        const result = await testAgent.execute('test task', { param: 'value' });

        // Check that the response was tracked
        const stats = monitor.getAgentStats(testAgent.getId());
        assert.ok(stats);
        assert.strictEqual(stats!.totalRequests, 1);
        assert.strictEqual(stats!.totalResponses, 1);
    });

    test('Should detect and handle agent issues', async () => {
        // Initialize agent system
        await agentSystem.initialize();

        // Create a spy for the agent system event emitter
        const eventSpy = sandbox.spy();
        const subscription = agentSystem.onEvent(eventSpy);

        // Manually trigger an agent intervention
        const interventionId = monitor.applyAgentIntervention(
            testAgent.getId(),
            LLMIssueType.LOOP_DETECTED,
            'Agent appears to be stuck in a loop'
        );

        assert.ok(interventionId);

        // Check that the event was fired
        assert.ok(eventSpy.called);
        assert.ok(eventSpy.calledWith(sinon.match({
            type: 'agent_intervention',
            agentId: testAgent.getId()
        })));

        subscription.dispose();
    });

    test('Should analyze agent behavior patterns', async () => {
        // Initialize agent system
        await agentSystem.initialize();

        // Execute multiple tasks to generate data
        await testAgent.execute('task 1', { param: 'value1' });
        await testAgent.execute('task 2', { param: 'value2' });
        await testAgent.execute('task 3', { param: 'value3' });

        // Analyze behavior
        const analysis = monitor.analyzeAgentBehavior(testAgent.getId());

        assert.ok(analysis);
        assert.ok(Array.isArray(analysis.patterns));
        assert.ok(Array.isArray(analysis.anomalies));
        assert.ok(Array.isArray(analysis.recommendations));
    });
});
