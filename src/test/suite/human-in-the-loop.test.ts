/**
 * Human-in-the-Loop Tests for X10sion
 *
 * This file contains tests for the human-in-the-loop functionality.
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import {
    HumanInTheLoopAgent,
    InterventionLevel,
    ActionType,
    InterventionRequest,
    InterventionResponse,
    HumanInTheLoopCapabilities
} from '../../agents/human-in-the-loop-agent.js';
import { getDefaultHumanInTheLoopConfig } from '../../agents/human-in-the-loop-config.js';
import { HumanInTheLoopDataProvider } from '../../ui/human-in-the-loop-view.js';

// Mock dependencies
class MockWorkerPool {
    execute() {
        return Promise.resolve();
    }

    getStatus() {
        return {
            maxWorkers: 4,
            activeWorkers: 0,
            queuedTasks: 0
        };
    }
}

class MockLLMMonitor {
    trackRequest() { return 'req-123'; }
    trackResponse() { return 'res-123'; }
    trackError() { return 'err-123'; }

    // Add required methods for BaseAgent
    registerAgent() {}
    unregisterAgent() {}

    getUsageStats() {
        return {
            totalRequests: 0,
            totalResponses: 0,
            totalErrors: 0,
            totalTokens: 0,
            averageLatencyMs: 0,
            errorRate: 0,
            issueRate: 0,
            modelStats: {},
            timeStats: {
                last24Hours: { requests: 0, responses: 0, errors: 0, tokens: 0 },
                last7Days: { requests: 0, responses: 0, errors: 0, tokens: 0 },
                last30Days: { requests: 0, responses: 0, errors: 0, tokens: 0 }
            }
        };
    }
}

class MockLLMProvider {
    getName() { return 'mock'; }
    getAvailableModels() { return Promise.resolve(['mock-model']); }
    generateCompletion() { return Promise.resolve({ text: 'mock response', usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 }, finishReason: 'stop', model: 'mock-model' }); }
    generateEmbedding() { return Promise.resolve({ embedding: [0.1, 0.2, 0.3], usage: { promptTokens: 10, totalTokens: 10 }, model: 'mock-model' }); }
    isAvailable() { return Promise.resolve(true); }
    getDefaultModel() { return 'mock-model'; }
}

suite('Human-in-the-Loop Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let showInformationMessageStub: sinon.SinonStub;
    let showInputBoxStub: sinon.SinonStub;
    let showQuickPickStub: sinon.SinonStub;
    let agent: HumanInTheLoopAgent;
    let dataProvider: HumanInTheLoopDataProvider;

    setup(() => {
        sandbox = sinon.createSandbox();

        // Stub VS Code API calls
        showInformationMessageStub = sandbox.stub(vscode.window, 'showInformationMessage');
        showInputBoxStub = sandbox.stub(vscode.window, 'showInputBox');
        showQuickPickStub = sandbox.stub(vscode.window, 'showQuickPick');

        // Create agent with mocked dependencies
        const workerPool = new MockWorkerPool() as any;
        const llmMonitor = new MockLLMMonitor() as any;
        const llmProvider = new MockLLMProvider() as any;
        const config = getDefaultHumanInTheLoopConfig();

        agent = new HumanInTheLoopAgent(
            workerPool,
            llmMonitor,
            llmProvider,
            config
        );

        // Create data provider
        dataProvider = new HumanInTheLoopDataProvider();
    });

    teardown(() => {
        sandbox.restore();
        agent.dispose();
    });

    test('Should auto-approve when intervention level is NONE', async () => {
        const response = await agent.requestIntervention(
            ActionType.CODE_GENERATION,
            'Generate a function',
            'function example() {}',
            [],
            {},
            InterventionLevel.NONE
        );

        assert.strictEqual(response.approved, true);
        assert.strictEqual(showInformationMessageStub.called, false);
    });

    test('Should show notification when intervention level is NOTIFICATION', async () => {
        showInformationMessageStub.resolves('OK');

        const response = await agent.requestIntervention(
            ActionType.CODE_GENERATION,
            'Generate a function',
            'function example() {}',
            [],
            {},
            InterventionLevel.NOTIFICATION
        );

        assert.strictEqual(response.approved, true);
        assert.strictEqual(showInformationMessageStub.calledOnce, true);
    });

    test('Should request approval when intervention level is APPROVAL', async () => {
        showInformationMessageStub.resolves('Approve');
        showInputBoxStub.resolves('Looks good');

        const response = await agent.requestIntervention(
            ActionType.CODE_MODIFICATION,
            'Modify a function',
            'function example() { return true; }',
            [],
            {},
            InterventionLevel.APPROVAL
        );

        assert.strictEqual(response.approved, true);
        assert.strictEqual(response.feedback, 'Looks good');
        assert.strictEqual(showInformationMessageStub.calledOnce, true);
    });

    test('Should reject when approval is denied', async () => {
        showInformationMessageStub.resolves('Reject');

        const response = await agent.requestIntervention(
            ActionType.CODE_MODIFICATION,
            'Modify a function',
            'function example() { return true; }',
            [],
            {},
            InterventionLevel.APPROVAL
        );

        assert.strictEqual(response.approved, false);
        assert.strictEqual(showInformationMessageStub.calledOnce, true);
    });

    test('Should request guidance when intervention level is GUIDANCE', async () => {
        showInputBoxStub.resolves('Use async/await instead');

        const response = await agent.requestIntervention(
            ActionType.CODE_GENERATION,
            'Generate an async function',
            'function example() { return Promise.resolve(); }',
            [],
            {},
            InterventionLevel.GUIDANCE
        );

        assert.strictEqual(response.approved, true);
        assert.strictEqual(response.modifiedAction, 'Use async/await instead');
        assert.strictEqual(showInputBoxStub.calledOnce, true);
    });

    test('Should handle alternatives in guidance request', async () => {
        showQuickPickStub.resolves('Alternative 1');

        const response = await agent.requestIntervention(
            ActionType.CODE_GENERATION,
            'Choose an implementation',
            'Implementation A',
            ['Alternative 1', 'Alternative 2'],
            {},
            InterventionLevel.GUIDANCE
        );

        assert.strictEqual(response.approved, true);
        assert.strictEqual(response.modifiedAction, 'Alternative 1');
        assert.strictEqual(response.selectedAlternative, 0);
        assert.strictEqual(showQuickPickStub.calledOnce, true);
    });

    test('Should request takeover when intervention level is TAKEOVER', async () => {
        showInformationMessageStub.resolves('Take Over');
        showInputBoxStub.resolves('I need to handle this manually');

        // Mock document opening
        const openTextDocumentStub = sandbox.stub(vscode.workspace, 'openTextDocument').resolves({} as any);
        const showTextDocumentStub = sandbox.stub(vscode.window, 'showTextDocument').resolves();

        const response = await agent.requestIntervention(
            ActionType.SECURITY_CRITICAL,
            'Review security-critical code',
            'Implement authentication',
            [],
            { filePath: 'src/auth.ts' },
            InterventionLevel.TAKEOVER
        );

        assert.strictEqual(response.approved, false);
        assert.strictEqual(response.feedback, 'I need to handle this manually');
        assert.strictEqual(showInformationMessageStub.calledOnce, true);
        assert.strictEqual(showInputBoxStub.calledOnce, true);
        assert.strictEqual(openTextDocumentStub.calledOnce, true);
        assert.strictEqual(showTextDocumentStub.calledOnce, true);
    });

    test('Data provider should track pending interventions', async () => {
        const request: InterventionRequest = {
            id: 'test-1',
            timestamp: Date.now(),
            actionType: ActionType.CODE_GENERATION,
            description: 'Test intervention',
            level: InterventionLevel.APPROVAL
        };

        dataProvider.addPendingIntervention(request);

        const children = await dataProvider.getChildren();
        assert.strictEqual(children.length, 1);
    });

    test('Data provider should complete interventions', async () => {
        const request: InterventionRequest = {
            id: 'test-1',
            timestamp: Date.now(),
            actionType: ActionType.CODE_GENERATION,
            description: 'Test intervention',
            level: InterventionLevel.APPROVAL
        };

        const response: InterventionResponse = {
            requestId: 'test-1',
            timestamp: Date.now(),
            approved: true,
            feedback: 'Looks good'
        };

        dataProvider.addPendingIntervention(request);
        dataProvider.completeIntervention('test-1', response);

        // Should move from pending to completed
        const pendingChildren = await dataProvider.getChildren();
        assert.strictEqual(pendingChildren.length, 1);
    });

    test('Data provider should clear completed interventions', async () => {
        const request: InterventionRequest = {
            id: 'test-1',
            timestamp: Date.now(),
            actionType: ActionType.CODE_GENERATION,
            description: 'Test intervention',
            level: InterventionLevel.APPROVAL
        };

        const response: InterventionResponse = {
            requestId: 'test-1',
            timestamp: Date.now(),
            approved: true,
            feedback: 'Looks good'
        };

        dataProvider.addPendingIntervention(request);
        dataProvider.completeIntervention('test-1', response);
        dataProvider.clearCompletedInterventions();

        // Should have only the "No interventions" item
        const children = await dataProvider.getChildren();
        assert.strictEqual(children.length, 1);
        assert.strictEqual(children[0].label, 'No interventions');
    });
});
