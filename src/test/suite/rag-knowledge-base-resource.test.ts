/**
 * RAG Knowledge Base Resource Tests for X10sion
 *
 * This file contains tests for the RAG knowledge base resource.
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import * as fs from 'fs';
import * as path from 'path';
import { X10sionMcpServer } from '../../mcp/server.js';
import { ragKnowledgeBaseResource, registerRagKnowledgeBaseResource } from '../../mcp/resources/rag-knowledge-base.js';

suite('RAG Knowledge Base Resource Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let server: X10sionMcpServer;
    // We'll use a modified version of the resource for testing
    let testRagKnowledgeBaseResource: any;
    let mockFs: {
        existsSync: sinon.SinonStub;
        mkdirSync: sinon.SinonStub;
        readdirSync: sinon.SinonStub;
        readFileSync: sinon.SinonStub;
        writeFileSync: sinon.SinonStub;
        unlinkSync: sinon.SinonStub;
    };
    let mockWorkspaceFolders: vscode.WorkspaceFolder[];
    let mockEntries: any[];

    setup(() => {
        sandbox = sinon.createSandbox();
        server = new X10sionMcpServer({
            logging: {
                level: 'error', // Minimize logging during tests
                format: 'text'
            }
        });

        // Create mock entries
        mockEntries = [
            {
                id: 'entry1',
                title: 'Test Entry 1',
                content: 'This is test entry 1',
                tags: ['test', 'entry1'],
                timestamp: 1621234567890
            },
            {
                id: 'entry2',
                title: 'Test Entry 2',
                content: 'This is test entry 2',
                tags: ['test', 'entry2'],
                timestamp: 1621234567891
            },
            {
                id: 'entry3',
                title: 'Another Test Entry',
                content: 'This is another test entry',
                tags: ['test', 'another'],
                timestamp: 1621234567892
            }
        ];

        // Create mock functions
        mockFs = {
            existsSync: sandbox.stub().returns(true),
            mkdirSync: sandbox.stub(),
            readdirSync: sandbox.stub().returns(['entry1.json', 'entry2.json', 'entry3.json']),
            readFileSync: sandbox.stub(),
            writeFileSync: sandbox.stub(),
            unlinkSync: sandbox.stub()
        };

        // Set up readFileSync to return mock entries
        mockFs.readFileSync.withArgs(sinon.match(/entry1\.json$/)).returns(JSON.stringify(mockEntries[0]));
        mockFs.readFileSync.withArgs(sinon.match(/entry2\.json$/)).returns(JSON.stringify(mockEntries[1]));
        mockFs.readFileSync.withArgs(sinon.match(/entry3\.json$/)).returns(JSON.stringify(mockEntries[2]));

        // Create mock workspace folders
        mockWorkspaceFolders = [
            {
                uri: vscode.Uri.file('/workspace'),
                name: 'workspace',
                index: 0
            }
        ];

        // Create a modified version of the resource for testing
        testRagKnowledgeBaseResource = { ...ragKnowledgeBaseResource };

        // Override the getData method to use our mocks
        testRagKnowledgeBaseResource.getData = async (params?: any) => {
            try {
                // If no params are provided, return all entries
                if (!params) {
                    return mockEntries;
                }

                // If a query is provided, search the knowledge base
                if (params.query) {
                    const query = params.query.toLowerCase();
                    const limit = params.limit || 10;
                    const tags = params.tags;

                    // Filter entries by tags if provided
                    let filteredEntries = mockEntries;
                    if (tags && tags.length > 0) {
                        filteredEntries = mockEntries.filter(entry => {
                            return tags.some((tag: string) => entry.tags.includes(tag));
                        });
                    }

                    // Search entries by query
                    const searchResults = filteredEntries.filter(entry => {
                        const titleMatch = entry.title.toLowerCase().includes(query);
                        const contentMatch = entry.content.toLowerCase().includes(query);
                        return titleMatch || contentMatch;
                    });

                    // Return the search results
                    return {
                        entries: searchResults.slice(0, limit),
                        totalResults: searchResults.length,
                        query
                    };
                }

                // If an ID is provided, get a specific entry
                if (params.id) {
                    const entry = mockEntries.find(e => e.id === params.id);
                    return entry || null;
                }

                // If tags are provided, filter by tags
                if (params.tags) {
                    const tags = params.tags;
                    const limit = params.limit || 10;

                    // Filter entries by tags
                    const filteredEntries = mockEntries.filter(entry => {
                        return tags.some((tag: string) => entry.tags.includes(tag));
                    });

                    // Return the limited number of entries
                    return filteredEntries.slice(0, limit);
                }

                // Default to returning all entries
                return mockEntries;
            } catch (error) {
                throw new Error(`Failed to get knowledge base entries: ${error instanceof Error ? error.message : String(error)}`);
            }
        };

        // Override the update method to use our mocks
        testRagKnowledgeBaseResource.update = async (data: any) => {
            try {
                // Validate parameters
                if (!data) {
                    throw new Error('Data is required');
                }

                // If an entry is provided, add or update it
                if (data.entry) {
                    const entry = data.entry;

                    // Validate the entry
                    if (!entry.id || !entry.title || !entry.content) {
                        throw new Error('Entry ID, title, and content are required');
                    }

                    // Ensure tags is an array
                    if (!entry.tags || !Array.isArray(entry.tags)) {
                        entry.tags = [];
                    }

                    // Set the timestamp if not provided
                    if (!entry.timestamp) {
                        entry.timestamp = Date.now();
                    }

                    // Add or update the entry in the mock entries
                    const index = mockEntries.findIndex(e => e.id === entry.id);
                    if (index >= 0) {
                        mockEntries[index] = entry;
                    } else {
                        mockEntries.push(entry);
                    }

                    return;
                }

                // If an ID is provided, delete the entry
                if (data.deleteId) {
                    const index = mockEntries.findIndex(e => e.id === data.deleteId);
                    if (index >= 0) {
                        mockEntries.splice(index, 1);
                    }

                    return;
                }

                throw new Error('Invalid update parameters');
            } catch (error) {
                throw new Error(`Failed to update knowledge base: ${error instanceof Error ? error.message : String(error)}`);
            }
        };
    });

    teardown(async () => {
        sandbox.restore();
        server.dispose();
    });

    test('Should register RAG knowledge base resource', async () => {
        await server.start();

        // Register our test resource instead of the real one
        const resourceId = await server.registerResource(testRagKnowledgeBaseResource);
        assert.strictEqual(resourceId as unknown as string, 'rag-knowledge-base');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'rag-knowledge-base');
    });

    test('Should get all knowledge base entries', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call getData method with no parameters
        const entries = await (resource as any).getData();

        // Verify the entries
        assert.strictEqual(entries.length, 3);
        assert.strictEqual(entries[0].id, 'entry1');
        assert.strictEqual(entries[1].id, 'entry2');
        assert.strictEqual(entries[2].id, 'entry3');
    });

    test('Should get entry by ID', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call getData method with ID parameter
        const entry = await (resource as any).getData({ id: 'entry2' });

        // Verify the entry
        assert.strictEqual(entry.id, 'entry2');
        assert.strictEqual(entry.title, 'Test Entry 2');
        assert.strictEqual(entry.content, 'This is test entry 2');
        assert.deepStrictEqual(entry.tags, ['test', 'entry2']);
    });

    test('Should get entries by tags', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call getData method with tags parameter
        const entries = await (resource as any).getData({ tags: ['entry2'] });

        // Verify the entries
        assert.strictEqual(entries.length, 1);
        assert.strictEqual(entries[0].id, 'entry2');
    });

    test('Should search knowledge base', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call getData method with query parameter
        const searchResult = await (resource as any).getData({ query: 'another' });

        // Verify the search result
        assert.strictEqual(searchResult.entries.length, 1);
        assert.strictEqual(searchResult.entries[0].id, 'entry3');
        assert.strictEqual(searchResult.totalResults, 1);
        assert.strictEqual(searchResult.query, 'another');
    });

    test('Should add new entry', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call update method to add a new entry
        const newEntry = {
            id: 'entry4',
            title: 'Test Entry 4',
            content: 'This is test entry 4',
            tags: ['test', 'entry4'],
            timestamp: 1621234567893
        };

        await (resource as any).update({ entry: newEntry });

        // Verify the entry was added
        const entries = await (resource as any).getData();
        assert.strictEqual(entries.length, 4);
        assert.strictEqual(entries[3].id, 'entry4');
    });

    test('Should update existing entry', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call update method to update an existing entry
        const updatedEntry = {
            id: 'entry2',
            title: 'Updated Test Entry 2',
            content: 'This is the updated test entry 2',
            tags: ['test', 'entry2', 'updated'],
            timestamp: 1621234567891
        };

        await (resource as any).update({ entry: updatedEntry });

        // Verify the entry was updated
        const entry = await (resource as any).getData({ id: 'entry2' });
        assert.strictEqual(entry.title, 'Updated Test Entry 2');
        assert.strictEqual(entry.content, 'This is the updated test entry 2');
        assert.deepStrictEqual(entry.tags, ['test', 'entry2', 'updated']);
    });

    test('Should delete entry', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call update method to delete an entry
        await (resource as any).update({ deleteId: 'entry2' });

        // Verify the entry was deleted
        const entries = await (resource as any).getData();
        assert.strictEqual(entries.length, 2);
        assert.strictEqual(entries[0].id, 'entry1');
        assert.strictEqual(entries[1].id, 'entry3');
    });

    test('Should handle invalid update parameters', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call update method with invalid parameters
        try {
            await (resource as any).update({ invalidParam: 'value' });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to update knowledge base: Invalid update parameters');
        }
    });

    test('Should validate entry parameters', async () => {
        await server.start();
        await server.registerResource(testRagKnowledgeBaseResource);

        const resource = await server.getComponent('rag-knowledge-base');
        assert.ok(resource);

        // Call update method with invalid entry
        try {
            await (resource as any).update({ entry: { id: 'entry4' } });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to update knowledge base: Entry ID, title, and content are required');
        }
    });
});
