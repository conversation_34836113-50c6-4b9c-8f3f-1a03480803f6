/**
 * Tests for the Code Analysis Agent
 */

import * as assert from 'assert';
import * as sinon from 'sinon';
import * as vscode from 'vscode';
import { CodeAnalysisAgent, IssueSeverity, IssueCategory } from '../../agents/code-analysis-agent';
import { WorkerPool } from '../../parallel/worker-pool';
import { LLMMonitor } from '../../monitoring/llm-monitor';
import { LanguageModelProvider } from '../../llm/providers/base-provider';

suite('Code Analysis Agent Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let workerPool: WorkerPool;
    let llmMonitor: LLMMonitor;
    let llmProvider: LanguageModelProvider;
    let agent: CodeAnalysisAgent;

    setup(() => {
        sandbox = sinon.createSandbox();

        // Create mock worker pool
        workerPool = {
            execute: sandbox.stub().callsFake(async (task) => {
                return await task.execute();
            }),
            dispose: sandbox.stub()
        } as unknown as WorkerPool;

        // Create mock LLM monitor
        llmMonitor = {
            onEvent: sandbox.stub().returns({ dispose: sandbox.stub() }),
            registerAgent: sandbox.stub(),
            unregisterAgent: sandbox.stub(),
            dispose: sandbox.stub()
        } as unknown as LLMMonitor;

        // Create mock LLM provider
        llmProvider = {
            getName: sandbox.stub().returns('test-provider'),
            getAvailableModels: sandbox.stub().resolves(['test-model']),
            generateCompletion: sandbox.stub().callsFake((prompt, options) => Promise.resolve({
                text: `\`\`\`json
{
  "issues": [
    {
      "id": "unused-variable",
      "title": "Unused variable",
      "description": "The variable 'unused' is declared but never used",
      "severity": "warning",
      "category": "quality",
      "lineNumber": 2,
      "codeSnippet": "const unused = 'test';",
      "suggestion": "Remove the unused variable or use it"
    },
    {
      "id": "missing-return-type",
      "title": "Missing return type",
      "description": "Function 'add' is missing a return type annotation",
      "severity": "info",
      "category": "best_practice",
      "lineNumber": 4,
      "codeSnippet": "function add(a, b) {",
      "suggestion": "Add a return type annotation: function add(a: number, b: number): number"
    }
  ],
  "metrics": [
    {
      "name": "complexity",
      "value": 1,
      "description": "Cyclomatic complexity",
      "threshold": 10,
      "status": "good"
    },
    {
      "name": "maintainability",
      "value": 85,
      "description": "Maintainability index",
      "threshold": 70,
      "status": "good"
    }
  ],
  "summary": "The code has minor issues including an unused variable and missing type annotations",
  "recommendations": [
    "Add type annotations to all functions and variables",
    "Remove unused variables"
  ],
  "codeQualityScore": 85
}
\`\`\``,
                usage: { promptTokens: 100, completionTokens: 200, totalTokens: 300 },
                finishReason: 'stop',
                model: 'test-model'
            })),
            generateEmbedding: sandbox.stub().resolves({
                embedding: [0.1, 0.2, 0.3],
                usage: { promptTokens: 5, totalTokens: 5 },
                model: 'test-model'
            }),
            isAvailable: sandbox.stub().resolves(true),
            getDefaultModel: sandbox.stub().returns('test-model')
        } as unknown as LanguageModelProvider;

        // Create agent
        agent = new CodeAnalysisAgent(
            'test-code-analysis-agent',
            workerPool,
            llmMonitor,
            llmProvider,
            {
                defaultCategories: [IssueCategory.QUALITY, IssueCategory.BEST_PRACTICE],
                defaultMinSeverity: IssueSeverity.INFO,
                includeMetricsByDefault: true,
                includeRecommendationsByDefault: true
            }
        );

        // Mock VS Code workspace
        sandbox.stub(vscode.workspace, 'openTextDocument').callsFake(async (uri: any) => {
            return {
                getText: () => `function add(a, b) {
  const unused = 'test';
  return a + b;
}`,
                languageId: 'typescript',
                uri: { fsPath: typeof uri === 'string' ? uri : uri.fsPath || '' }
            } as any;
        });

        sandbox.stub(vscode.workspace, 'textDocuments').value([
            {
                uri: { fsPath: 'test.ts' },
                getText: () => `function add(a, b) {
  const unused = 'test';
  return a + b;
}`,
                languageId: 'typescript'
            }
        ]);
    });

    teardown(() => {
        sandbox.restore();
        agent.dispose();
    });

    test('Should analyze code correctly', async () => {
        const code = `function add(a, b) {
  const unused = 'test';
  return a + b;
}`;

        const result = await agent.execute(code, { language: 'typescript' });

        assert.strictEqual(result.issues.length, 2);
        assert.strictEqual(result.metrics.length, 2);
        assert.strictEqual(result.codeQualityScore, 85);
        assert.ok(result.summary.includes('minor issues'));
        assert.strictEqual(result.recommendations.length, 2);
    });

    test('Should parse code blocks from task', async () => {
        const task = `Analyze this code:

\`\`\`typescript
function add(a, b) {
  const unused = 'test';
  return a + b;
}
\`\`\``;

        await agent.execute(task);

        // Check that the LLM provider was called with the correct prompt
        const prompt = (llmProvider.generateCompletion as sinon.SinonStub).getCall(0).args[0];
        assert.ok(prompt.includes('function add(a, b)'));
        assert.ok(prompt.includes('const unused = \'test\''));
    });

    test('Should filter issues by minimum severity', async () => {
        // Modify the LLM response to include issues with different severities
        llmProvider.generateCompletion = sandbox.stub().resolves({
            text: `\`\`\`json
{
  "issues": [
    {
      "id": "critical-issue",
      "title": "Critical issue",
      "description": "A critical security vulnerability",
      "severity": "critical",
      "category": "security",
      "lineNumber": 1
    },
    {
      "id": "error-issue",
      "title": "Error issue",
      "description": "A serious error",
      "severity": "error",
      "category": "quality",
      "lineNumber": 2
    },
    {
      "id": "warning-issue",
      "title": "Warning issue",
      "description": "A warning",
      "severity": "warning",
      "category": "best_practice",
      "lineNumber": 3
    },
    {
      "id": "info-issue",
      "title": "Info issue",
      "description": "An informational issue",
      "severity": "info",
      "category": "maintainability",
      "lineNumber": 4
    }
  ],
  "metrics": [],
  "summary": "Test summary",
  "recommendations": [],
  "codeQualityScore": 50
}
\`\`\``,
            usage: { promptTokens: 100, completionTokens: 200, totalTokens: 300 },
            finishReason: 'stop',
            model: 'test-model'
        });

        // Test with minimum severity of WARNING
        const result = await agent.execute('test code', {
            language: 'typescript',
            minSeverity: IssueSeverity.WARNING
        });

        // Should include critical, error, and warning, but not info
        assert.strictEqual(result.issues.length, 3);
        assert.ok(result.issues.some(issue => issue.id === 'critical-issue'));
        assert.ok(result.issues.some(issue => issue.id === 'error-issue'));
        assert.ok(result.issues.some(issue => issue.id === 'warning-issue'));
        assert.ok(!result.issues.some(issue => issue.id === 'info-issue'));
    });

    test('Should analyze a file', async () => {
        const result = await agent.analyzeFile('test.ts');

        assert.strictEqual(result.issues.length, 2);
        assert.strictEqual(result.metrics.length, 2);
        assert.strictEqual(result.codeQualityScore, 85);
    });

    test('Should analyze multiple files', async () => {
        const results = await agent.analyzeFiles(['test1.ts', 'test2.ts']);

        assert.strictEqual(Object.keys(results).length, 2);
        assert.ok(results['test1.ts']);
        assert.ok(results['test2.ts']);
        assert.strictEqual(results['test1.ts'].issues.length, 2);
        assert.strictEqual(results['test2.ts'].issues.length, 2);
    });

    test('Should handle malformed LLM responses', async () => {
        // Simulate a malformed response
        llmProvider.generateCompletion = sandbox.stub().resolves({
            text: 'This is not valid JSON',
            usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 },
            finishReason: 'stop',
            model: 'test-model'
        });

        const result = await agent.execute('test code', { language: 'typescript' });

        // Should return a fallback result
        assert.strictEqual(result.issues.length, 0);
        assert.strictEqual(result.metrics.length, 0);
        assert.strictEqual(result.codeQualityScore, 0);
        assert.ok(result.summary.includes('Failed to parse'));
    });

    test('Should infer language from task', async () => {
        // Test TypeScript inference
        await agent.execute('Analyze this TypeScript code');
        let prompt = (llmProvider.generateCompletion as sinon.SinonStub).getCall(0).args[0];
        assert.ok(prompt.includes('typescript code'));

        // Test Python inference
        await agent.execute('Analyze this Python code');
        prompt = (llmProvider.generateCompletion as sinon.SinonStub).getCall(1).args[0];
        assert.ok(prompt.includes('python code'));
    });
});
