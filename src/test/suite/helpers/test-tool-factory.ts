/**
 * Test Tool Factory for File System Tool Testing
 * 
 * Creates test versions of tools with mock implementations.
 */

import { fileSystemTool, FileSystemOperationType } from '../../../mcp/tools/file-system.js';
import { HumanInTheLoopAgent, InterventionLevel } from '../../../human-in-the-loop/agent.js';
import {
    MockFileSystem,
    resolvePath,
    mockReadFile,
    mockWriteFile,
    mockAppendFile,
    mockDeleteFile,
    mockCopyFile,
    mockMoveFile,
    mockCreateDirectory,
    mockListDirectory,
    mockCheckExists,
    mockGetStats
} from './file-system-mocks.js';

/**
 * Create a test version of the file system tool with mock implementations
 */
export function createTestFileSystemTool(mockFs: MockFileSystem): any {
    const testTool = { ...fileSystemTool };

    // Override the execute method to use our mock fs functions
    testTool.execute = async (options: any) => {
        try {
            // Validate options
            if (!options || !options.operation) {
                throw new Error('Operation is required');
            }

            if (!options.path) {
                throw new Error('Path is required');
            }

            const operation = options.operation;
            const filePath = resolvePath(options.path);
            const encoding = options.encoding || 'utf-8';
            const interventionLevel = options.interventionLevel || InterventionLevel.NOTIFICATION;

            // Get the human-in-the-loop agent
            const hitlAgent = HumanInTheLoopAgent.getInstance();

            // Request intervention if needed
            const intervention = await hitlAgent.requestIntervention({
                level: interventionLevel,
                title: 'File System Operation',
                message: `Do you want to perform the following operation?\n\nOperation: ${operation}\nPath: ${filePath}${options.destination ? `\nDestination: ${resolvePath(options.destination)}` : ''}`,
                actions: ['Execute', 'Cancel'],
                defaultAction: 'Execute',
                data: { operation, path: filePath, destination: options.destination }
            });

            // If the intervention was rejected, return an error
            if (intervention.rejected) {
                return {
                    operation,
                    path: filePath,
                    success: false,
                    error: 'File system operation was rejected by the user'
                };
            }

            // Perform the operation
            switch (operation) {
                case FileSystemOperationType.READ:
                    return await mockReadFile(mockFs, filePath, encoding);

                case FileSystemOperationType.WRITE:
                    if (options.content === undefined) {
                        throw new Error('Content is required for write operation');
                    }
                    return await mockWriteFile(mockFs, filePath, options.content, encoding);

                case FileSystemOperationType.APPEND:
                    if (options.content === undefined) {
                        throw new Error('Content is required for append operation');
                    }
                    return await mockAppendFile(mockFs, filePath, options.content, encoding);

                case FileSystemOperationType.DELETE:
                    return await mockDeleteFile(mockFs, filePath);

                case FileSystemOperationType.COPY:
                    if (!options.destination) {
                        throw new Error('Destination is required for copy operation');
                    }
                    return await mockCopyFile(mockFs, filePath, resolvePath(options.destination));

                case FileSystemOperationType.MOVE:
                case FileSystemOperationType.RENAME:
                    if (!options.destination) {
                        throw new Error('Destination is required for move/rename operation');
                    }
                    return await mockMoveFile(mockFs, filePath, resolvePath(options.destination));

                case FileSystemOperationType.CREATE_DIRECTORY:
                    return await mockCreateDirectory(mockFs, filePath);

                case FileSystemOperationType.LIST_DIRECTORY:
                    return await mockListDirectory(mockFs, filePath);

                case FileSystemOperationType.CHECK_EXISTS:
                    return await mockCheckExists(mockFs, filePath);

                case FileSystemOperationType.GET_STATS:
                    return await mockGetStats(mockFs, filePath);

                default:
                    throw new Error(`Unsupported operation: ${operation}`);
            }
        } catch (error) {
            throw new Error(`Failed to perform file system operation: ${error instanceof Error ? error.message : String(error)}`);
        }
    };

    return testTool;
}

/**
 * Create test setup for file system tool tests
 */
export interface TestSetup {
    mockFs: MockFileSystem;
    testTool: any;
    workspaceFoldersStub: any;
    hitlStub: any;
}

/**
 * Create a complete test setup with all necessary mocks
 */
export function createTestSetup(sandbox: any, vscode: any, HumanInTheLoopAgent: any): TestSetup {
    // Create mock fs functions
    const mockFs = {
        readFile: sandbox.stub().resolves(Buffer.from('Test file content')),
        writeFile: sandbox.stub().resolves(),
        delete: sandbox.stub().resolves(),
        copy: sandbox.stub().resolves(),
        rename: sandbox.stub().resolves(),
        createDirectory: sandbox.stub().resolves(),
        readDirectory: sandbox.stub().resolves([
            ['file1.txt', vscode.FileType.File],
            ['file2.txt', vscode.FileType.File],
            ['dir1', vscode.FileType.Directory]
        ]),
        stat: sandbox.stub().resolves({
            type: vscode.FileType.File,
            size: 1024,
            ctime: 1621234567890,
            mtime: 1621234567890
        })
    };

    // Stub the workspace.workspaceFolders property
    const workspaceFoldersStub = sandbox.stub(vscode.workspace, 'workspaceFolders').value([
        {
            uri: vscode.Uri.file('/workspace'),
            name: 'workspace',
            index: 0
        }
    ]);

    // Stub the HumanInTheLoopAgent.getInstance function
    const mockHitlAgent = {
        requestIntervention: sandbox.stub().resolves({ rejected: false, result: 'Execute' })
    };
    const hitlStub = sandbox.stub(HumanInTheLoopAgent, 'getInstance').returns(mockHitlAgent as any);

    // Create test tool
    const testTool = createTestFileSystemTool(mockFs);

    return {
        mockFs,
        testTool,
        workspaceFoldersStub,
        hitlStub
    };
}
