/**
 * File System Mock Utilities for Testing
 * 
 * Provides mock implementations of file system operations for testing.
 */

import * as vscode from 'vscode';
import * as sinon from 'sinon';
import * as path from 'path';
import { FileSystemOperationType } from '../../../mcp/tools/file-system.js';

export interface MockFileSystem {
    readFile: sinon.SinonStub;
    writeFile: sinon.SinonStub;
    delete: sinon.SinonStub;
    copy: sinon.SinonStub;
    rename: sinon.SinonStub;
    createDirectory: sinon.SinonStub;
    readDirectory: sinon.SinonStub;
    stat: sinon.SinonStub;
}

/**
 * Create mock file system functions
 */
export function createMockFileSystem(sandbox: sinon.SinonSandbox): MockFileSystem {
    return {
        readFile: sandbox.stub().resolves(Buffer.from('Test file content')),
        writeFile: sandbox.stub().resolves(),
        delete: sandbox.stub().resolves(),
        copy: sandbox.stub().resolves(),
        rename: sandbox.stub().resolves(),
        createDirectory: sandbox.stub().resolves(),
        readDirectory: sandbox.stub().resolves([
            ['file1.txt', vscode.FileType.File],
            ['file2.txt', vscode.FileType.File],
            ['dir1', vscode.FileType.Directory]
        ]),
        stat: sandbox.stub().resolves({
            type: vscode.FileType.File,
            size: 1024,
            ctime: 1621234567890,
            mtime: 1621234567890
        })
    };
}

/**
 * Helper function to resolve paths
 */
export function resolvePath(filePath: string): string {
    if (path.isAbsolute(filePath)) {
        return filePath;
    }

    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error('No workspace folder is open');
    }

    return path.join(workspaceFolders[0].uri.fsPath, filePath);
}

/**
 * Mock read file operation
 */
export async function mockReadFile(
    mockFs: MockFileSystem,
    filePath: string,
    encoding: string
): Promise<any> {
    try {
        const content = await mockFs.readFile(vscode.Uri.file(filePath));
        return {
            operation: FileSystemOperationType.READ,
            path: filePath,
            content: Buffer.from(content).toString(encoding as BufferEncoding),
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.READ,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Mock write file operation
 */
export async function mockWriteFile(
    mockFs: MockFileSystem,
    filePath: string,
    content: string,
    encoding: string
): Promise<any> {
    try {
        await mockFs.writeFile(
            vscode.Uri.file(filePath),
            Buffer.from(content, encoding as BufferEncoding)
        );
        return {
            operation: FileSystemOperationType.WRITE,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.WRITE,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Mock append file operation
 */
export async function mockAppendFile(
    mockFs: MockFileSystem,
    filePath: string,
    content: string,
    encoding: string
): Promise<any> {
    try {
        // Read the existing content
        let existingContent = '';
        try {
            const fileContent = await mockFs.readFile(vscode.Uri.file(filePath));
            existingContent = Buffer.from(fileContent).toString(encoding as BufferEncoding);
        } catch (error) {
            // File doesn't exist, start with empty content
        }

        // Append the new content
        const newContent = existingContent + content;

        // Write the combined content
        await mockFs.writeFile(
            vscode.Uri.file(filePath),
            Buffer.from(newContent, encoding as BufferEncoding)
        );

        return {
            operation: FileSystemOperationType.APPEND,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.APPEND,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Mock delete file operation
 */
export async function mockDeleteFile(mockFs: MockFileSystem, filePath: string): Promise<any> {
    try {
        await mockFs.delete(vscode.Uri.file(filePath), { recursive: true });
        return {
            operation: FileSystemOperationType.DELETE,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.DELETE,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Mock copy file operation
 */
export async function mockCopyFile(
    mockFs: MockFileSystem,
    sourcePath: string,
    destinationPath: string
): Promise<any> {
    try {
        await mockFs.copy(
            vscode.Uri.file(sourcePath),
            vscode.Uri.file(destinationPath),
            { overwrite: true }
        );
        return {
            operation: FileSystemOperationType.COPY,
            path: sourcePath,
            destination: destinationPath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.COPY,
            path: sourcePath,
            destination: destinationPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Mock move file operation
 */
export async function mockMoveFile(
    mockFs: MockFileSystem,
    sourcePath: string,
    destinationPath: string
): Promise<any> {
    try {
        await mockFs.rename(
            vscode.Uri.file(sourcePath),
            vscode.Uri.file(destinationPath),
            { overwrite: true }
        );
        return {
            operation: FileSystemOperationType.MOVE,
            path: sourcePath,
            destination: destinationPath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.MOVE,
            path: sourcePath,
            destination: destinationPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Mock create directory operation
 */
export async function mockCreateDirectory(mockFs: MockFileSystem, dirPath: string): Promise<any> {
    try {
        await mockFs.createDirectory(vscode.Uri.file(dirPath));
        return {
            operation: FileSystemOperationType.CREATE_DIRECTORY,
            path: dirPath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.CREATE_DIRECTORY,
            path: dirPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Mock list directory operation
 */
export async function mockListDirectory(mockFs: MockFileSystem, dirPath: string): Promise<any> {
    try {
        const entries = await mockFs.readDirectory(vscode.Uri.file(dirPath));

        // Convert entries to a more readable format
        const files = [];
        const directories = [];

        for (const [name, type] of entries) {
            if (type === vscode.FileType.File) {
                files.push(name);
            } else if (type === vscode.FileType.Directory) {
                directories.push(name);
            }
        }

        return {
            operation: FileSystemOperationType.LIST_DIRECTORY,
            path: dirPath,
            files,
            directories,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.LIST_DIRECTORY,
            path: dirPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Mock check exists operation
 */
export async function mockCheckExists(mockFs: MockFileSystem, filePath: string): Promise<any> {
    try {
        const stat = await mockFs.stat(vscode.Uri.file(filePath));
        return {
            operation: FileSystemOperationType.CHECK_EXISTS,
            path: filePath,
            exists: true,
            isFile: stat.type === vscode.FileType.File,
            isDirectory: stat.type === vscode.FileType.Directory,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.CHECK_EXISTS,
            path: filePath,
            exists: false,
            success: true
        };
    }
}

/**
 * Mock get stats operation
 */
export async function mockGetStats(mockFs: MockFileSystem, filePath: string): Promise<any> {
    try {
        const stat = await mockFs.stat(vscode.Uri.file(filePath));
        return {
            operation: FileSystemOperationType.GET_STATS,
            path: filePath,
            isFile: stat.type === vscode.FileType.File,
            isDirectory: stat.type === vscode.FileType.Directory,
            isSymbolicLink: stat.type === vscode.FileType.SymbolicLink,
            size: stat.size,
            createdTime: stat.ctime,
            modifiedTime: stat.mtime,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.GET_STATS,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}
