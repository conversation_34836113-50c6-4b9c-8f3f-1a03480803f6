/**
 * MCP Server Tests for X10sion
 *
 * This file contains tests for the MCP server implementation.
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import { X10sionMcpServer } from '../../mcp/server.js';
import {
    MCPEventType,
    MCPResource,
    MCPTool,
    MCPPrompt,
    MCPAgent,
    MCPComponentType,
    MCPComponentStatus
} from '../../mcp/types.js';

suite('MCP Server Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let server: X10sionMcpServer;

    setup(() => {
        sandbox = sinon.createSandbox();
        server = new X10sionMcpServer({
            logging: {
                level: 'error', // Minimize logging during tests
                format: 'text'
            }
        });
    });

    teardown(async () => {
        sandbox.restore();
        server.dispose();
    });

    test('Should create and start server', async () => {
        await server.start();
        const status = server.getStatus();
        assert.strictEqual(status.running, true);
        assert.strictEqual(status.componentCount, 0);
    });

    test('Should stop server', async () => {
        await server.start();
        await server.stop();
        const status = server.getStatus();
        assert.strictEqual(status.running, false);
    });

    test('Should register resource', async () => {
        await server.start();

        const resource: MCPResource = {
            id: 'test-resource',
            name: 'Test Resource',
            description: 'A test resource',
            type: MCPComponentType.RESOURCE,
            version: '1.0.0',
            status: MCPComponentStatus.ACTIVE,
            contentType: 'application/json',
            getData: async () => ({ test: 'test data' })
        };

        const resourceId = await server.registerResource(resource);
        assert.strictEqual(resourceId as unknown as string, 'test-resource');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'test-resource');
    });

    test('Should register tool', async () => {
        await server.start();

        const tool: MCPTool = {
            id: 'test-tool',
            name: 'Test Tool',
            description: 'A test tool',
            type: MCPComponentType.TOOL,
            version: '1.0.0',
            status: MCPComponentStatus.ACTIVE,
            parameters: [
                {
                    name: 'input',
                    type: 'string',
                    description: 'Input parameter',
                    required: true
                }
            ],
            execute: async (params: any) => ({ output: params.input })
        };

        const toolId = await server.registerTool(tool);
        assert.strictEqual(toolId as unknown as string, 'test-tool');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'test-tool');
    });

    test('Should register prompt', async () => {
        await server.start();

        const prompt: MCPPrompt = {
            id: 'test-prompt',
            name: 'Test Prompt',
            description: 'A test prompt',
            type: MCPComponentType.PROMPT,
            version: '1.0.0',
            status: MCPComponentStatus.ACTIVE,
            template: 'This is a test prompt with {{variable}}',
            variables: ['variable'],
            render: async (variables: any) => `Test prompt with ${JSON.stringify(variables)}`
        };

        const promptId = await server.registerPrompt(prompt);
        assert.strictEqual(promptId as unknown as string, 'test-prompt');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'test-prompt');
    });

    test('Should register agent', async () => {
        await server.start();

        const agent: MCPAgent = {
            id: 'test-agent',
            name: 'Test Agent',
            description: 'A test agent',
            type: MCPComponentType.AGENT,
            version: '1.0.0',
            status: MCPComponentStatus.ACTIVE,
            capabilities: ['test-capability'],
            execute: async (task: string, context: any) => ({ result: `Executed ${task}` })
        };

        const agentId = await server.registerAgent(agent);
        assert.strictEqual(agentId as unknown as string, 'test-agent');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'test-agent');
    });

    test('Should unregister component', async () => {
        await server.start();

        const resource: MCPResource = {
            id: 'test-resource',
            name: 'Test Resource',
            description: 'A test resource',
            type: MCPComponentType.RESOURCE,
            version: '1.0.0',
            status: MCPComponentStatus.ACTIVE,
            contentType: 'application/json',
            getData: async () => ({ test: 'test data' })
        };

        await server.registerResource(resource);
        await server.unregisterComponent('test-resource', 'resource');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 0);
    });

    test('Should get component by ID', async () => {
        await server.start();

        const resource: MCPResource = {
            id: 'test-resource',
            name: 'Test Resource',
            description: 'A test resource',
            type: MCPComponentType.RESOURCE,
            version: '1.0.0',
            status: MCPComponentStatus.ACTIVE,
            contentType: 'application/json',
            getData: async () => ({ test: 'test data' })
        };

        await server.registerResource(resource);

        const component = await server.getComponent('test-resource');
        assert.strictEqual(component?.id, 'test-resource');
        assert.strictEqual(component?.name, 'Test Resource');
    });

    test('Should emit events', async () => {
        const eventSpy = sinon.spy();
        const unsubscribe = server.subscribe(eventSpy);

        await server.start();
        assert.strictEqual(eventSpy.callCount, 1);
        assert.strictEqual(eventSpy.firstCall.args[0].type, MCPEventType.SERVER_STARTED);

        await server.stop();
        assert.strictEqual(eventSpy.callCount, 2);
        assert.strictEqual(eventSpy.secondCall.args[0].type, MCPEventType.SERVER_STOPPED);

        unsubscribe();
    });

    test('Should throw error when server not running', async () => {
        try {
            await server.registerResource({
                id: 'test-resource',
                name: 'Test Resource',
                description: 'A test resource',
                type: MCPComponentType.RESOURCE,
                version: '1.0.0',
                status: MCPComponentStatus.ACTIVE,
                contentType: 'application/json',
                getData: async () => ({ test: 'test data' })
            });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.code, 'SERVER_NOT_RUNNING');
        }
    });
});
