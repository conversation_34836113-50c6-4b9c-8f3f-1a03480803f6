/**
 * Guidelines Resource Tests for X10sion
 *
 * This file contains tests for the guidelines resource.
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import * as fs from 'fs';
import * as path from 'path';
import { X10sionMcpServer } from '../../mcp/server.js';
import { guidelinesResource, registerGuidelinesResource, GuidelineType } from '../../mcp/resources/guidelines.js';

suite('Guidelines Resource Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let server: X10sionMcpServer;
    // We'll use a modified version of the resource for testing
    let testGuidelinesResource: any;
    let mockFs: {
        existsSync: sinon.SinonStub;
        readFileSync: sinon.SinonStub;
    };
    let mockWorkspaceFolders: vscode.WorkspaceFolder[];

    setup(() => {
        sandbox = sinon.createSandbox();
        server = new X10sionMcpServer({
            logging: {
                level: 'error', // Minimize logging during tests
                format: 'text'
            }
        });

        // Create mock functions
        mockFs = {
            existsSync: sandbox.stub().returns(true),
            readFileSync: sandbox.stub().returns('# Test Guidelines\n\nThese are test guidelines.')
        };

        // Create mock workspace folders
        mockWorkspaceFolders = [
            {
                uri: vscode.Uri.file('/workspace'),
                name: 'workspace',
                index: 0
            }
        ];

        // Create a modified version of the resource for testing
        testGuidelinesResource = { ...guidelinesResource };

        // Override the getData method to use our mocks
        testGuidelinesResource.getData = async (params?: any) => {
            try {
                // Default to project guidelines if no type is specified
                const guidelineType = params?.type || GuidelineType.PROJECT;

                // Get the guidelines based on the type
                switch (guidelineType) {
                    case GuidelineType.PROJECT:
                        return await mockGetProjectGuidelines();
                    case GuidelineType.WORKSPACE:
                        return await mockGetWorkspaceGuidelines();
                    case GuidelineType.CODING_STANDARDS:
                        return await mockGetCodingStandardsGuidelines();
                    case GuidelineType.DOCUMENTATION:
                        return await mockGetDocumentationGuidelines();
                    case GuidelineType.TESTING:
                        return await mockGetTestingGuidelines();
                    case GuidelineType.SECURITY:
                        return await mockGetSecurityGuidelines();
                    case GuidelineType.PERFORMANCE:
                        return await mockGetPerformanceGuidelines();
                    case GuidelineType.ACCESSIBILITY:
                        return await mockGetAccessibilityGuidelines();
                    case GuidelineType.CUSTOM:
                        if (!params?.path) {
                            throw new Error('Path is required for custom guidelines');
                        }
                        return await mockGetCustomGuidelines(params.path);
                    default:
                        throw new Error(`Unknown guideline type: ${guidelineType}`);
                }
            } catch (error) {
                throw new Error(`Failed to get guidelines: ${error instanceof Error ? error.message : String(error)}`);
            }
        };

        // Mock functions for getting guidelines
        async function mockGetProjectGuidelines(): Promise<any> {
            const filePath = path.join('/workspace', 'CONTRIBUTING.md');
            if (mockFs.existsSync(filePath)) {
                const content = mockFs.readFileSync(filePath, 'utf-8');
                return {
                    type: 'project',
                    path: 'CONTRIBUTING.md',
                    content
                };
            }

            return {
                type: 'project',
                path: null,
                content: 'No project guidelines found in the workspace.'
            };
        }

        async function mockGetWorkspaceGuidelines(): Promise<any> {
            const filePath = path.join('/workspace', '.vscode/guidelines.md');
            if (mockFs.existsSync(filePath)) {
                const content = mockFs.readFileSync(filePath, 'utf-8');
                return {
                    type: 'workspace',
                    path: '.vscode/guidelines.md',
                    content
                };
            }

            return {
                type: 'workspace',
                path: null,
                content: 'No workspace guidelines found in the workspace.'
            };
        }

        async function mockGetCodingStandardsGuidelines(): Promise<any> {
            const filePath = path.join('/workspace', 'docs/coding-standards.md');
            if (mockFs.existsSync(filePath)) {
                const content = mockFs.readFileSync(filePath, 'utf-8');
                return {
                    type: 'coding-standards',
                    path: 'docs/coding-standards.md',
                    content
                };
            }

            return {
                type: 'coding-standards',
                path: null,
                content: 'No coding standards guidelines found in the workspace.'
            };
        }

        async function mockGetDocumentationGuidelines(): Promise<any> {
            const filePath = path.join('/workspace', 'docs/documentation-guidelines.md');
            if (mockFs.existsSync(filePath)) {
                const content = mockFs.readFileSync(filePath, 'utf-8');
                return {
                    type: 'documentation',
                    path: 'docs/documentation-guidelines.md',
                    content
                };
            }

            return {
                type: 'documentation',
                path: null,
                content: 'No documentation guidelines found in the workspace.'
            };
        }

        async function mockGetTestingGuidelines(): Promise<any> {
            const filePath = path.join('/workspace', 'docs/testing-guidelines.md');
            if (mockFs.existsSync(filePath)) {
                const content = mockFs.readFileSync(filePath, 'utf-8');
                return {
                    type: 'testing',
                    path: 'docs/testing-guidelines.md',
                    content
                };
            }

            return {
                type: 'testing',
                path: null,
                content: 'No testing guidelines found in the workspace.'
            };
        }

        async function mockGetSecurityGuidelines(): Promise<any> {
            const filePath = path.join('/workspace', 'SECURITY.md');
            if (mockFs.existsSync(filePath)) {
                const content = mockFs.readFileSync(filePath, 'utf-8');
                return {
                    type: 'security',
                    path: 'SECURITY.md',
                    content
                };
            }

            return {
                type: 'security',
                path: null,
                content: 'No security guidelines found in the workspace.'
            };
        }

        async function mockGetPerformanceGuidelines(): Promise<any> {
            const filePath = path.join('/workspace', 'docs/performance-guidelines.md');
            if (mockFs.existsSync(filePath)) {
                const content = mockFs.readFileSync(filePath, 'utf-8');
                return {
                    type: 'performance',
                    path: 'docs/performance-guidelines.md',
                    content
                };
            }

            return {
                type: 'performance',
                path: null,
                content: 'No performance guidelines found in the workspace.'
            };
        }

        async function mockGetAccessibilityGuidelines(): Promise<any> {
            const filePath = path.join('/workspace', 'docs/accessibility-guidelines.md');
            if (mockFs.existsSync(filePath)) {
                const content = mockFs.readFileSync(filePath, 'utf-8');
                return {
                    type: 'accessibility',
                    path: 'docs/accessibility-guidelines.md',
                    content
                };
            }

            return {
                type: 'accessibility',
                path: null,
                content: 'No accessibility guidelines found in the workspace.'
            };
        }

        async function mockGetCustomGuidelines(customPath: string): Promise<any> {
            const resolvedPath = path.join('/workspace', customPath);

            if (!mockFs.existsSync(resolvedPath)) {
                throw new Error(`Custom guidelines file not found: ${customPath}`);
            }

            const content = mockFs.readFileSync(resolvedPath, 'utf-8');

            return {
                type: 'custom',
                path: customPath,
                content
            };
        }

        // Stub the workspace.workspaceFolders property
        sandbox.stub(vscode.workspace, 'workspaceFolders').value(mockWorkspaceFolders);
    });

    teardown(async () => {
        sandbox.restore();
        server.dispose();
    });

    test('Should register guidelines resource', async () => {
        await server.start();

        // Register our test resource instead of the real one
        const resourceId = await server.registerResource(testGuidelinesResource);
        assert.strictEqual(resourceId as unknown as string, 'guidelines');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'guidelines');
    });

    test('Should get project guidelines', async () => {
        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call getData method with project type
        const data = await (resource as any).getData({ type: GuidelineType.PROJECT });

        // Verify the data
        assert.strictEqual(data.type, 'project');
        assert.ok(data.path);
        assert.strictEqual(data.content, '# Test Guidelines\n\nThese are test guidelines.');

        // Verify the fs calls
        assert.ok(mockFs.existsSync.called);
        assert.ok(mockFs.readFileSync.called);
    });

    test('Should get workspace guidelines', async () => {
        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call getData method with workspace type
        const data = await (resource as any).getData({ type: GuidelineType.WORKSPACE });

        // Verify the data
        assert.strictEqual(data.type, 'workspace');
        assert.ok(data.path);
        assert.strictEqual(data.content, '# Test Guidelines\n\nThese are test guidelines.');
    });

    test('Should get coding standards guidelines', async () => {
        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call getData method with coding standards type
        const data = await (resource as any).getData({ type: GuidelineType.CODING_STANDARDS });

        // Verify the data
        assert.strictEqual(data.type, 'coding-standards');
        assert.ok(data.path);
        assert.strictEqual(data.content, '# Test Guidelines\n\nThese are test guidelines.');
    });

    test('Should get custom guidelines', async () => {
        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call getData method with custom type and path
        const data = await (resource as any).getData({
            type: GuidelineType.CUSTOM,
            path: 'custom-guidelines.md'
        });

        // Verify the data
        assert.strictEqual(data.type, 'custom');
        assert.strictEqual(data.path, 'custom-guidelines.md');
        assert.strictEqual(data.content, '# Test Guidelines\n\nThese are test guidelines.');
    });

    test('Should handle missing custom guidelines path', async () => {
        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call getData method with custom type but no path
        try {
            await (resource as any).getData({ type: GuidelineType.CUSTOM });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to get guidelines: Path is required for custom guidelines');
        }
    });

    test('Should handle non-existent guidelines file', async () => {
        // Make the existsSync stub return false
        mockFs.existsSync.returns(false);

        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call getData method with project type
        const data = await (resource as any).getData({ type: GuidelineType.PROJECT });

        // Verify the data
        assert.strictEqual(data.type, 'project');
        assert.strictEqual(data.path, null);
        assert.strictEqual(data.content, 'No project guidelines found in the workspace.');
    });

    test('Should handle non-existent custom guidelines file', async () => {
        // Make the existsSync stub return false
        mockFs.existsSync.returns(false);

        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call getData method with custom type and path
        try {
            await (resource as any).getData({
                type: GuidelineType.CUSTOM,
                path: 'non-existent.md'
            });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to get guidelines: Custom guidelines file not found: non-existent.md');
        }
    });

    test('Should handle unknown guideline type', async () => {
        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call getData method with unknown type
        try {
            await (resource as any).getData({ type: 'unknown' });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to get guidelines: Unknown guideline type: unknown');
        }
    });

    test('Should not support update operation', async () => {
        await server.start();
        await server.registerResource(testGuidelinesResource);

        const resource = await server.getComponent('guidelines');
        assert.ok(resource);

        // Call update method and expect it to throw
        try {
            await (resource as any).update({ content: 'Updated guidelines' });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Update not supported for guidelines');
        }
    });
});
