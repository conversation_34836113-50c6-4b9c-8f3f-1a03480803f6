/**
 * File System Tool Tests for X10sion
 *
 * This file contains tests for the file system tool.
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import * as path from 'path';
import { X10sionMcpServer } from '../../mcp/server.js';
import {
    fileSystemTool,
    registerFileSystemTool,
    FileSystemOperationType
} from '../../mcp/tools/file-system.js';
import { HumanInTheLoopAgent, InterventionLevel } from '../../human-in-the-loop/agent.js';
import { createMockFileSystem, MockFileSystem } from './helpers/file-system-mocks.js';
import { createTestSetup, TestSetup } from './helpers/test-tool-factory.js';

suite('File System Tool Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let server: X10sionMcpServer;
    let testSetup: TestSetup;

    setup(() => {
        sandbox = sinon.createSandbox();
        server = new X10sionMcpServer({
            logging: {
                level: 'error', // Minimize logging during tests
                format: 'text'
            }
        });

        // Create test setup with all mocks
        testSetup = createTestSetup(sandbox, vscode, HumanInTheLoopAgent);

    });

    teardown(async () => {
        sandbox.restore();
        server.dispose();
    });

    test('Should register file system tool', async () => {
        await server.start();

        const toolId = await registerFileSystemTool(server);
        assert.strictEqual(toolId as unknown as string, 'file-system');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'file-system');
    });

    test('Should read file', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute read operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.READ,
            path: 'test.txt'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.READ);
        assert.strictEqual(result.path, path.join('/workspace', 'test.txt'));
        assert.strictEqual(result.content, 'Test file content');
        assert.strictEqual(result.success, true);

        // Verify the readFile call
        assert.strictEqual(testSetup.testSetup.mockFs.readFile.callCount, 1);
        const uri = testSetup.testSetup.mockFs.readFile.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'test.txt'));
    });

    test('Should write file', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute write operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.WRITE,
            path: 'test.txt',
            content: 'New content'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.WRITE);
        assert.strictEqual(result.path, path.join('/workspace', 'test.txt'));
        assert.strictEqual(result.success, true);

        // Verify the writeFile call
        assert.strictEqual(testSetup.testSetup.mockFs.writeFile.callCount, 1);
        const uri = testSetup.testSetup.mockFs.writeFile.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'test.txt'));

        const content = testSetup.testSetup.mockFs.writeFile.firstCall.args[1];
        assert.deepStrictEqual(content, Buffer.from('New content', 'utf-8'));
    });

    test('Should append to file', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute append operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.APPEND,
            path: 'test.txt',
            content: ' - Appended content'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.APPEND);
        assert.strictEqual(result.path, path.join('/workspace', 'test.txt'));
        assert.strictEqual(result.success, true);

        // Verify the readFile and writeFile calls
        assert.strictEqual(testSetup.mockFs.readFile.callCount, 1);
        assert.strictEqual(testSetup.mockFs.writeFile.callCount, 1);

        const writeUri = testSetup.mockFs.writeFile.firstCall.args[0];
        assert.strictEqual(writeUri.fsPath, path.join('/workspace', 'test.txt'));

        const content = testSetup.mockFs.writeFile.firstCall.args[1];
        assert.deepStrictEqual(content, Buffer.from('Test file content - Appended content', 'utf-8'));
    });

    test('Should delete file', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute delete operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.DELETE,
            path: 'test.txt'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.DELETE);
        assert.strictEqual(result.path, path.join('/workspace', 'test.txt'));
        assert.strictEqual(result.success, true);

        // Verify the delete call
        assert.strictEqual(testSetup.mockFs.delete.callCount, 1);
        const uri = testSetup.mockFs.delete.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'test.txt'));
    });

    test('Should copy file', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute copy operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.COPY,
            path: 'source.txt',
            destination: 'destination.txt'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.COPY);
        assert.strictEqual(result.path, path.join('/workspace', 'source.txt'));
        assert.strictEqual(result.destination, path.join('/workspace', 'destination.txt'));
        assert.strictEqual(result.success, true);

        // Verify the copy call
        assert.strictEqual(testSetup.mockFs.copy.callCount, 1);
        const sourceUri = testSetup.mockFs.copy.firstCall.args[0];
        const destUri = testSetup.mockFs.copy.firstCall.args[1];
        assert.strictEqual(sourceUri.fsPath, path.join('/workspace', 'source.txt'));
        assert.strictEqual(destUri.fsPath, path.join('/workspace', 'destination.txt'));
    });

    test('Should move file', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute move operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.MOVE,
            path: 'source.txt',
            destination: 'destination.txt'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.MOVE);
        assert.strictEqual(result.path, path.join('/workspace', 'source.txt'));
        assert.strictEqual(result.destination, path.join('/workspace', 'destination.txt'));
        assert.strictEqual(result.success, true);

        // Verify the rename call
        assert.strictEqual(testSetup.mockFs.rename.callCount, 1);
        const sourceUri = testSetup.mockFs.rename.firstCall.args[0];
        const destUri = testSetup.mockFs.rename.firstCall.args[1];
        assert.strictEqual(sourceUri.fsPath, path.join('/workspace', 'source.txt'));
        assert.strictEqual(destUri.fsPath, path.join('/workspace', 'destination.txt'));
    });

    test('Should create directory', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute create directory operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.CREATE_DIRECTORY,
            path: 'new-directory'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.CREATE_DIRECTORY);
        assert.strictEqual(result.path, path.join('/workspace', 'new-directory'));
        assert.strictEqual(result.success, true);

        // Verify the createDirectory call
        assert.strictEqual(testSetup.mockFs.createDirectory.callCount, 1);
        const uri = testSetup.mockFs.createDirectory.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'new-directory'));
    });

    test('Should list directory', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute list directory operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.LIST_DIRECTORY,
            path: 'directory'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.LIST_DIRECTORY);
        assert.strictEqual(result.path, path.join('/workspace', 'directory'));
        assert.strictEqual(result.success, true);
        assert.deepStrictEqual(result.files, ['file1.txt', 'file2.txt']);
        assert.deepStrictEqual(result.directories, ['dir1']);

        // Verify the readDirectory call
        assert.strictEqual(testSetup.mockFs.readDirectory.callCount, 1);
        const uri = testSetup.mockFs.readDirectory.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'directory'));
    });

    test('Should check if file exists', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute check exists operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.CHECK_EXISTS,
            path: 'test.txt'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.CHECK_EXISTS);
        assert.strictEqual(result.path, path.join('/workspace', 'test.txt'));
        assert.strictEqual(result.success, true);
        assert.strictEqual(result.exists, true);
        assert.strictEqual(result.isFile, true);
        assert.strictEqual(result.isDirectory, false);

        // Verify the stat call
        assert.strictEqual(testSetup.mockFs.stat.callCount, 1);
        const uri = testSetup.mockFs.stat.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'test.txt'));
    });

    test('Should get file stats', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Execute get stats operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.GET_STATS,
            path: 'test.txt'
        });

        // Verify the result
        assert.strictEqual(result.operation, FileSystemOperationType.GET_STATS);
        assert.strictEqual(result.path, path.join('/workspace', 'test.txt'));
        assert.strictEqual(result.success, true);
        assert.strictEqual(result.isFile, true);
        assert.strictEqual(result.isDirectory, false);
        assert.strictEqual(result.size, 1024);
        assert.strictEqual(result.createdTime, 1621234567890);
        assert.strictEqual(result.modifiedTime, 1621234567890);

        // Verify the stat call
        assert.strictEqual(testSetup.mockFs.stat.callCount, 1);
        const uri = testSetup.mockFs.stat.firstCall.args[0];
        assert.strictEqual(uri.fsPath, path.join('/workspace', 'test.txt'));
    });

    test('Should respect human-in-the-loop intervention', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Set up the HITL agent to reject the operation
        const mockHitlAgent = HumanInTheLoopAgent.getInstance();
        (mockHitlAgent.requestIntervention as sinon.SinonStub).resolves({ rejected: true });

        // Execute an operation
        const result = await (tool as any).execute({
            operation: FileSystemOperationType.DELETE,
            path: 'important-file.txt',
            interventionLevel: InterventionLevel.APPROVAL
        });

        // Verify the result indicates rejection
        assert.strictEqual(result.operation, FileSystemOperationType.DELETE);
        assert.strictEqual(result.path, path.join('/workspace', 'important-file.txt'));
        assert.strictEqual(result.success, false);
        assert.strictEqual(result.error, 'File system operation was rejected by the user');

        // Verify the delete was not called
        assert.strictEqual(testSetup.mockFs.delete.callCount, 0);
    });

    test('Should require operation parameter', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Call execute without operation and expect it to throw
        try {
            await (tool as any).execute({ path: 'test.txt' });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to perform file system operation: Operation is required');
        }
    });

    test('Should require path parameter', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Call execute without path and expect it to throw
        try {
            await (tool as any).execute({ operation: FileSystemOperationType.READ });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to perform file system operation: Path is required');
        }
    });

    test('Should require content for write operation', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Call execute with write operation but no content and expect it to throw
        try {
            await (tool as any).execute({
                operation: FileSystemOperationType.WRITE,
                path: 'test.txt'
            });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to perform file system operation: Content is required for write operation');
        }
    });

    test('Should require destination for copy operation', async () => {
        await server.start();
        await server.registerTool(testSetup.testTool);

        const tool = await server.getComponent('file-system');
        assert.ok(tool);

        // Call execute with copy operation but no destination and expect it to throw
        try {
            await (tool as any).execute({
                operation: FileSystemOperationType.COPY,
                path: 'test.txt'
            });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to perform file system operation: Destination is required for copy operation');
        }
    });
});
