/**
 * Shell Command Tool Tests for X10sion
 *
 * This file contains tests for the shell command tool.
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import * as cp from 'child_process';
import { X10sionMcpServer } from '../../mcp/server.js';
import { shellCommandTool, registerShellCommandTool } from '../../mcp/tools/shell-command.js';
import { HumanInTheLoopAgent, InterventionLevel } from '../../human-in-the-loop/agent.js';

suite('Shell Command Tool Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let server: X10sionMcpServer;
    let mockExec: sinon.SinonStub;
    let terminalStub: sinon.SinonStub;
    let hitlStub: sinon.SinonStub;
    let testShellCommandTool: any;

    setup(() => {
        sandbox = sinon.createSandbox();
        server = new X10sionMcpServer({
            logging: {
                level: 'error', // Minimize logging during tests
                format: 'text'
            }
        });

        // Create a mock exec function
        mockExec = sandbox.stub();
        mockExec.callsFake((command, options, callback) => {
            // Simulate successful command execution
            if (command === 'echo "Hello, World!"') {
                callback(null, 'Hello, World!\n', '');
            }
            // Simulate failed command execution
            else if (command === 'invalid-command') {
                const error = new Error('Command not found: invalid-command');
                (error as any).code = 127;
                callback(error, '', 'Command not found: invalid-command');
            }
            // Default case
            else {
                callback(null, `Executed: ${command}\n`, '');
            }

            // Return a mock child process
            return {
                on: () => {},
                stdout: { on: () => {} },
                stderr: { on: () => {} }
            } as any;
        });

        // Stub the vscode.window.createTerminal function
        terminalStub = sandbox.stub(vscode.window, 'createTerminal');
        terminalStub.returns({
            name: 'Shell Command',
            show: sandbox.stub(),
            sendText: sandbox.stub(),
            dispose: sandbox.stub()
        });

        // Stub the HumanInTheLoopAgent.getInstance function
        const mockHitlAgent = {
            requestIntervention: sandbox.stub().resolves({ rejected: false, result: 'Execute' })
        };
        hitlStub = sandbox.stub(HumanInTheLoopAgent, 'getInstance').returns(mockHitlAgent as any);

        // Create a modified version of the tool for testing
        testShellCommandTool = { ...shellCommandTool };

        // Override the execute method to use our mock exec function
        testShellCommandTool.execute = async (options: any) => {
            try {
                // Validate options
                if (!options || !options.command) {
                    throw new Error('Command is required');
                }

                const command = options.command;
                const cwd = options.cwd || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
                const timeout = options.timeout || 30000; // Default timeout: 30 seconds
                const showOutput = options.showOutput !== undefined ? options.showOutput : true;
                const interventionLevel = options.interventionLevel || InterventionLevel.NOTIFICATION;

                // Get the human-in-the-loop agent
                const hitlAgent = HumanInTheLoopAgent.getInstance();

                // Request intervention if needed
                const intervention = await hitlAgent.requestIntervention({
                    level: interventionLevel,
                    title: 'Shell Command Execution',
                    message: `Do you want to execute the following command?\n\n${command}\n\nWorking directory: ${cwd}`,
                    actions: ['Execute', 'Cancel'],
                    defaultAction: 'Execute',
                    data: { command, cwd }
                });

                // If the intervention was rejected, return an error
                if (intervention.rejected) {
                    return {
                        command,
                        exitCode: null,
                        stdout: '',
                        stderr: '',
                        error: 'Command execution was rejected by the user',
                        executionTime: 0
                    };
                }

                // Show the terminal if requested
                let terminal: vscode.Terminal | undefined;
                if (showOutput) {
                    terminal = vscode.window.createTerminal({
                        name: 'Shell Command',
                        cwd
                    });
                    terminal.show();
                }

                // Execute the command
                const startTime = Date.now();

                return new Promise<any>((resolve, reject) => {
                    try {
                        // If showing output in terminal, use the terminal
                        if (showOutput && terminal) {
                            terminal.sendText(command);

                            // We can't get the output from the terminal, so we'll also execute it with exec
                            mockExec(command, { cwd, timeout }, (error: Error | null, stdout: string, stderr: string) => {
                                const endTime = Date.now();
                                const executionTime = endTime - startTime;

                                if (error) {
                                    resolve({
                                        command,
                                        exitCode: (error as any).code || 1,
                                        stdout,
                                        stderr,
                                        error: error.message,
                                        executionTime
                                    });
                                } else {
                                    resolve({
                                        command,
                                        exitCode: 0,
                                        stdout,
                                        stderr,
                                        executionTime
                                    });
                                }
                            });
                        } else {
                            // If not showing output in terminal, just use exec
                            mockExec(command, { cwd, timeout }, (error: Error | null, stdout: string, stderr: string) => {
                                const endTime = Date.now();
                                const executionTime = endTime - startTime;

                                if (error) {
                                    resolve({
                                        command,
                                        exitCode: (error as any).code || 1,
                                        stdout,
                                        stderr,
                                        error: error.message,
                                        executionTime
                                    });
                                } else {
                                    resolve({
                                        command,
                                        exitCode: 0,
                                        stdout,
                                        stderr,
                                        executionTime
                                    });
                                }
                            });
                        }
                    } catch (error) {
                        const endTime = Date.now();
                        const executionTime = endTime - startTime;

                        resolve({
                            command,
                            exitCode: null,
                            stdout: '',
                            stderr: '',
                            error: error instanceof Error ? error.message : String(error),
                            executionTime
                        });
                    }
                });
            } catch (error) {
                throw new Error(`Failed to execute shell command: ${error instanceof Error ? error.message : String(error)}`);
            }
        };
    });

    teardown(async () => {
        sandbox.restore();
        server.dispose();
    });

    test('Should register shell command tool', async () => {
        await server.start();

        const toolId = await registerShellCommandTool(server);
        assert.strictEqual(toolId as unknown as string, 'shell-command');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'shell-command');
    });

    test('Should execute shell command successfully', async () => {
        await server.start();
        await server.registerTool(testShellCommandTool);

        const tool = await server.getComponent('shell-command');
        assert.ok(tool);

        // Execute a simple command
        const result = await (tool as any).execute({
            command: 'echo "Hello, World!"',
            showOutput: false
        });

        // Verify the result
        assert.strictEqual(result.command, 'echo "Hello, World!"');
        assert.strictEqual(result.exitCode, 0);
        assert.strictEqual(result.stdout, 'Hello, World!\n');
        assert.strictEqual(result.stderr, '');
        assert.ok(result.executionTime >= 0);

        // Verify the exec call
        assert.strictEqual(mockExec.callCount, 1);
        assert.strictEqual(mockExec.firstCall.args[0], 'echo "Hello, World!"');
    });

    test('Should handle command failure', async () => {
        await server.start();
        await server.registerTool(testShellCommandTool);

        const tool = await server.getComponent('shell-command');
        assert.ok(tool);

        // Execute an invalid command
        const result = await (tool as any).execute({
            command: 'invalid-command',
            showOutput: false
        });

        // Verify the result
        assert.strictEqual(result.command, 'invalid-command');
        assert.strictEqual(result.exitCode, 127);
        assert.strictEqual(result.stdout, '');
        assert.strictEqual(result.stderr, 'Command not found: invalid-command');
        assert.strictEqual(result.error, 'Command not found: invalid-command');
        assert.ok(result.executionTime >= 0);
    });

    test('Should show output in terminal when requested', async () => {
        await server.start();
        await server.registerTool(testShellCommandTool);

        const tool = await server.getComponent('shell-command');
        assert.ok(tool);

        // Execute a command with terminal output
        const result = await (tool as any).execute({
            command: 'echo "Hello, Terminal!"',
            showOutput: true
        });

        // Verify the terminal was created and used
        assert.strictEqual(terminalStub.callCount, 1);
        const terminal = terminalStub.firstCall.returnValue;
        assert.strictEqual(terminal.show.callCount, 1);
        assert.strictEqual(terminal.sendText.callCount, 1);
        assert.strictEqual(terminal.sendText.firstCall.args[0], 'echo "Hello, Terminal!"');
    });

    test('Should respect human-in-the-loop intervention', async () => {
        await server.start();
        await server.registerTool(testShellCommandTool);

        const tool = await server.getComponent('shell-command');
        assert.ok(tool);

        // Set up the HITL agent to reject the command
        const mockHitlAgent = HumanInTheLoopAgent.getInstance();
        (mockHitlAgent.requestIntervention as sinon.SinonStub).resolves({ rejected: true });

        // Execute a command
        const result = await (tool as any).execute({
            command: 'echo "This should be rejected"',
            interventionLevel: InterventionLevel.APPROVAL
        });

        // Verify the result indicates rejection
        assert.strictEqual(result.command, 'echo "This should be rejected"');
        assert.strictEqual(result.exitCode, null);
        assert.strictEqual(result.stdout, '');
        assert.strictEqual(result.stderr, '');
        assert.strictEqual(result.error, 'Command execution was rejected by the user');
        assert.strictEqual(result.executionTime, 0);

        // Verify the exec was not called
        assert.strictEqual(mockExec.callCount, 0);
    });

    test('Should require command parameter', async () => {
        await server.start();
        await server.registerTool(testShellCommandTool);

        const tool = await server.getComponent('shell-command');
        assert.ok(tool);

        // Call execute without command and expect it to throw
        try {
            await (tool as any).execute({});
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to execute shell command: Command is required');
        }
    });

    test('Should use default values for optional parameters', async () => {
        await server.start();
        await server.registerTool(testShellCommandTool);

        const tool = await server.getComponent('shell-command');
        assert.ok(tool);

        // Execute a command with minimal options
        await (tool as any).execute({ command: 'echo "Default Values"' });

        // Verify the exec call used default values
        assert.strictEqual(mockExec.callCount, 1);
        const options = mockExec.firstCall.args[1];
        assert.ok(options.timeout); // Should have a default timeout

        // Verify the terminal was created (default showOutput is true)
        assert.strictEqual(terminalStub.callCount, 1);
    });
});
