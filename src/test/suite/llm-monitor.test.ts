import * as assert from 'assert';
import * as sinon from 'sinon';
import * as vscode from 'vscode';
import { LLMMonitor, LLMIssueType, InterventionStrategy } from '../../monitoring/llm-monitor';

suite('LLM Monitor Tests', () => {
    let monitor: LLMMonitor;
    let sandbox: sinon.SinonSandbox;
    let showInfoStub: sinon.SinonStub;
    let showErrorStub: sinon.SinonStub;
    let createStatusBarStub: sinon.SinonStub;
    let statusBarItem: any;

    setup(() => {
        sandbox = sinon.createSandbox();
        
        // Mock VS Code APIs
        statusBarItem = {
            text: '',
            tooltip: '',
            command: '',
            show: sandbox.stub(),
            dispose: sandbox.stub()
        };
        
        showInfoStub = sandbox.stub(vscode.window, 'showInformationMessage').resolves();
        showErrorStub = sandbox.stub(vscode.window, 'showErrorMessage').resolves();
        createStatusBarStub = sandbox.stub(vscode.window, 'createStatusBarItem').returns(statusBarItem);
        
        // Create monitor with test options
        monitor = new LLMMonitor({
            maxHistoryItems: 100,
            enableIssueDetection: true,
            issueDetectionConfidenceThreshold: 0.6,
            enableTokenCounting: true,
            enableInterventions: true,
            debugMode: true
        });
    });

    teardown(() => {
        monitor.dispose();
        sandbox.restore();
    });

    test('Should initialize with correct options', () => {
        assert.strictEqual(createStatusBarStub.calledOnce, true);
        assert.strictEqual(statusBarItem.show.calledOnce, true);
    });

    test('Should track requests and responses', () => {
        const requestId = monitor.trackRequest('test-model', 'Test prompt', {});
        
        assert.ok(requestId.startsWith('req-'));
        
        const responseId = monitor.trackResponse(
            requestId,
            'test-model',
            'Test response',
            100,
            10
        );
        
        assert.ok(responseId.startsWith('res-'));
        
        const stats = monitor.getUsageStats();
        assert.strictEqual(stats.totalRequests, 1);
        assert.strictEqual(stats.totalResponses, 1);
        assert.strictEqual(stats.totalTokens, 10);
    });

    test('Should track errors', () => {
        const requestId = monitor.trackRequest('test-model', 'Test prompt', {});
        const error = new Error('Test error');
        
        const errorId = monitor.trackError(requestId, 'test-model', error);
        
        assert.ok(errorId.startsWith('err-'));
        
        const stats = monitor.getUsageStats();
        assert.strictEqual(stats.totalErrors, 1);
    });

    test('Should detect repetition issues', () => {
        const requestId = monitor.trackRequest('test-model', 'Test prompt', {});
        
        // Create a response with repetition
        const repeatedText = 'This is a test. '.repeat(10);
        
        const responseId = monitor.trackResponse(
            requestId,
            'test-model',
            repeatedText,
            100
        );
        
        // Check if issue was detected
        const stats = monitor.getUsageStats();
        assert.ok(stats.issueRate > 0);
    });

    test('Should detect incomplete response issues', () => {
        const requestId = monitor.trackRequest('test-model', 'Test prompt', {});
        
        // Create an incomplete response
        const incompleteText = 'This is an incomplete response with unclosed code block: ```javascript\nfunction test() {';
        
        const responseId = monitor.trackResponse(
            requestId,
            'test-model',
            incompleteText,
            100
        );
        
        // Check if issue was detected
        const stats = monitor.getUsageStats();
        assert.ok(stats.issueRate > 0);
    });

    test('Should detect hallucination issues', () => {
        const requestId = monitor.trackRequest('test-model', 'Tell me about TypeScript', {});
        
        // Create a response with hallucinated URL
        const hallucinatedText = 'TypeScript is a programming language. You can learn more at https://fake-typescript-docs.com';
        
        const responseId = monitor.trackResponse(
            requestId,
            'test-model',
            hallucinatedText,
            100
        );
        
        // Check if issue was detected
        const stats = monitor.getUsageStats();
        assert.ok(stats.issueRate > 0);
    });

    test('Should apply interventions for issues', () => {
        // Enable interventions
        (monitor as any).options.enableInterventions = true;
        
        const requestId = monitor.trackRequest('test-model', 'Tell me about TypeScript', {});
        
        // Create a response with hallucinated URL
        const hallucinatedText = 'TypeScript is a programming language. You can learn more at https://fake-typescript-docs.com';
        
        const responseId = monitor.trackResponse(
            requestId,
            'test-model',
            hallucinatedText,
            100
        );
        
        // Check if intervention was applied
        assert.ok(showInfoStub.called);
        
        // Check status bar update
        assert.ok(statusBarItem.text.includes('issue'));
    });

    test('Should handle multiple issues and interventions', () => {
        const requestId = monitor.trackRequest('test-model', 'Tell me about TypeScript', {});
        
        // Create a response with multiple issues
        const problematicText = 'TypeScript is a programming language. '.repeat(5) + 
            'You can learn more at https://fake-typescript-docs.com. ' +
            'Here is some code: ```typescript\nfunction test() {';
        
        const responseId = monitor.trackResponse(
            requestId,
            'test-model',
            problematicText,
            100
        );
        
        // Check if multiple issues were detected
        const stats = monitor.getUsageStats();
        assert.ok(stats.issueRate > 0);
        
        // Check if interventions were applied
        assert.ok(showInfoStub.called);
    });
});
