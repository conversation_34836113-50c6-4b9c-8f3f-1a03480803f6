/**
 * Editor Context Resource Tests for X10sion
 *
 * This file contains tests for the editor context resource.
 */

import * as assert from 'assert';
import * as vscode from 'vscode';
import * as sinon from 'sinon';
import { X10sionMcpServer } from '../../mcp/server.js';
import { editorContextResource, registerEditorContextResource } from '../../mcp/resources/editor-context.js';
import * as extension from '../../extension.js';

suite('Editor Context Resource Tests', () => {
    let sandbox: sinon.SinonSandbox;
    let server: X10sionMcpServer;
    let getActiveEditorContextStub: sinon.SinonStub;

    setup(() => {
        sandbox = sinon.createSandbox();
        server = new X10sionMcpServer({
            logging: {
                level: 'error', // Minimize logging during tests
                format: 'text'
            }
        });

        // Stub the getActiveEditorContext function
        getActiveEditorContextStub = sandbox.stub(extension, 'getActiveEditorContext');
    });

    teardown(async () => {
        sandbox.restore();
        server.dispose();
    });

    test('Should register editor context resource', async () => {
        await server.start();

        const resourceId = await registerEditorContextResource(server);
        assert.strictEqual(resourceId as unknown as string, 'editor-context');

        const components = await server.getComponents();
        assert.strictEqual(components.length, 1);
        assert.strictEqual(components[0].id, 'editor-context');
    });

    test('Should get editor context data', async () => {
        // Mock editor context data
        const mockContext = {
            fileName: 'test.ts',
            language: 'typescript',
            selection: {
                start: { line: 1, character: 0 },
                end: { line: 2, character: 10 }
            },
            content: 'const test = "hello";\nconsole.log(test);'
        };

        getActiveEditorContextStub.resolves(mockContext);

        await server.start();
        await registerEditorContextResource(server);

        const resource = await server.getComponent('editor-context');
        assert.ok(resource);

        // Call getData method
        const data = await (resource as any).getData();

        // Verify the data
        assert.deepStrictEqual(data, mockContext);
        assert.strictEqual(getActiveEditorContextStub.callCount, 1);
    });

    test('Should handle errors when getting editor context', async () => {
        // Make the stub throw an error
        getActiveEditorContextStub.rejects(new Error('Test error'));

        await server.start();
        await registerEditorContextResource(server);

        const resource = await server.getComponent('editor-context');
        assert.ok(resource);

        // Call getData method and expect it to throw
        try {
            await (resource as any).getData();
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Failed to get editor context');
        }

        assert.strictEqual(getActiveEditorContextStub.callCount, 1);
    });

    test('Should not support update operation', async () => {
        await server.start();
        await registerEditorContextResource(server);

        const resource = await server.getComponent('editor-context');
        assert.ok(resource);

        // Call update method and expect it to throw
        try {
            await (resource as any).update({ test: 'data' });
            assert.fail('Should have thrown an error');
        } catch (error: any) {
            assert.strictEqual(error.message, 'Update not supported for editor context');
        }
    });
});
