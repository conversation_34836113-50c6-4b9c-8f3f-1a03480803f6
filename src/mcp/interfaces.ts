/**
 * MCP Interfaces for X10sion
 *
 * This module defines the interfaces for the Model Context Protocol (MCP) implementation.
 * Based on the MCP specification from May 2025.
 */

import {
    MCPComponent,
    MCPResource,
    MCPTool,
    MCPPrompt,
    MCPAgent,
    MCPEvent,
    MCPServerOptions,
    MCPClientOptions,
    ResourceId,
    ToolId,
    PromptId,
    AgentId
} from './types.js';

/**
 * MCP Server Interface
 */
export interface MCPServer {
    /**
     * Start the MCP server
     */
    start(): Promise<void>;

    /**
     * Stop the MCP server
     */
    stop(): Promise<void>;

    /**
     * Register a resource with the server
     * @param resource Resource to register
     * @returns Resource ID
     */
    registerResource(resource: MCPResource): Promise<ResourceId>;

    /**
     * Register a tool with the server
     * @param tool Tool to register
     * @returns Tool ID
     */
    registerTool(tool: MCPTool): Promise<ToolId>;

    /**
     * Register a prompt with the server
     * @param prompt Prompt to register
     * @returns Prompt ID
     */
    registerPrompt(prompt: MCPPrompt): Promise<PromptId>;

    /**
     * Register an agent with the server
     * @param agent Agent to register
     * @returns Agent ID
     */
    registerAgent(agent: MCPAgent): Promise<AgentId>;

    /**
     * Unregister a component from the server
     * @param id Component ID
     * @param type Component type
     */
    unregisterComponent(id: string, type: string): Promise<void>;

    /**
     * Get all registered components
     * @returns All registered components
     */
    getComponents(): Promise<MCPComponent[]>;

    /**
     * Get a component by ID
     * @param id Component ID
     * @returns Component or undefined if not found
     */
    getComponent(id: string): Promise<MCPComponent | undefined>;

    /**
     * Subscribe to server events
     * @param callback Callback function for events
     * @returns Unsubscribe function
     */
    subscribe(callback: (event: MCPEvent) => void): () => void;
}

/**
 * MCP Client Interface
 */
export interface MCPClient {
    /**
     * Connect to the MCP server
     */
    connect(): Promise<void>;

    /**
     * Disconnect from the MCP server
     */
    disconnect(): Promise<void>;

    /**
     * Get all available resources
     * @returns All available resources
     */
    getResources(): Promise<MCPResource[]>;

    /**
     * Get a resource by ID
     * @param id Resource ID
     * @returns Resource or undefined if not found
     */
    getResource(id: string): Promise<MCPResource | undefined>;

    /**
     * Get resource data
     * @param id Resource ID
     * @returns Resource data
     */
    getResourceData(id: string): Promise<any>;

    /**
     * Update resource data
     * @param id Resource ID
     * @param data New resource data
     */
    updateResourceData(id: string, data: any): Promise<void>;

    /**
     * Get all available tools
     * @returns All available tools
     */
    getTools(): Promise<MCPTool[]>;

    /**
     * Get a tool by ID
     * @param id Tool ID
     * @returns Tool or undefined if not found
     */
    getTool(id: string): Promise<MCPTool | undefined>;

    /**
     * Execute a tool
     * @param id Tool ID
     * @param params Tool parameters
     * @returns Tool execution result
     */
    executeTool(id: string, params: Record<string, any>): Promise<any>;

    /**
     * Get all available prompts
     * @returns All available prompts
     */
    getPrompts(): Promise<MCPPrompt[]>;

    /**
     * Get a prompt by ID
     * @param id Prompt ID
     * @returns Prompt or undefined if not found
     */
    getPrompt(id: string): Promise<MCPPrompt | undefined>;

    /**
     * Render a prompt
     * @param id Prompt ID
     * @param variables Prompt variables
     * @returns Rendered prompt
     */
    renderPrompt(id: string, variables: Record<string, any>): Promise<string>;

    /**
     * Get all available agents
     * @returns All available agents
     */
    getAgents(): Promise<MCPAgent[]>;

    /**
     * Get an agent by ID
     * @param id Agent ID
     * @returns Agent or undefined if not found
     */
    getAgent(id: string): Promise<MCPAgent | undefined>;

    /**
     * Execute an agent
     * @param id Agent ID
     * @param task Task to execute
     * @param context Task context
     * @returns Agent execution result
     */
    executeAgent(id: string, task: string, context: any): Promise<any>;

    /**
     * Subscribe to client events
     * @param callback Callback function for events
     * @returns Unsubscribe function
     */
    subscribe(callback: (event: MCPEvent) => void): () => void;
}

/**
 * MCP Resource Handler Interface
 */
export interface MCPResourceHandler {
    /**
     * Get resource data
     * @returns Resource data
     */
    getData(): Promise<any>;

    /**
     * Update resource data
     * @param data New resource data
     */
    update?(data: any): Promise<void>;
}

/**
 * MCP Tool Handler Interface
 */
export interface MCPToolHandler {
    /**
     * Execute the tool
     * @param params Tool parameters
     * @returns Tool execution result
     */
    execute(params: Record<string, any>): Promise<any>;
}

/**
 * MCP Prompt Handler Interface
 */
export interface MCPPromptHandler {
    /**
     * Render the prompt
     * @param variables Prompt variables
     * @returns Rendered prompt
     */
    render(variables: Record<string, any>): Promise<string>;
}

/**
 * MCP Agent Handler Interface
 */
export interface MCPAgentHandler {
    /**
     * Execute the agent
     * @param task Task to execute
     * @param context Task context
     * @returns Agent execution result
     */
    execute(task: string, context: any): Promise<any>;
}

/**
 * MCP Event Handler Interface
 */
export interface MCPEventHandler {
    /**
     * Handle an event
     * @param event Event to handle
     */
    handleEvent(event: MCPEvent): void;
}
