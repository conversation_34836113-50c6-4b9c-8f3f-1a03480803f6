/**
 * MCP Utilities for X10sion
 *
 * This module provides utility functions for working with MCP components.
 */

import {
    MCPComponent,
    MCPResource,
    MCPTool,
    MCPPrompt,
    MCPAgent,
    MCPComponentType,
    MCPComponentStatus,
    mcpComponentSchema,
    mcpResourceSchema,
    mcpToolSchema,
    mcpPromptSchema,
    mcpAgentSchema,
    isResource,
    isTool,
    isPrompt,
    isAgent
} from './types.js';

/**
 * Validate a component
 * @param component Component to validate
 * @returns Validation result
 */
export function validateComponent(component: any): { valid: boolean; errors?: string[] } {
    const result = mcpComponentSchema.safeParse(component);

    if (!result.success) {
        return {
            valid: false,
            errors: result.error.errors.map((err: any) => `${err.path.join('.')}: ${err.message}`)
        };
    }

    // Validate specific component type
    switch (component.type) {
        case MCPComponentType.RESOURCE:
            return validateResource(component);

        case MCPComponentType.TOOL:
            return validateTool(component);

        case MCPComponentType.PROMPT:
            return validatePrompt(component);

        case MCPComponentType.AGENT:
            return validateAgent(component);

        default:
            return { valid: true };
    }
}

/**
 * Validate a resource
 * @param resource Resource to validate
 * @returns Validation result
 */
export function validateResource(resource: any): { valid: boolean; errors?: string[] } {
    const result = mcpResourceSchema.safeParse(resource);

    if (!result.success) {
        return {
            valid: false,
            errors: result.error.errors.map((err: any) => `${err.path.join('.')}: ${err.message}`)
        };
    }

    // Check if getData is a function
    if (typeof resource.getData !== 'function') {
        return {
            valid: false,
            errors: ['getData must be a function']
        };
    }

    // Check if update is a function if provided
    if (resource.update !== undefined && typeof resource.update !== 'function') {
        return {
            valid: false,
            errors: ['update must be a function if provided']
        };
    }

    return { valid: true };
}

/**
 * Validate a tool
 * @param tool Tool to validate
 * @returns Validation result
 */
export function validateTool(tool: any): { valid: boolean; errors?: string[] } {
    const result = mcpToolSchema.safeParse(tool);

    if (!result.success) {
        return {
            valid: false,
            errors: result.error.errors.map((err: any) => `${err.path.join('.')}: ${err.message}`)
        };
    }

    // Check if execute is a function
    if (typeof tool.execute !== 'function') {
        return {
            valid: false,
            errors: ['execute must be a function']
        };
    }

    return { valid: true };
}

/**
 * Validate a prompt
 * @param prompt Prompt to validate
 * @returns Validation result
 */
export function validatePrompt(prompt: any): { valid: boolean; errors?: string[] } {
    const result = mcpPromptSchema.safeParse(prompt);

    if (!result.success) {
        return {
            valid: false,
            errors: result.error.errors.map((err: any) => `${err.path.join('.')}: ${err.message}`)
        };
    }

    // Check if render is a function
    if (typeof prompt.render !== 'function') {
        return {
            valid: false,
            errors: ['render must be a function']
        };
    }

    // Check if all variables in the template are in the variables array
    const templateVariables = extractVariablesFromTemplate(prompt.template);
    const missingVariables = templateVariables.filter(v => !prompt.variables.includes(v));

    if (missingVariables.length > 0) {
        return {
            valid: false,
            errors: [`Template contains variables not listed in variables array: ${missingVariables.join(', ')}`]
        };
    }

    return { valid: true };
}

/**
 * Validate an agent
 * @param agent Agent to validate
 * @returns Validation result
 */
export function validateAgent(agent: any): { valid: boolean; errors?: string[] } {
    const result = mcpAgentSchema.safeParse(agent);

    if (!result.success) {
        return {
            valid: false,
            errors: result.error.errors.map((err: any) => `${err.path.join('.')}: ${err.message}`)
        };
    }

    // Check if execute is a function
    if (typeof agent.execute !== 'function') {
        return {
            valid: false,
            errors: ['execute must be a function']
        };
    }

    return { valid: true };
}

/**
 * Extract variables from a template
 * @param template Template string
 * @returns Array of variable names
 */
export function extractVariablesFromTemplate(template: string): string[] {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables: string[] = [];
    let match;

    while ((match = variableRegex.exec(template)) !== null) {
        variables.push(match[1].trim());
    }

    return [...new Set(variables)]; // Remove duplicates
}

/**
 * Serialize a component to JSON
 * @param component Component to serialize
 * @returns Serialized component
 */
export function serializeComponent(component: MCPComponent): any {
    const serialized = { ...component };

    // Remove functions
    if (isResource(component)) {
        delete (serialized as any).getData;
        delete (serialized as any).update;
    } else if (isTool(component)) {
        delete (serialized as any).execute;
    } else if (isPrompt(component)) {
        delete (serialized as any).render;
    } else if (isAgent(component)) {
        delete (serialized as any).execute;
    }

    return serialized;
}

/**
 * Create a unique ID for a component
 * @param type Component type
 * @param name Component name
 * @returns Unique ID
 */
export function createComponentId(type: MCPComponentType, name: string): string {
    const normalizedName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 6);

    return `${type}-${normalizedName}-${timestamp}-${random}`;
}

/**
 * Create an error object
 * @param message Error message
 * @param code Error code
 * @param details Error details
 * @returns Error object
 */
export function createError(message: string, code: string, details?: any): Error {
    const error = new Error(message) as any;
    error.code = code;
    error.details = details;
    return error;
}

/**
 * Log a message
 * @param level Log level
 * @param message Log message
 * @param data Additional data
 */
export function log(level: 'error' | 'warn' | 'info' | 'debug', message: string, data?: any): void {
    const timestamp = new Date().toISOString();

    switch (level) {
        case 'error':
            console.error(`[${timestamp}] ERROR: ${message}`, data);
            break;

        case 'warn':
            console.warn(`[${timestamp}] WARN: ${message}`, data);
            break;

        case 'info':
            console.info(`[${timestamp}] INFO: ${message}`, data);
            break;

        case 'debug':
            console.debug(`[${timestamp}] DEBUG: ${message}`, data);
            break;
    }
}
