/**
 * MCP Types for X10sion
 * 
 * This module defines the types for the Model Context Protocol (MCP) implementation.
 * Based on the MCP specification from May 2025.
 */

import { z } from 'zod';

/**
 * MCP Component Types
 */
export enum MCPComponentType {
    RESOURCE = 'resource',
    TOOL = 'tool',
    PROMPT = 'prompt',
    AGENT = 'agent'
}

/**
 * MCP Component Status
 */
export enum MCPComponentStatus {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    ERROR = 'error'
}

/**
 * Base MCP Component
 */
export interface MCPComponent {
    id: string;
    name: string;
    description: string;
    type: MCPComponentType;
    version: string;
    status: MCPComponentStatus;
    metadata?: Record<string, any>;
}

/**
 * MCP Resource
 */
export interface MCPResource extends MCPComponent {
    type: MCPComponentType.RESOURCE;
    contentType: string;
    getData: () => Promise<any>;
    update?: (data: any) => Promise<void>;
}

/**
 * MCP Tool Parameter
 */
export interface MCPToolParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    description: string;
    required: boolean;
    default?: any;
    enum?: any[];
    schema?: Record<string, any>;
}

/**
 * MCP Tool
 */
export interface MCPTool extends MCPComponent {
    type: MCPComponentType.TOOL;
    parameters: MCPToolParameter[];
    execute: (params: Record<string, any>) => Promise<any>;
}

/**
 * MCP Prompt
 */
export interface MCPPrompt extends MCPComponent {
    type: MCPComponentType.PROMPT;
    template: string;
    variables: string[];
    render: (variables: Record<string, any>) => Promise<string>;
}

/**
 * MCP Agent
 */
export interface MCPAgent extends MCPComponent {
    type: MCPComponentType.AGENT;
    capabilities: string[];
    execute: (task: string, context: any) => Promise<any>;
}

/**
 * MCP Server Options
 */
export interface MCPServerOptions {
    port?: number;
    host?: string;
    cors?: boolean;
    auth?: {
        enabled: boolean;
        apiKey?: string;
        jwt?: {
            secret: string;
            expiresIn: string;
        };
    };
    ssl?: {
        enabled: boolean;
        key: string;
        cert: string;
    };
    logging?: {
        level: 'error' | 'warn' | 'info' | 'debug';
        format: 'json' | 'text';
    };
}

/**
 * MCP Client Options
 */
export interface MCPClientOptions {
    serverUrl: string;
    auth?: {
        apiKey?: string;
        jwt?: string;
    };
    timeout?: number;
    retries?: number;
    logging?: {
        level: 'error' | 'warn' | 'info' | 'debug';
        format: 'json' | 'text';
    };
}

/**
 * MCP Event Types
 */
export enum MCPEventType {
    COMPONENT_ADDED = 'component.added',
    COMPONENT_UPDATED = 'component.updated',
    COMPONENT_REMOVED = 'component.removed',
    COMPONENT_ERROR = 'component.error',
    SERVER_STARTED = 'server.started',
    SERVER_STOPPED = 'server.stopped',
    CLIENT_CONNECTED = 'client.connected',
    CLIENT_DISCONNECTED = 'client.disconnected'
}

/**
 * MCP Event
 */
export interface MCPEvent {
    type: MCPEventType;
    timestamp: number;
    data: any;
}

/**
 * Zod Schemas for Runtime Validation
 */

// Base MCP Component Schema
export const mcpComponentSchema = z.object({
    id: z.string().min(1),
    name: z.string().min(1),
    description: z.string(),
    type: z.nativeEnum(MCPComponentType),
    version: z.string().regex(/^\d+\.\d+\.\d+$/),
    status: z.nativeEnum(MCPComponentStatus),
    metadata: z.record(z.any()).optional()
});

// MCP Resource Schema
export const mcpResourceSchema = mcpComponentSchema.extend({
    type: z.literal(MCPComponentType.RESOURCE),
    contentType: z.string().min(1)
});

// MCP Tool Parameter Schema
export const mcpToolParameterSchema = z.object({
    name: z.string().min(1),
    type: z.enum(['string', 'number', 'boolean', 'object', 'array']),
    description: z.string(),
    required: z.boolean(),
    default: z.any().optional(),
    enum: z.array(z.any()).optional(),
    schema: z.record(z.any()).optional()
});

// MCP Tool Schema
export const mcpToolSchema = mcpComponentSchema.extend({
    type: z.literal(MCPComponentType.TOOL),
    parameters: z.array(mcpToolParameterSchema)
});

// MCP Prompt Schema
export const mcpPromptSchema = mcpComponentSchema.extend({
    type: z.literal(MCPComponentType.PROMPT),
    template: z.string().min(1),
    variables: z.array(z.string())
});

// MCP Agent Schema
export const mcpAgentSchema = mcpComponentSchema.extend({
    type: z.literal(MCPComponentType.AGENT),
    capabilities: z.array(z.string())
});

// Type Guards
export function isResource(component: MCPComponent): component is MCPResource {
    return component.type === MCPComponentType.RESOURCE;
}

export function isTool(component: MCPComponent): component is MCPTool {
    return component.type === MCPComponentType.TOOL;
}

export function isPrompt(component: MCPComponent): component is MCPPrompt {
    return component.type === MCPComponentType.PROMPT;
}

export function isAgent(component: MCPComponent): component is MCPAgent {
    return component.type === MCPComponentType.AGENT;
}

// Branded Types for Enhanced Type Safety
export type ResourceId = string & { __brand: 'ResourceId' };
export type ToolId = string & { __brand: 'ToolId' };
export type PromptId = string & { __brand: 'PromptId' };
export type AgentId = string & { __brand: 'AgentId' };

export function createResourceId(id: string): ResourceId {
    return id as ResourceId;
}

export function createToolId(id: string): ToolId {
    return id as ToolId;
}

export function createPromptId(id: string): PromptId {
    return id as PromptId;
}

export function createAgentId(id: string): AgentId {
    return id as AgentId;
}
