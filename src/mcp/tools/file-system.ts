/**
 * File System Tool for MCP
 *
 * This module provides a tool for performing file system operations.
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import {
    MCPTool,
    MCPComponentType,
    MCPComponentStatus,
    ToolId
} from '../types.js';
import { log } from '../utils.js';
import { HumanInTheLoopAgent, InterventionLevel } from '../../human-in-the-loop/agent.js';

/**
 * File system operation types
 */
export enum FileSystemOperationType {
    READ = 'read',
    WRITE = 'write',
    APPEND = 'append',
    DELETE = 'delete',
    COPY = 'copy',
    MOVE = 'move',
    RENAME = 'rename',
    CREATE_DIRECTORY = 'create_directory',
    LIST_DIRECTORY = 'list_directory',
    CHECK_EXISTS = 'check_exists',
    GET_STATS = 'get_stats'
}

/**
 * File system operation options
 */
export interface FileSystemOptions {
    /** The type of operation to perform */
    operation: FileSystemOperationType;
    /** The path of the file or directory */
    path: string;
    /** The content to write (for write and append operations) */
    content?: string;
    /** The destination path (for copy, move, and rename operations) */
    destination?: string;
    /** The encoding to use (for read, write, and append operations) */
    encoding?: string;
    /** The intervention level for human-in-the-loop */
    interventionLevel?: InterventionLevel;
}

/**
 * File System Tool
 *
 * Provides a tool for performing file system operations.
 */
export const fileSystemTool: MCPTool = {
    id: 'file-system',
    name: 'File System',
    description: 'Performs file system operations',
    type: MCPComponentType.TOOL,
    version: '1.0.0',
    status: MCPComponentStatus.ACTIVE,
    parameters: [
        {
            name: 'operation',
            description: 'The type of operation to perform',
            type: 'string',
            required: true
        },
        {
            name: 'path',
            description: 'The path of the file or directory',
            type: 'string',
            required: true
        },
        {
            name: 'content',
            description: 'The content to write (for write and append operations)',
            type: 'string',
            required: false
        },
        {
            name: 'destination',
            description: 'The destination path (for copy, move, and rename operations)',
            type: 'string',
            required: false
        },
        {
            name: 'encoding',
            description: 'The encoding to use (for read, write, and append operations)',
            type: 'string',
            required: false
        },
        {
            name: 'interventionLevel',
            description: 'The intervention level for human-in-the-loop',
            type: 'string',
            required: false
        }
    ],

    /**
     * Execute a file system operation
     * @param options File system options
     * @returns Operation result
     */
    async execute(options: any): Promise<any> {
        try {
            // Validate options
            if (!options || !options.operation) {
                throw new Error('Operation is required');
            }

            if (!options.path) {
                throw new Error('Path is required');
            }

            const operation = options.operation;
            const filePath = resolvePath(options.path);
            const encoding = options.encoding || 'utf-8';
            const interventionLevel = options.interventionLevel || InterventionLevel.NOTIFICATION;

            // Get the human-in-the-loop agent
            const hitlAgent = HumanInTheLoopAgent.getInstance();

            // Request intervention if needed
            const intervention = await hitlAgent.requestIntervention({
                level: interventionLevel,
                title: 'File System Operation',
                message: `Do you want to perform the following operation?\n\nOperation: ${operation}\nPath: ${filePath}${options.destination ? `\nDestination: ${resolvePath(options.destination)}` : ''}`,
                actions: ['Execute', 'Cancel'],
                defaultAction: 'Execute',
                data: { operation, path: filePath, destination: options.destination }
            });

            // If the intervention was rejected, return an error
            if (intervention.rejected) {
                return {
                    operation,
                    path: filePath,
                    success: false,
                    error: 'File system operation was rejected by the user'
                };
            }

            // Perform the operation
            switch (operation) {
                case FileSystemOperationType.READ:
                    return await readFile(filePath, encoding);

                case FileSystemOperationType.WRITE:
                    if (options.content === undefined) {
                        throw new Error('Content is required for write operation');
                    }
                    return await writeFile(filePath, options.content, encoding);

                case FileSystemOperationType.APPEND:
                    if (options.content === undefined) {
                        throw new Error('Content is required for append operation');
                    }
                    return await appendFile(filePath, options.content, encoding);

                case FileSystemOperationType.DELETE:
                    return await deleteFile(filePath);

                case FileSystemOperationType.COPY:
                    if (!options.destination) {
                        throw new Error('Destination is required for copy operation');
                    }
                    return await copyFile(filePath, resolvePath(options.destination));

                case FileSystemOperationType.MOVE:
                case FileSystemOperationType.RENAME:
                    if (!options.destination) {
                        throw new Error('Destination is required for move/rename operation');
                    }
                    return await moveFile(filePath, resolvePath(options.destination));

                case FileSystemOperationType.CREATE_DIRECTORY:
                    return await createDirectory(filePath);

                case FileSystemOperationType.LIST_DIRECTORY:
                    return await listDirectory(filePath);

                case FileSystemOperationType.CHECK_EXISTS:
                    return await checkExists(filePath);

                case FileSystemOperationType.GET_STATS:
                    return await getStats(filePath);

                default:
                    throw new Error(`Unsupported operation: ${operation}`);
            }
        } catch (error) {
            log('error', 'Failed to perform file system operation', error);
            throw new Error(`Failed to perform file system operation: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
};

/**
 * Resolve a path relative to the workspace root
 * @param filePath File path
 * @returns Resolved path
 */
function resolvePath(filePath: string): string {
    if (path.isAbsolute(filePath)) {
        return filePath;
    }

    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error('No workspace folder is open');
    }

    return path.join(workspaceFolders[0].uri.fsPath, filePath);
}

/**
 * Read a file
 * @param filePath File path
 * @param encoding File encoding
 * @returns File content
 */
async function readFile(filePath: string, encoding: string): Promise<any> {
    try {
        const content = await vscode.workspace.fs.readFile(vscode.Uri.file(filePath));
        return {
            operation: FileSystemOperationType.READ,
            path: filePath,
            content: Buffer.from(content).toString(encoding as BufferEncoding),
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.READ,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Write a file
 * @param filePath File path
 * @param content File content
 * @param encoding File encoding
 * @returns Operation result
 */
async function writeFile(filePath: string, content: string, encoding: string): Promise<any> {
    try {
        await vscode.workspace.fs.writeFile(
            vscode.Uri.file(filePath),
            Buffer.from(content, encoding as BufferEncoding)
        );
        return {
            operation: FileSystemOperationType.WRITE,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.WRITE,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Append to a file
 * @param filePath File path
 * @param content Content to append
 * @param encoding File encoding
 * @returns Operation result
 */
async function appendFile(filePath: string, content: string, encoding: string): Promise<any> {
    try {
        // Read the existing content
        let existingContent = '';
        try {
            const fileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(filePath));
            existingContent = Buffer.from(fileContent).toString(encoding as BufferEncoding);
        } catch (error) {
            // File doesn't exist, start with empty content
        }

        // Append the new content
        const newContent = existingContent + content;

        // Write the combined content
        await vscode.workspace.fs.writeFile(
            vscode.Uri.file(filePath),
            Buffer.from(newContent, encoding as BufferEncoding)
        );

        return {
            operation: FileSystemOperationType.APPEND,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.APPEND,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Delete a file
 * @param filePath File path
 * @returns Operation result
 */
async function deleteFile(filePath: string): Promise<any> {
    try {
        await vscode.workspace.fs.delete(vscode.Uri.file(filePath), { recursive: true });
        return {
            operation: FileSystemOperationType.DELETE,
            path: filePath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.DELETE,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Copy a file
 * @param sourcePath Source path
 * @param destinationPath Destination path
 * @returns Operation result
 */
async function copyFile(sourcePath: string, destinationPath: string): Promise<any> {
    try {
        await vscode.workspace.fs.copy(
            vscode.Uri.file(sourcePath),
            vscode.Uri.file(destinationPath),
            { overwrite: true }
        );
        return {
            operation: FileSystemOperationType.COPY,
            path: sourcePath,
            destination: destinationPath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.COPY,
            path: sourcePath,
            destination: destinationPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Move a file
 * @param sourcePath Source path
 * @param destinationPath Destination path
 * @returns Operation result
 */
async function moveFile(sourcePath: string, destinationPath: string): Promise<any> {
    try {
        await vscode.workspace.fs.rename(
            vscode.Uri.file(sourcePath),
            vscode.Uri.file(destinationPath),
            { overwrite: true }
        );
        return {
            operation: FileSystemOperationType.MOVE,
            path: sourcePath,
            destination: destinationPath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.MOVE,
            path: sourcePath,
            destination: destinationPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Create a directory
 * @param dirPath Directory path
 * @returns Operation result
 */
async function createDirectory(dirPath: string): Promise<any> {
    try {
        await vscode.workspace.fs.createDirectory(vscode.Uri.file(dirPath));
        return {
            operation: FileSystemOperationType.CREATE_DIRECTORY,
            path: dirPath,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.CREATE_DIRECTORY,
            path: dirPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * List directory contents
 * @param dirPath Directory path
 * @returns Directory contents
 */
async function listDirectory(dirPath: string): Promise<any> {
    try {
        const entries = await vscode.workspace.fs.readDirectory(vscode.Uri.file(dirPath));

        // Convert entries to a more readable format
        const files = [];
        const directories = [];

        for (const [name, type] of entries) {
            if (type === vscode.FileType.File) {
                files.push(name);
            } else if (type === vscode.FileType.Directory) {
                directories.push(name);
            }
        }

        return {
            operation: FileSystemOperationType.LIST_DIRECTORY,
            path: dirPath,
            files,
            directories,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.LIST_DIRECTORY,
            path: dirPath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Check if a file or directory exists
 * @param filePath File path
 * @returns Existence check result
 */
async function checkExists(filePath: string): Promise<any> {
    try {
        const stat = await vscode.workspace.fs.stat(vscode.Uri.file(filePath));
        return {
            operation: FileSystemOperationType.CHECK_EXISTS,
            path: filePath,
            exists: true,
            isFile: stat.type === vscode.FileType.File,
            isDirectory: stat.type === vscode.FileType.Directory,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.CHECK_EXISTS,
            path: filePath,
            exists: false,
            success: true
        };
    }
}

/**
 * Get file or directory stats
 * @param filePath File path
 * @returns File stats
 */
async function getStats(filePath: string): Promise<any> {
    try {
        const stat = await vscode.workspace.fs.stat(vscode.Uri.file(filePath));
        return {
            operation: FileSystemOperationType.GET_STATS,
            path: filePath,
            isFile: stat.type === vscode.FileType.File,
            isDirectory: stat.type === vscode.FileType.Directory,
            isSymbolicLink: stat.type === vscode.FileType.SymbolicLink,
            size: stat.size,
            createdTime: stat.ctime,
            modifiedTime: stat.mtime,
            success: true
        };
    } catch (error) {
        return {
            operation: FileSystemOperationType.GET_STATS,
            path: filePath,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        };
    }
}

/**
 * Register the file system tool with the MCP server
 * @param server MCP server
 * @returns Tool ID
 */
export async function registerFileSystemTool(server: any): Promise<ToolId> {
    try {
        const toolId = await server.registerTool(fileSystemTool);
        log('info', `File system tool registered: ${toolId}`);
        return toolId;
    } catch (error) {
        log('error', 'Failed to register file system tool', error);
        throw error;
    }
}
