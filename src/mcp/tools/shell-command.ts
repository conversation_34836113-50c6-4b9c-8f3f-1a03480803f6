/**
 * Shell Command Tool for MCP
 *
 * This module provides a tool for executing shell commands.
 */

import * as vscode from 'vscode';
import * as cp from 'child_process';
import * as os from 'os';
import {
    MCPTool,
    MCPComponentType,
    MCPComponentStatus,
    ToolId
} from '../types.js';
import { log } from '../utils.js';
import { HumanInTheLoopAgent, InterventionLevel } from '../../human-in-the-loop/agent.js';

/**
 * Shell command execution options
 */
export interface ShellCommandOptions {
    /** The command to execute */
    command: string;
    /** The working directory for the command */
    cwd?: string;
    /** The timeout in milliseconds */
    timeout?: number;
    /** Whether to show the output in the terminal */
    showOutput?: boolean;
    /** The intervention level for human-in-the-loop */
    interventionLevel?: InterventionLevel;
}

/**
 * Shell command execution result
 */
export interface ShellCommandResult {
    /** The command that was executed */
    command: string;
    /** The exit code of the command */
    exitCode: number | null;
    /** The standard output of the command */
    stdout: string;
    /** The standard error of the command */
    stderr: string;
    /** The error message if the command failed */
    error?: string;
    /** The time it took to execute the command in milliseconds */
    executionTime: number;
}

/**
 * Shell Command Tool
 *
 * Provides a tool for executing shell commands.
 */
export const shellCommandTool: MCPTool = {
    id: 'shell-command',
    name: 'Shell Command',
    description: 'Executes shell commands',
    type: MCPComponentType.TOOL,
    version: '1.0.0',
    status: MCPComponentStatus.ACTIVE,
    parameters: [
        {
            name: 'command',
            description: 'The command to execute',
            type: 'string',
            required: true
        },
        {
            name: 'cwd',
            description: 'The working directory for the command',
            type: 'string',
            required: false
        },
        {
            name: 'timeout',
            description: 'The timeout in milliseconds',
            type: 'number',
            required: false
        },
        {
            name: 'showOutput',
            description: 'Whether to show the output in the terminal',
            type: 'boolean',
            required: false
        },
        {
            name: 'interventionLevel',
            description: 'The intervention level for human-in-the-loop',
            type: 'string',
            required: false
        }
    ],

    /**
     * Execute a shell command
     * @param options Shell command options
     * @returns Shell command result
     */
    async execute(options: any): Promise<any> {
        try {
            // Validate options
            if (!options || !options.command) {
                throw new Error('Command is required');
            }

            const command = options.command;
            const cwd = options.cwd || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || os.homedir();
            const timeout = options.timeout || 30000; // Default timeout: 30 seconds
            const showOutput = options.showOutput !== undefined ? options.showOutput : true;
            const interventionLevel = options.interventionLevel || InterventionLevel.NOTIFICATION;

            // Get the human-in-the-loop agent
            const hitlAgent = HumanInTheLoopAgent.getInstance();

            // Request intervention if needed
            const intervention = await hitlAgent.requestIntervention({
                level: interventionLevel,
                title: 'Shell Command Execution',
                message: `Do you want to execute the following command?\n\n${command}\n\nWorking directory: ${cwd}`,
                actions: ['Execute', 'Cancel'],
                defaultAction: 'Execute',
                data: { command, cwd }
            });

            // If the intervention was rejected, return an error
            if (intervention.rejected) {
                return {
                    command,
                    exitCode: null,
                    stdout: '',
                    stderr: '',
                    error: 'Command execution was rejected by the user',
                    executionTime: 0
                };
            }

            // Show the terminal if requested
            let terminal: vscode.Terminal | undefined;
            if (showOutput) {
                terminal = vscode.window.createTerminal({
                    name: 'Shell Command',
                    cwd
                });
                terminal.show();
            }

            // Execute the command
            const startTime = Date.now();

            return new Promise<ShellCommandResult>((resolve, reject) => {
                try {
                    // If showing output in terminal, use the terminal
                    if (showOutput && terminal) {
                        terminal.sendText(command);

                        // We can't get the output from the terminal, so we'll also execute it with exec
                        const childProcess = cp.exec(command, { cwd, timeout }, (error, stdout, stderr) => {
                            const endTime = Date.now();
                            const executionTime = endTime - startTime;

                            if (error) {
                                resolve({
                                    command,
                                    exitCode: error.code || 1,
                                    stdout,
                                    stderr,
                                    error: error.message,
                                    executionTime
                                });
                            } else {
                                resolve({
                                    command,
                                    exitCode: 0,
                                    stdout,
                                    stderr,
                                    executionTime
                                });
                            }
                        });
                    } else {
                        // If not showing output in terminal, just use exec
                        const childProcess = cp.exec(command, { cwd, timeout }, (error, stdout, stderr) => {
                            const endTime = Date.now();
                            const executionTime = endTime - startTime;

                            if (error) {
                                resolve({
                                    command,
                                    exitCode: error.code || 1,
                                    stdout,
                                    stderr,
                                    error: error.message,
                                    executionTime
                                });
                            } else {
                                resolve({
                                    command,
                                    exitCode: 0,
                                    stdout,
                                    stderr,
                                    executionTime
                                });
                            }
                        });
                    }
                } catch (error) {
                    const endTime = Date.now();
                    const executionTime = endTime - startTime;

                    resolve({
                        command,
                        exitCode: null,
                        stdout: '',
                        stderr: '',
                        error: error instanceof Error ? error.message : String(error),
                        executionTime
                    });
                }
            });
        } catch (error) {
            log('error', 'Failed to execute shell command', error);
            throw new Error(`Failed to execute shell command: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
};

/**
 * Register the shell command tool with the MCP server
 * @param server MCP server
 * @returns Tool ID
 */
export async function registerShellCommandTool(server: any): Promise<ToolId> {
    try {
        const toolId = await server.registerTool(shellCommandTool);
        log('info', `Shell command tool registered: ${toolId}`);
        return toolId;
    } catch (error) {
        log('error', 'Failed to register shell command tool', error);
        throw error;
    }
}
