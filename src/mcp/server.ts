/**
 * MCP Server for X10sion
 *
 * This module provides the Model Context Protocol (MCP) server implementation.
 * Based on the MCP specification from May 2025.
 */

import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import {
    MCPComponent,
    MCPResource,
    MCPTool,
    MCPPrompt,
    MCPAgent,
    MCPEventType,
    MCPEvent,
    MCPServerOptions,
    isResource,
    isTool,
    isPrompt,
    isAgent,
    ResourceId,
    ToolId,
    PromptId,
    AgentId,
    createResourceId,
    createToolId,
    createPromptId,
    createAgentId
} from './types.js';
import { MCPServer } from './interfaces.js';
import { validateComponent, log, createComponentId, createError } from './utils.js';

/**
 * X10sion MCP Server
 *
 * Implements the MCP server interface for the X10sion extension.
 */
export class X10sionMcpServer implements MCPServer {
    private options: MCPServerOptions;
    private components: Map<string, MCPComponent> = new Map();
    private eventEmitter: EventEmitter = new EventEmitter();
    private isRunning: boolean = false;
    private disposables: vscode.Disposable[] = [];
    private statusBarItem: vscode.StatusBarItem;

    /**
     * Constructor for X10sionMcpServer
     * @param options Server options
     */
    constructor(options: MCPServerOptions = {}) {
        this.options = {
            port: 0, // In-memory only
            host: 'localhost',
            cors: false,
            auth: {
                enabled: false
            },
            logging: {
                level: 'info',
                format: 'text'
            },
            ...options
        };

        // Create status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.statusBarItem.text = '$(plug) MCP: Stopped';
        this.statusBarItem.tooltip = 'X10sion MCP Server';
        this.statusBarItem.command = 'x10sion.toggleMcpServer';
        this.disposables.push(this.statusBarItem);

        // Register commands
        this.disposables.push(
            vscode.commands.registerCommand('x10sion.toggleMcpServer', () => {
                if (this.isRunning) {
                    this.stop();
                } else {
                    this.start();
                }
            })
        );

        log('info', 'MCP Server initialized');
    }

    /**
     * Start the MCP server
     */
    async start(): Promise<void> {
        if (this.isRunning) {
            return;
        }

        try {
            // In a real implementation, this would start a server
            // For now, we just set the flag and emit an event
            this.isRunning = true;
            this.statusBarItem.text = '$(plug) MCP: Running';
            this.statusBarItem.show();

            const event: MCPEvent = {
                type: MCPEventType.SERVER_STARTED,
                timestamp: Date.now(),
                data: {
                    options: this.options
                }
            };

            this.eventEmitter.emit('event', event);
            log('info', 'MCP Server started');
        } catch (error) {
            log('error', 'Failed to start MCP Server', error);
            throw createError('Failed to start MCP Server', 'SERVER_START_ERROR', error);
        }
    }

    /**
     * Stop the MCP server
     */
    async stop(): Promise<void> {
        if (!this.isRunning) {
            return;
        }

        try {
            // In a real implementation, this would stop the server
            // For now, we just set the flag and emit an event
            this.isRunning = false;
            this.statusBarItem.text = '$(plug) MCP: Stopped';

            const event: MCPEvent = {
                type: MCPEventType.SERVER_STOPPED,
                timestamp: Date.now(),
                data: {}
            };

            this.eventEmitter.emit('event', event);
            log('info', 'MCP Server stopped');
        } catch (error) {
            log('error', 'Failed to stop MCP Server', error);
            throw createError('Failed to stop MCP Server', 'SERVER_STOP_ERROR', error);
        }
    }

    /**
     * Register a resource with the server
     * @param resource Resource to register
     * @returns Resource ID
     */
    async registerResource(resource: MCPResource): Promise<ResourceId> {
        if (!this.isRunning) {
            throw createError('Server is not running', 'SERVER_NOT_RUNNING');
        }

        // Validate the resource
        const validation = validateComponent(resource);
        if (!validation.valid) {
            throw createError('Invalid resource', 'INVALID_RESOURCE', validation.errors);
        }

        // Check if the resource already exists
        if (this.components.has(resource.id)) {
            throw createError('Resource already exists', 'RESOURCE_EXISTS');
        }

        // Register the resource
        this.components.set(resource.id, resource);

        // Emit an event
        const event: MCPEvent = {
            type: MCPEventType.COMPONENT_ADDED,
            timestamp: Date.now(),
            data: {
                component: resource
            }
        };

        this.eventEmitter.emit('event', event);
        log('info', `Resource registered: ${resource.id} (${resource.name})`);

        // Use the branded type for the resource ID
        return createResourceId(resource.id);
    }

    /**
     * Register a tool with the server
     * @param tool Tool to register
     * @returns Tool ID
     */
    async registerTool(tool: MCPTool): Promise<ToolId> {
        if (!this.isRunning) {
            throw createError('Server is not running', 'SERVER_NOT_RUNNING');
        }

        // Validate the tool
        const validation = validateComponent(tool);
        if (!validation.valid) {
            throw createError('Invalid tool', 'INVALID_TOOL', validation.errors);
        }

        // Check if the tool already exists
        if (this.components.has(tool.id)) {
            throw createError('Tool already exists', 'TOOL_EXISTS');
        }

        // Register the tool
        this.components.set(tool.id, tool);

        // Emit an event
        const event: MCPEvent = {
            type: MCPEventType.COMPONENT_ADDED,
            timestamp: Date.now(),
            data: {
                component: tool
            }
        };

        this.eventEmitter.emit('event', event);
        log('info', `Tool registered: ${tool.id} (${tool.name})`);

        // Use the branded type for the tool ID
        return createToolId(tool.id);
    }

    /**
     * Register a prompt with the server
     * @param prompt Prompt to register
     * @returns Prompt ID
     */
    async registerPrompt(prompt: MCPPrompt): Promise<PromptId> {
        if (!this.isRunning) {
            throw createError('Server is not running', 'SERVER_NOT_RUNNING');
        }

        // Validate the prompt
        const validation = validateComponent(prompt);
        if (!validation.valid) {
            throw createError('Invalid prompt', 'INVALID_PROMPT', validation.errors);
        }

        // Check if the prompt already exists
        if (this.components.has(prompt.id)) {
            throw createError('Prompt already exists', 'PROMPT_EXISTS');
        }

        // Register the prompt
        this.components.set(prompt.id, prompt);

        // Emit an event
        const event: MCPEvent = {
            type: MCPEventType.COMPONENT_ADDED,
            timestamp: Date.now(),
            data: {
                component: prompt
            }
        };

        this.eventEmitter.emit('event', event);
        log('info', `Prompt registered: ${prompt.id} (${prompt.name})`);

        // Use the branded type for the prompt ID
        return createPromptId(prompt.id);
    }

    /**
     * Register an agent with the server
     * @param agent Agent to register
     * @returns Agent ID
     */
    async registerAgent(agent: MCPAgent): Promise<AgentId> {
        if (!this.isRunning) {
            throw createError('Server is not running', 'SERVER_NOT_RUNNING');
        }

        // Validate the agent
        const validation = validateComponent(agent);
        if (!validation.valid) {
            throw createError('Invalid agent', 'INVALID_AGENT', validation.errors);
        }

        // Check if the agent already exists
        if (this.components.has(agent.id)) {
            throw createError('Agent already exists', 'AGENT_EXISTS');
        }

        // Register the agent
        this.components.set(agent.id, agent);

        // Emit an event
        const event: MCPEvent = {
            type: MCPEventType.COMPONENT_ADDED,
            timestamp: Date.now(),
            data: {
                component: agent
            }
        };

        this.eventEmitter.emit('event', event);
        log('info', `Agent registered: ${agent.id} (${agent.name})`);

        // Use the branded type for the agent ID
        return createAgentId(agent.id);
    }

    /**
     * Unregister a component from the server
     * @param id Component ID
     * @param type Component type
     */
    async unregisterComponent(id: string, type: string): Promise<void> {
        if (!this.isRunning) {
            throw createError('Server is not running', 'SERVER_NOT_RUNNING');
        }

        // Check if the component exists
        if (!this.components.has(id)) {
            throw createError('Component not found', 'COMPONENT_NOT_FOUND');
        }

        // Check if the component type matches
        const component = this.components.get(id)!;
        if (component.type !== type) {
            throw createError('Component type mismatch', 'COMPONENT_TYPE_MISMATCH');
        }

        // Unregister the component
        this.components.delete(id);

        // Emit an event
        const event: MCPEvent = {
            type: MCPEventType.COMPONENT_REMOVED,
            timestamp: Date.now(),
            data: {
                component
            }
        };

        this.eventEmitter.emit('event', event);
        log('info', `Component unregistered: ${id} (${component.name})`);
    }

    /**
     * Get all registered components
     * @returns All registered components
     */
    async getComponents(): Promise<MCPComponent[]> {
        if (!this.isRunning) {
            throw createError('Server is not running', 'SERVER_NOT_RUNNING');
        }

        return Array.from(this.components.values());
    }

    /**
     * Get a component by ID
     * @param id Component ID
     * @returns Component or undefined if not found
     */
    async getComponent(id: string): Promise<MCPComponent | undefined> {
        if (!this.isRunning) {
            throw createError('Server is not running', 'SERVER_NOT_RUNNING');
        }

        return this.components.get(id);
    }

    /**
     * Subscribe to server events
     * @param callback Callback function for events
     * @returns Unsubscribe function
     */
    subscribe(callback: (event: MCPEvent) => void): () => void {
        this.eventEmitter.on('event', callback);
        return () => this.eventEmitter.off('event', callback);
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        // Stop the server if it's running
        if (this.isRunning) {
            this.stop().catch(error => {
                log('error', 'Failed to stop MCP Server during disposal', error);
            });
        }

        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Clear components
        this.components.clear();

        // Remove all event listeners
        this.eventEmitter.removeAllListeners();

        log('info', 'MCP Server disposed');
    }

    /**
     * Get the server status
     * @returns Server status
     */
    getStatus(): { running: boolean; componentCount: number } {
        return {
            running: this.isRunning,
            componentCount: this.components.size
        };
    }
}
