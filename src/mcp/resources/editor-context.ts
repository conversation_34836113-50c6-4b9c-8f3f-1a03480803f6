/**
 * Editor Context Resource for MCP
 *
 * This module provides the editor context resource for the MCP server.
 * It allows clients to access the current editor context.
 */

import * as vscode from 'vscode';
import {
    MCPResource,
    MCPComponentType,
    MCPComponentStatus,
    ResourceId
} from '../types.js';
import { getActiveEditorContext } from '../../extension.js';
import { log } from '../utils.js';

/**
 * Editor Context Resource
 *
 * Provides access to the current editor context.
 */
export const editorContextResource: MCPResource = {
    id: 'editor-context',
    name: 'Editor Context',
    description: 'Provides access to the current editor context',
    type: MCPComponentType.RESOURCE,
    version: '1.0.0',
    status: MCPComponentStatus.ACTIVE,
    contentType: 'application/json',

    /**
     * Get the current editor context
     * @returns The current editor context
     */
    async getData(): Promise<any> {
        try {
            const context = await getActiveEditorContext();
            return context;
        } catch (error) {
            log('error', 'Failed to get editor context', error);
            throw new Error('Failed to get editor context');
        }
    },

    /**
     * Update is not supported for editor context
     */
    async update(data: any): Promise<void> {
        throw new Error('Update not supported for editor context');
    }
};

/**
 * Register the editor context resource with the MCP server
 * @param server MCP server
 * @returns Resource ID
 */
export async function registerEditorContextResource(server: any): Promise<ResourceId> {
    try {
        const resourceId = await server.registerResource(editorContextResource);
        log('info', `Editor context resource registered: ${resourceId}`);
        return resourceId;
    } catch (error) {
        log('error', 'Failed to register editor context resource', error);
        throw error;
    }
}
