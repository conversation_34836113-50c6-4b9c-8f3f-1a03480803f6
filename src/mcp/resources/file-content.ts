/**
 * File Content Resource for MCP
 * 
 * This module provides the file content resource for the MCP server.
 * It allows clients to access the content of files in the workspace.
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { 
    MCPResource, 
    MCPComponentType, 
    MCPComponentStatus,
    ResourceId
} from '../types.js';
import { log } from '../utils.js';

/**
 * File Content Resource
 * 
 * Provides access to the content of files in the workspace.
 */
export const fileContentResource: MCPResource = {
    id: 'file-content',
    name: 'File Content',
    description: 'Provides access to the content of files in the workspace',
    type: MCPComponentType.RESOURCE,
    version: '1.0.0',
    status: MCPComponentStatus.ACTIVE,
    contentType: 'application/json',
    
    /**
     * Get the content of a file
     * @param params Parameters for the resource
     * @returns The file content
     */
    async getData(params?: any): Promise<any> {
        try {
            // Validate parameters
            if (!params || !params.filePath) {
                throw new Error('File path is required');
            }
            
            const filePath = params.filePath;
            
            // Resolve the file path
            let resolvedPath = filePath;
            if (!path.isAbsolute(filePath)) {
                // If the path is relative, resolve it against the workspace root
                const workspaceFolders = vscode.workspace.workspaceFolders;
                if (!workspaceFolders || workspaceFolders.length === 0) {
                    throw new Error('No workspace folder is open');
                }
                
                resolvedPath = path.join(workspaceFolders[0].uri.fsPath, filePath);
            }
            
            // Create a URI for the file
            const uri = vscode.Uri.file(resolvedPath);
            
            // Read the file content
            const content = await vscode.workspace.fs.readFile(uri);
            
            // Convert the content to a string
            const contentStr = Buffer.from(content).toString('utf-8');
            
            // Get the language ID based on the file extension
            const languageId = getLanguageIdFromPath(resolvedPath);
            
            return {
                filePath: resolvedPath,
                content: contentStr,
                languageId
            };
        } catch (error) {
            log('error', 'Failed to get file content', error);
            throw new Error(`Failed to get file content: ${error instanceof Error ? error.message : String(error)}`);
        }
    },
    
    /**
     * Update the content of a file
     * @param data The new file content
     */
    async update(data: any): Promise<void> {
        try {
            // Validate parameters
            if (!data || !data.filePath || data.content === undefined) {
                throw new Error('File path and content are required');
            }
            
            const filePath = data.filePath;
            const content = data.content;
            
            // Resolve the file path
            let resolvedPath = filePath;
            if (!path.isAbsolute(filePath)) {
                // If the path is relative, resolve it against the workspace root
                const workspaceFolders = vscode.workspace.workspaceFolders;
                if (!workspaceFolders || workspaceFolders.length === 0) {
                    throw new Error('No workspace folder is open');
                }
                
                resolvedPath = path.join(workspaceFolders[0].uri.fsPath, filePath);
            }
            
            // Create a URI for the file
            const uri = vscode.Uri.file(resolvedPath);
            
            // Convert the content to a buffer
            const contentBuffer = Buffer.from(content, 'utf-8');
            
            // Write the file content
            await vscode.workspace.fs.writeFile(uri, contentBuffer);
            
            log('info', `File content updated: ${resolvedPath}`);
        } catch (error) {
            log('error', 'Failed to update file content', error);
            throw new Error(`Failed to update file content: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
};

/**
 * Get the language ID from a file path
 * @param filePath The file path
 * @returns The language ID
 */
function getLanguageIdFromPath(filePath: string): string | null {
    const ext = path.extname(filePath).toLowerCase();
    
    // Map common extensions to language IDs
    const extensionMap: Record<string, string> = {
        '.js': 'javascript',
        '.jsx': 'javascriptreact',
        '.ts': 'typescript',
        '.tsx': 'typescriptreact',
        '.html': 'html',
        '.css': 'css',
        '.scss': 'scss',
        '.json': 'json',
        '.md': 'markdown',
        '.py': 'python',
        '.java': 'java',
        '.c': 'c',
        '.cpp': 'cpp',
        '.cs': 'csharp',
        '.go': 'go',
        '.rs': 'rust',
        '.rb': 'ruby',
        '.php': 'php',
        '.sh': 'shellscript',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.xml': 'xml',
        '.sql': 'sql',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.dart': 'dart'
    };
    
    return extensionMap[ext] || null;
}

/**
 * Register the file content resource with the MCP server
 * @param server MCP server
 * @returns Resource ID
 */
export async function registerFileContentResource(server: any): Promise<ResourceId> {
    try {
        const resourceId = await server.registerResource(fileContentResource);
        log('info', `File content resource registered: ${resourceId}`);
        return resourceId;
    } catch (error) {
        log('error', 'Failed to register file content resource', error);
        throw error;
    }
}
