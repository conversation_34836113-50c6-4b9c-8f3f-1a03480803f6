/**
 * Guidelines Resource for MCP
 * 
 * This module provides the guidelines resource for the MCP server.
 * It allows clients to access project and workspace guidelines.
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { 
    MCPResource, 
    MCPComponentType, 
    MCPComponentStatus,
    ResourceId
} from '../types.js';
import { log } from '../utils.js';

/**
 * Guidelines types
 */
export enum GuidelineType {
    PROJECT = 'project',
    WORKSPACE = 'workspace',
    CODING_STANDARDS = 'coding-standards',
    DOCUMENTATION = 'documentation',
    TESTING = 'testing',
    SECURITY = 'security',
    PERFORMANCE = 'performance',
    ACCESSIBILITY = 'accessibility',
    CUSTOM = 'custom'
}

/**
 * Guidelines resource
 * 
 * Provides access to project and workspace guidelines.
 */
export const guidelinesResource: MCPResource = {
    id: 'guidelines',
    name: 'Guidelines',
    description: 'Provides access to project and workspace guidelines',
    type: MCPComponentType.RESOURCE,
    version: '1.0.0',
    status: MCPComponentStatus.ACTIVE,
    contentType: 'application/json',
    
    /**
     * Get guidelines
     * @param params Parameters for the resource
     * @returns The guidelines
     */
    async getData(params?: any): Promise<any> {
        try {
            // Default to project guidelines if no type is specified
            const guidelineType = params?.type || GuidelineType.PROJECT;
            
            // Get the guidelines based on the type
            switch (guidelineType) {
                case GuidelineType.PROJECT:
                    return await getProjectGuidelines();
                case GuidelineType.WORKSPACE:
                    return await getWorkspaceGuidelines();
                case GuidelineType.CODING_STANDARDS:
                    return await getCodingStandardsGuidelines();
                case GuidelineType.DOCUMENTATION:
                    return await getDocumentationGuidelines();
                case GuidelineType.TESTING:
                    return await getTestingGuidelines();
                case GuidelineType.SECURITY:
                    return await getSecurityGuidelines();
                case GuidelineType.PERFORMANCE:
                    return await getPerformanceGuidelines();
                case GuidelineType.ACCESSIBILITY:
                    return await getAccessibilityGuidelines();
                case GuidelineType.CUSTOM:
                    if (!params?.path) {
                        throw new Error('Path is required for custom guidelines');
                    }
                    return await getCustomGuidelines(params.path);
                default:
                    throw new Error(`Unknown guideline type: ${guidelineType}`);
            }
        } catch (error) {
            log('error', 'Failed to get guidelines', error);
            throw new Error(`Failed to get guidelines: ${error instanceof Error ? error.message : String(error)}`);
        }
    },
    
    /**
     * Update is not supported for guidelines
     */
    async update(data: any): Promise<void> {
        throw new Error('Update not supported for guidelines');
    }
};

/**
 * Get project guidelines
 * @returns Project guidelines
 */
async function getProjectGuidelines(): Promise<any> {
    // Look for common project guideline files
    const guidelineFiles = [
        'CONTRIBUTING.md',
        'GUIDELINES.md',
        'docs/CONTRIBUTING.md',
        'docs/GUIDELINES.md',
        '.github/CONTRIBUTING.md',
        'CODE_OF_CONDUCT.md',
        '.github/CODE_OF_CONDUCT.md'
    ];
    
    return await findAndReadGuidelines(guidelineFiles, 'project');
}

/**
 * Get workspace guidelines
 * @returns Workspace guidelines
 */
async function getWorkspaceGuidelines(): Promise<any> {
    // Look for workspace-specific guideline files
    const guidelineFiles = [
        '.vscode/guidelines.md',
        '.vscode/workspace-guidelines.md',
        'workspace-guidelines.md'
    ];
    
    return await findAndReadGuidelines(guidelineFiles, 'workspace');
}

/**
 * Get coding standards guidelines
 * @returns Coding standards guidelines
 */
async function getCodingStandardsGuidelines(): Promise<any> {
    // Look for coding standards guideline files
    const guidelineFiles = [
        'docs/coding-standards.md',
        'docs/code-style.md',
        'docs/style-guide.md',
        '.github/coding-standards.md',
        'STYLE_GUIDE.md'
    ];
    
    return await findAndReadGuidelines(guidelineFiles, 'coding-standards');
}

/**
 * Get documentation guidelines
 * @returns Documentation guidelines
 */
async function getDocumentationGuidelines(): Promise<any> {
    // Look for documentation guideline files
    const guidelineFiles = [
        'docs/documentation-guidelines.md',
        'docs/doc-standards.md',
        'DOCUMENTATION.md'
    ];
    
    return await findAndReadGuidelines(guidelineFiles, 'documentation');
}

/**
 * Get testing guidelines
 * @returns Testing guidelines
 */
async function getTestingGuidelines(): Promise<any> {
    // Look for testing guideline files
    const guidelineFiles = [
        'docs/testing-guidelines.md',
        'docs/test-standards.md',
        'TESTING.md'
    ];
    
    return await findAndReadGuidelines(guidelineFiles, 'testing');
}

/**
 * Get security guidelines
 * @returns Security guidelines
 */
async function getSecurityGuidelines(): Promise<any> {
    // Look for security guideline files
    const guidelineFiles = [
        'docs/security-guidelines.md',
        'SECURITY.md',
        '.github/SECURITY.md'
    ];
    
    return await findAndReadGuidelines(guidelineFiles, 'security');
}

/**
 * Get performance guidelines
 * @returns Performance guidelines
 */
async function getPerformanceGuidelines(): Promise<any> {
    // Look for performance guideline files
    const guidelineFiles = [
        'docs/performance-guidelines.md',
        'PERFORMANCE.md'
    ];
    
    return await findAndReadGuidelines(guidelineFiles, 'performance');
}

/**
 * Get accessibility guidelines
 * @returns Accessibility guidelines
 */
async function getAccessibilityGuidelines(): Promise<any> {
    // Look for accessibility guideline files
    const guidelineFiles = [
        'docs/accessibility-guidelines.md',
        'ACCESSIBILITY.md'
    ];
    
    return await findAndReadGuidelines(guidelineFiles, 'accessibility');
}

/**
 * Get custom guidelines
 * @param customPath Path to the custom guidelines file
 * @returns Custom guidelines
 */
async function getCustomGuidelines(customPath: string): Promise<any> {
    // Resolve the file path
    let resolvedPath = customPath;
    if (!path.isAbsolute(customPath)) {
        // If the path is relative, resolve it against the workspace root
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            throw new Error('No workspace folder is open');
        }
        
        resolvedPath = path.join(workspaceFolders[0].uri.fsPath, customPath);
    }
    
    // Check if the file exists
    if (!fs.existsSync(resolvedPath)) {
        throw new Error(`Custom guidelines file not found: ${customPath}`);
    }
    
    // Read the file content
    const content = fs.readFileSync(resolvedPath, 'utf-8');
    
    return {
        type: 'custom',
        path: customPath,
        content
    };
}

/**
 * Find and read guidelines from a list of possible files
 * @param files List of possible guideline files
 * @param type Type of guidelines
 * @returns Guidelines content
 */
async function findAndReadGuidelines(files: string[], type: string): Promise<any> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error('No workspace folder is open');
    }
    
    const workspaceRoot = workspaceFolders[0].uri.fsPath;
    
    // Find the first existing file
    for (const file of files) {
        const filePath = path.join(workspaceRoot, file);
        if (fs.existsSync(filePath)) {
            // Read the file content
            const content = fs.readFileSync(filePath, 'utf-8');
            
            return {
                type,
                path: file,
                content
            };
        }
    }
    
    // No guideline file found
    return {
        type,
        path: null,
        content: `No ${type} guidelines found in the workspace.`
    };
}

/**
 * Register the guidelines resource with the MCP server
 * @param server MCP server
 * @returns Resource ID
 */
export async function registerGuidelinesResource(server: any): Promise<ResourceId> {
    try {
        const resourceId = await server.registerResource(guidelinesResource);
        log('info', `Guidelines resource registered: ${resourceId}`);
        return resourceId;
    } catch (error) {
        log('error', 'Failed to register guidelines resource', error);
        throw error;
    }
}
