/**
 * RAG Knowledge Base Resource for MCP
 * 
 * This module provides the RAG knowledge base resource for the MCP server.
 * It allows clients to access and search through a knowledge base for relevant information.
 */

import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { 
    MCPResource, 
    MCPComponentType, 
    MCPComponentStatus,
    ResourceId
} from '../types.js';
import { log } from '../utils.js';

/**
 * Knowledge base entry
 */
interface KnowledgeBaseEntry {
    id: string;
    title: string;
    content: string;
    tags: string[];
    source?: string;
    timestamp: number;
}

/**
 * Knowledge base search result
 */
interface KnowledgeBaseSearchResult {
    entries: KnowledgeBaseEntry[];
    totalResults: number;
    query: string;
}

/**
 * RAG Knowledge Base Resource
 * 
 * Provides access to a knowledge base for retrieval-augmented generation.
 */
export const ragKnowledgeBaseResource: MCPResource = {
    id: 'rag-knowledge-base',
    name: 'RAG Knowledge Base',
    description: 'Provides access to a knowledge base for retrieval-augmented generation',
    type: MCPComponentType.RESOURCE,
    version: '1.0.0',
    status: MCPComponentStatus.ACTIVE,
    contentType: 'application/json',
    
    /**
     * Get knowledge base entries
     * @param params Parameters for the resource
     * @returns Knowledge base entries or search results
     */
    async getData(params?: any): Promise<any> {
        try {
            // If no params are provided, return all entries
            if (!params) {
                return await getAllEntries();
            }
            
            // If a query is provided, search the knowledge base
            if (params.query) {
                return await searchKnowledgeBase(params.query, params.limit || 10, params.tags);
            }
            
            // If an ID is provided, get a specific entry
            if (params.id) {
                return await getEntryById(params.id);
            }
            
            // If tags are provided, filter by tags
            if (params.tags) {
                return await getEntriesByTags(params.tags, params.limit || 10);
            }
            
            // Default to returning all entries
            return await getAllEntries();
        } catch (error) {
            log('error', 'Failed to get knowledge base entries', error);
            throw new Error(`Failed to get knowledge base entries: ${error instanceof Error ? error.message : String(error)}`);
        }
    },
    
    /**
     * Update the knowledge base
     * @param data The data to update
     */
    async update(data: any): Promise<void> {
        try {
            // Validate parameters
            if (!data) {
                throw new Error('Data is required');
            }
            
            // If an entry is provided, add or update it
            if (data.entry) {
                await addOrUpdateEntry(data.entry);
                return;
            }
            
            // If an ID is provided, delete the entry
            if (data.deleteId) {
                await deleteEntry(data.deleteId);
                return;
            }
            
            throw new Error('Invalid update parameters');
        } catch (error) {
            log('error', 'Failed to update knowledge base', error);
            throw new Error(`Failed to update knowledge base: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
};

/**
 * Get all knowledge base entries
 * @returns All knowledge base entries
 */
async function getAllEntries(): Promise<KnowledgeBaseEntry[]> {
    try {
        const knowledgeBasePath = getKnowledgeBasePath();
        
        // Create the knowledge base directory if it doesn't exist
        if (!fs.existsSync(knowledgeBasePath)) {
            fs.mkdirSync(knowledgeBasePath, { recursive: true });
            return [];
        }
        
        // Read all files in the knowledge base directory
        const files = fs.readdirSync(knowledgeBasePath);
        const entries: KnowledgeBaseEntry[] = [];
        
        for (const file of files) {
            if (file.endsWith('.json')) {
                const filePath = path.join(knowledgeBasePath, file);
                const content = fs.readFileSync(filePath, 'utf-8');
                try {
                    const entry = JSON.parse(content) as KnowledgeBaseEntry;
                    entries.push(entry);
                } catch (error) {
                    log('error', `Failed to parse knowledge base entry: ${file}`, error);
                }
            }
        }
        
        // Sort entries by timestamp (newest first)
        return entries.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
        log('error', 'Failed to get all knowledge base entries', error);
        throw error;
    }
}

/**
 * Get a knowledge base entry by ID
 * @param id Entry ID
 * @returns Knowledge base entry
 */
async function getEntryById(id: string): Promise<KnowledgeBaseEntry | null> {
    try {
        const knowledgeBasePath = getKnowledgeBasePath();
        const filePath = path.join(knowledgeBasePath, `${id}.json`);
        
        if (!fs.existsSync(filePath)) {
            return null;
        }
        
        const content = fs.readFileSync(filePath, 'utf-8');
        return JSON.parse(content) as KnowledgeBaseEntry;
    } catch (error) {
        log('error', `Failed to get knowledge base entry: ${id}`, error);
        throw error;
    }
}

/**
 * Get knowledge base entries by tags
 * @param tags Tags to filter by
 * @param limit Maximum number of entries to return
 * @returns Knowledge base entries
 */
async function getEntriesByTags(tags: string[], limit: number): Promise<KnowledgeBaseEntry[]> {
    try {
        const entries = await getAllEntries();
        
        // Filter entries by tags
        const filteredEntries = entries.filter(entry => {
            return tags.some(tag => entry.tags.includes(tag));
        });
        
        // Return the limited number of entries
        return filteredEntries.slice(0, limit);
    } catch (error) {
        log('error', 'Failed to get knowledge base entries by tags', error);
        throw error;
    }
}

/**
 * Search the knowledge base
 * @param query Search query
 * @param limit Maximum number of entries to return
 * @param tags Optional tags to filter by
 * @returns Knowledge base search results
 */
async function searchKnowledgeBase(query: string, limit: number, tags?: string[]): Promise<KnowledgeBaseSearchResult> {
    try {
        const entries = await getAllEntries();
        
        // Filter entries by tags if provided
        let filteredEntries = entries;
        if (tags && tags.length > 0) {
            filteredEntries = entries.filter(entry => {
                return tags.some(tag => entry.tags.includes(tag));
            });
        }
        
        // Search entries by query
        const searchResults = filteredEntries.filter(entry => {
            const titleMatch = entry.title.toLowerCase().includes(query.toLowerCase());
            const contentMatch = entry.content.toLowerCase().includes(query.toLowerCase());
            return titleMatch || contentMatch;
        });
        
        // Return the search results
        return {
            entries: searchResults.slice(0, limit),
            totalResults: searchResults.length,
            query
        };
    } catch (error) {
        log('error', 'Failed to search knowledge base', error);
        throw error;
    }
}

/**
 * Add or update a knowledge base entry
 * @param entry Knowledge base entry
 */
async function addOrUpdateEntry(entry: KnowledgeBaseEntry): Promise<void> {
    try {
        // Validate the entry
        if (!entry.id || !entry.title || !entry.content) {
            throw new Error('Entry ID, title, and content are required');
        }
        
        // Ensure tags is an array
        if (!entry.tags || !Array.isArray(entry.tags)) {
            entry.tags = [];
        }
        
        // Set the timestamp if not provided
        if (!entry.timestamp) {
            entry.timestamp = Date.now();
        }
        
        const knowledgeBasePath = getKnowledgeBasePath();
        
        // Create the knowledge base directory if it doesn't exist
        if (!fs.existsSync(knowledgeBasePath)) {
            fs.mkdirSync(knowledgeBasePath, { recursive: true });
        }
        
        const filePath = path.join(knowledgeBasePath, `${entry.id}.json`);
        
        // Write the entry to the file
        fs.writeFileSync(filePath, JSON.stringify(entry, null, 2), 'utf-8');
    } catch (error) {
        log('error', 'Failed to add or update knowledge base entry', error);
        throw error;
    }
}

/**
 * Delete a knowledge base entry
 * @param id Entry ID
 */
async function deleteEntry(id: string): Promise<void> {
    try {
        const knowledgeBasePath = getKnowledgeBasePath();
        const filePath = path.join(knowledgeBasePath, `${id}.json`);
        
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }
    } catch (error) {
        log('error', `Failed to delete knowledge base entry: ${id}`, error);
        throw error;
    }
}

/**
 * Get the knowledge base path
 * @returns Knowledge base path
 */
function getKnowledgeBasePath(): string {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        throw new Error('No workspace folder is open');
    }
    
    const workspaceRoot = workspaceFolders[0].uri.fsPath;
    return path.join(workspaceRoot, '.x10sion', 'knowledge-base');
}

/**
 * Register the RAG knowledge base resource with the MCP server
 * @param server MCP server
 * @returns Resource ID
 */
export async function registerRagKnowledgeBaseResource(server: any): Promise<ResourceId> {
    try {
        const resourceId = await server.registerResource(ragKnowledgeBaseResource);
        log('info', `RAG knowledge base resource registered: ${resourceId}`);
        return resourceId;
    } catch (error) {
        log('error', 'Failed to register RAG knowledge base resource', error);
        throw error;
    }
}
