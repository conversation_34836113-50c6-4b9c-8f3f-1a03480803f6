import * as vscode from 'vscode';
// We'll use the global fetch API instead of node-fetch
import { EditorContext } from './types.js';

/**
 * Ollama response interface
 */
interface OllamaResponse {
    response: string;
}

/**
 * Send a query to Ollama with context
 * @param userQuery The user's query
 * @param contextInfo The editor context
 * @returns The response from Ollama
 */
export async function sendToOllama(userQuery: string, contextInfo: EditorContext): Promise<string | null> {
    try {
        // Get Ollama API URL from settings, default to localhost:11434
        const config = vscode.workspace.getConfiguration('x10sion');
        const ollamaApiUrl = config.get<string>('ollamaApiUrl', 'http://localhost:11434');
        const ollamaModel = config.get<string>('ollamaModel', 'llama3');

        // Construct the prompt with context
        let prompt = `User Query: ${userQuery}\n\n`;

        if (contextInfo.selectedText) {
            prompt += `Selected Code:\n\`\`\`${contextInfo.languageId || ''}\n${contextInfo.selectedText}\n\`\`\`\n\n`;
        } else if (contextInfo.fileContent) {
            prompt += `File Content (${contextInfo.filePath}):\n\`\`\`${contextInfo.languageId || ''}\n${contextInfo.fileContent}\n\`\`\`\n\n`;
        }

        prompt += `Please provide a helpful response to the user's query based on the provided context.`;

        // Call Ollama API
        const response = await fetch(`${ollamaApiUrl}/api/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: ollamaModel,
                prompt: prompt,
                stream: false,
            }),
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Ollama API returned ${response.status}: ${errorText}`);
        }

        const responseData = await response.json() as OllamaResponse;
        console.log('Ollama Response:', responseData);

        if (responseData && typeof responseData.response === 'string') {
            return responseData.response.trim();
        } else {
            vscode.window.showErrorMessage('Ollama response malformed or missing "response" field. Check Debug Console.');
            console.error('Ollama response missing "response" field or not a string:', responseData);
            return null;
        }
    } catch (error: any) {
        vscode.window.showErrorMessage(`Error calling Ollama: ${error.message}. Check Debug Console.`);
        console.error('Error calling Ollama:', error);
        return null;
    }
}
