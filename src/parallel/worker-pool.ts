/**
 * Worker Pool for X10sion
 *
 * This module provides a worker pool for parallel processing of tasks,
 * optimizing resource usage and performance.
 */

import * as os from 'os';
import { Worker, isMainThread } from 'worker_threads';
import * as path from 'path';

/**
 * Task priority levels
 */
export enum TaskPriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
}

/**
 * Task interface for worker pool
 */
export interface Task<T> {
    execute(): Promise<T>;
    priority?: TaskPriority;
    id?: string;
}

/**
 * Worker status
 */
export enum WorkerStatus {
    IDLE = 'idle',
    BUSY = 'busy',
    TERMINATED = 'terminated'
}

/**
 * Worker info
 */
interface WorkerInfo {
    worker: Worker;
    status: WorkerStatus;
    taskId?: string;
    startTime?: number;
}

/**
 * Worker Pool class
 *
 * Manages a pool of workers for parallel task execution.
 */
export class WorkerPool {
    private maxWorkers: number;
    private workers: Map<number, WorkerInfo> = new Map();
    private taskQueue: Array<{
        task: Task<any>;
        resolve: (value: any) => void;
        reject: (reason: any) => void;
        priority: TaskPriority;
        id: string;
    }>;
    private nextWorkerId: number = 0;
    private nextTaskId: number = 0;
    private isProcessing: boolean = false;
    private workerScript: string;

    /**
     * Constructor for WorkerPool
     * @param maxWorkers Maximum number of workers to create (defaults to CPU core count)
     * @param workerScript Path to worker script (defaults to './worker.js')
     */
    constructor(maxWorkers: number = os.cpus().length, workerScript: string = path.join(__dirname, 'worker.js')) {
        if (!isMainThread) {
            throw new Error('WorkerPool can only be created in the main thread');
        }

        this.maxWorkers = maxWorkers;
        this.taskQueue = [];
        this.workerScript = workerScript;
        console.log(`Worker Pool initialized with max ${maxWorkers} workers`);
    }

    /**
     * Execute a task in the worker pool
     * @param task Task to execute
     * @returns Promise that resolves with the task result
     */
    execute<T>(task: Task<T>): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const priority = task.priority ?? TaskPriority.NORMAL;
            const id = task.id ?? `task-${this.nextTaskId++}`;

            this.taskQueue.push({
                task,
                resolve,
                reject,
                priority,
                id
            });

            // Sort the queue by priority (higher priority first)
            this.taskQueue.sort((a, b) => b.priority - a.priority);

            // Process the queue if not already processing
            if (!this.isProcessing) {
                this.processQueue();
            }
        });
    }

    /**
     * Process the task queue
     */
    private processQueue(): void {
        this.isProcessing = true;

        // If no tasks or all workers are busy, return
        if (this.taskQueue.length === 0) {
            this.isProcessing = false;
            return;
        }

        // Find an idle worker or create a new one if below max
        const idleWorker = this.getIdleWorker();
        if (idleWorker) {
            const { task, resolve, reject, id } = this.taskQueue.shift()!;
            this.executeTaskOnWorker(idleWorker, task, resolve, reject, id);
        } else if (this.workers.size < this.maxWorkers) {
            // Create a new worker
            const workerId = this.nextWorkerId++;
            const worker = new Worker(this.workerScript);

            const workerInfo: WorkerInfo = {
                worker,
                status: WorkerStatus.IDLE
            };

            this.workers.set(workerId, workerInfo);

            // Set up error handling
            worker.on('error', (err) => {
                console.error(`Worker ${workerId} error:`, err);
                this.handleWorkerError(workerId, err);
            });

            // Process the next task with this worker
            const { task, resolve, reject, id } = this.taskQueue.shift()!;
            this.executeTaskOnWorker(workerId, task, resolve, reject, id);
        }

        // Continue processing if there are more tasks
        if (this.taskQueue.length > 0) {
            setTimeout(() => this.processQueue(), 0);
        } else {
            this.isProcessing = false;
        }
    }

    /**
     * Get an idle worker
     * @returns Worker ID of an idle worker, or undefined if none available
     */
    private getIdleWorker(): number | undefined {
        for (const [id, info] of this.workers.entries()) {
            if (info.status === WorkerStatus.IDLE) {
                return id;
            }
        }
        return undefined;
    }

    /**
     * Execute a task on a worker
     * @param workerId Worker ID
     * @param task Task to execute
     * @param resolve Function to resolve the task promise
     * @param reject Function to reject the task promise
     * @param taskId Task ID
     */
    private executeTaskOnWorker<T>(
        workerId: number,
        task: Task<T>,
        resolve: (value: T) => void,
        reject: (reason: any) => void,
        taskId: string
    ): void {
        const workerInfo = this.workers.get(workerId)!;
        workerInfo.status = WorkerStatus.BUSY;
        workerInfo.taskId = taskId;
        workerInfo.startTime = Date.now();

        // For now, execute the task in the main thread
        // In a real implementation, this would send the task to the worker
        task.execute()
            .then(resolve)
            .catch(reject)
            .finally(() => {
                // Mark worker as idle
                if (this.workers.has(workerId)) {
                    const info = this.workers.get(workerId)!;
                    info.status = WorkerStatus.IDLE;
                    info.taskId = undefined;
                    info.startTime = undefined;
                }

                // Process the next task
                this.processQueue();
            });
    }

    /**
     * Handle worker error
     * @param workerId Worker ID
     * @param error Error
     */
    private handleWorkerError(workerId: number, error: Error): void {
        const workerInfo = this.workers.get(workerId);
        if (!workerInfo) {
            return;
        }

        // Terminate the worker
        workerInfo.worker.terminate();
        workerInfo.status = WorkerStatus.TERMINATED;

        // Create a new worker to replace it
        const newWorker = new Worker(this.workerScript);
        const newWorkerInfo: WorkerInfo = {
            worker: newWorker,
            status: WorkerStatus.IDLE
        };

        // Replace the worker
        this.workers.set(workerId, newWorkerInfo);

        // Set up error handling for the new worker
        newWorker.on('error', (err) => {
            console.error(`Worker ${workerId} error:`, err);
            this.handleWorkerError(workerId, err);
        });

        // Process the queue to continue with pending tasks
        this.processQueue();
    }

    /**
     * Get the current status of the worker pool
     * @returns Status object with worker and task information
     */
    getStatus(): any {
        const workerStatuses = Array.from(this.workers.entries()).map(([id, info]) => ({
            id,
            status: info.status,
            taskId: info.taskId,
            runningTime: info.startTime ? Date.now() - info.startTime : 0
        }));

        return {
            maxWorkers: this.maxWorkers,
            activeWorkers: this.workers.size,
            idleWorkers: Array.from(this.workers.values()).filter(w => w.status === WorkerStatus.IDLE).length,
            queuedTasks: this.taskQueue.length,
            workers: workerStatuses
        };
    }

    /**
     * Dispose of the worker pool
     */
    dispose(): void {
        // Terminate all workers
        for (const [_, info] of this.workers) {
            info.worker.terminate();
        }

        this.workers.clear();
        this.taskQueue = [];
        this.isProcessing = false;
        console.log('Worker pool disposed');
    }
}
