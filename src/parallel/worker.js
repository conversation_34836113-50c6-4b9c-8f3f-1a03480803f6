/**
 * Worker script for X10sion
 * 
 * This module provides a worker script for parallel processing of tasks.
 */

const { parentPort, workerData } = require('worker_threads');

// Initialize worker
console.log('Worker initialized');

// Handle messages from the main thread
parentPort.on('message', async (message) => {
    try {
        const { type, taskId, data } = message;
        
        switch (type) {
            case 'execute':
                // Execute the task
                const result = await executeTask(data);
                
                // Send the result back to the main thread
                parentPort.postMessage({
                    type: 'result',
                    taskId,
                    result
                });
                break;
                
            case 'ping':
                // Respond to ping
                parentPort.postMessage({
                    type: 'pong',
                    taskId
                });
                break;
                
            case 'terminate':
                // Clean up and exit
                console.log('Worker terminating');
                process.exit(0);
                break;
                
            default:
                throw new Error(`Unknown message type: ${type}`);
        }
    } catch (error) {
        // Send error back to the main thread
        parentPort.postMessage({
            type: 'error',
            taskId: message.taskId,
            error: {
                message: error.message,
                stack: error.stack
            }
        });
    }
});

/**
 * Execute a task
 * @param {Object} data Task data
 * @returns {Promise<any>} Task result
 */
async function executeTask(data) {
    const { taskType, params } = data;
    
    switch (taskType) {
        case 'embedding':
            return generateEmbedding(params);
            
        case 'similarity':
            return calculateSimilarity(params);
            
        case 'tokenize':
            return tokenizeText(params);
            
        case 'chunk':
            return chunkText(params);
            
        default:
            throw new Error(`Unknown task type: ${taskType}`);
    }
}

/**
 * Generate embedding for text
 * @param {Object} params Parameters
 * @returns {Promise<number[]>} Embedding
 */
async function generateEmbedding(params) {
    const { text, model } = params;
    
    // This is a placeholder implementation
    // In a real implementation, this would use a proper embedding model
    const embedding = new Array(384).fill(0).map(() => Math.random() * 2 - 1);
    
    // Normalize the embedding
    const norm = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    const normalizedEmbedding = embedding.map(val => val / norm);
    
    return normalizedEmbedding;
}

/**
 * Calculate similarity between embeddings
 * @param {Object} params Parameters
 * @returns {Promise<number[]>} Similarities
 */
async function calculateSimilarity(params) {
    const { queryEmbedding, documentEmbeddings } = params;
    
    // Calculate cosine similarity for each document embedding
    const similarities = documentEmbeddings.map(docEmbedding => {
        // Dot product
        const dotProduct = queryEmbedding.reduce((sum, val, i) => sum + val * docEmbedding[i], 0);
        return dotProduct; // Already normalized
    });
    
    return similarities;
}

/**
 * Tokenize text
 * @param {Object} params Parameters
 * @returns {Promise<number>} Token count
 */
async function tokenizeText(params) {
    const { text } = params;
    
    // This is a very simple tokenization approach
    // In a real implementation, this would use a proper tokenizer
    const tokens = text.split(/\s+/);
    
    return tokens.length;
}

/**
 * Chunk text
 * @param {Object} params Parameters
 * @returns {Promise<string[]>} Chunks
 */
async function chunkText(params) {
    const { text, maxChunkSize, overlap } = params;
    
    // This is a simple chunking approach
    // In a real implementation, this would use a more sophisticated chunking strategy
    const words = text.split(/\s+/);
    const chunks = [];
    
    for (let i = 0; i < words.length; i += maxChunkSize - overlap) {
        const chunk = words.slice(i, i + maxChunkSize).join(' ');
        chunks.push(chunk);
    }
    
    return chunks;
}

// Notify the main thread that the worker is ready
parentPort.postMessage({
    type: 'ready'
});
