/**
 * Human-in-the-Loop View for X10sion
 *
 * This module implements a VS Code TreeView for managing human-in-the-loop
 * interactions, showing pending requests and intervention history.
 */

import * as vscode from 'vscode';
import {
    InterventionRequest,
    InterventionResponse,
    InterventionLevel,
    ActionType
} from '../agents/human-in-the-loop-agent.js';

/**
 * Tree item for intervention requests
 */
class InterventionRequestItem extends vscode.TreeItem {
    constructor(
        public readonly request: InterventionRequest,
        public readonly response?: InterventionResponse
    ) {
        super(
            getInterventionTitle(request),
            response ? vscode.TreeItemCollapsibleState.Collapsed : vscode.TreeItemCollapsibleState.None
        );

        // Set icon based on intervention level
        this.iconPath = getIconForInterventionLevel(request.level);

        // Set context value for command enablement
        this.contextValue = response ? 'completedIntervention' : 'pendingIntervention';

        // Set tooltip
        this.tooltip = getInterventionTooltip(request, response);

        // Set description (timestamp)
        this.description = new Date(request.timestamp).toLocaleTimeString();

        // Add command for pending interventions
        if (!response) {
            this.command = {
                title: 'Respond to Intervention',
                command: 'x10sion.respondToIntervention',
                arguments: [request.id]
            };
        }
    }
}

/**
 * Tree item for intervention details
 */
class InterventionDetailItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly value: string
    ) {
        super(label);
        this.description = value;
        this.contextValue = 'interventionDetail';
    }
}

/**
 * Data provider for human-in-the-loop tree view
 */
export class HumanInTheLoopDataProvider implements vscode.TreeDataProvider<vscode.TreeItem> {
    private _onDidChangeTreeData = new vscode.EventEmitter<vscode.TreeItem | undefined>();
    readonly onDidChangeTreeData = this._onDidChangeTreeData.event;

    private pendingInterventions: Map<string, InterventionRequest> = new Map();
    private completedInterventions: Map<string, { request: InterventionRequest, response: InterventionResponse }> = new Map();

    constructor() {}

    /**
     * Get tree item for a given element
     */
    getTreeItem(element: vscode.TreeItem): vscode.TreeItem {
        return element;
    }

    /**
     * Get children of a tree item
     */
    getChildren(element?: vscode.TreeItem): Thenable<vscode.TreeItem[]> {
        if (!element) {
            // Root level - show pending and completed interventions
            const pendingItems = Array.from(this.pendingInterventions.values())
                .map(request => new InterventionRequestItem(request))
                .sort((a, b) => (b.request.timestamp - a.request.timestamp));

            const completedItems = Array.from(this.completedInterventions.values())
                .map(({ request, response }) => new InterventionRequestItem(request, response))
                .sort((a, b) => (b.request.timestamp - a.request.timestamp));

            // Only show the 10 most recent completed interventions
            const recentCompletedItems = completedItems.slice(0, 10);

            if (pendingItems.length === 0 && recentCompletedItems.length === 0) {
                return Promise.resolve([
                    new vscode.TreeItem('No interventions', vscode.TreeItemCollapsibleState.None)
                ]);
            }

            return Promise.resolve([
                ...pendingItems,
                ...recentCompletedItems
            ]);
        } else if (element instanceof InterventionRequestItem && element.response) {
            // Show details for a completed intervention
            const { request, response } = element;

            const details: vscode.TreeItem[] = [
                new InterventionDetailItem('Type', getActionTypeLabel(request.actionType)),
                new InterventionDetailItem('Level', getInterventionLevelLabel(request.level)),
                new InterventionDetailItem('Status', response.approved ? 'Approved' : 'Rejected'),
                new InterventionDetailItem('Time', new Date(request.timestamp).toLocaleString())
            ];

            if (response.feedback) {
                details.push(new InterventionDetailItem('Feedback', response.feedback));
            }

            if (response.modifiedAction) {
                details.push(new InterventionDetailItem('Modified Action', response.modifiedAction));
            }

            return Promise.resolve(details);
        }

        return Promise.resolve([]);
    }

    /**
     * Add a pending intervention request
     */
    addPendingIntervention(request: InterventionRequest): void {
        this.pendingInterventions.set(request.id, request);
        this._onDidChangeTreeData.fire(undefined);
    }

    /**
     * Complete an intervention with a response
     */
    completeIntervention(requestId: string, response: InterventionResponse): void {
        const request = this.pendingInterventions.get(requestId);
        if (request) {
            this.completedInterventions.set(requestId, { request, response });
            this.pendingInterventions.delete(requestId);
            this._onDidChangeTreeData.fire(undefined);
        }
    }

    /**
     * Clear all completed interventions
     */
    clearCompletedInterventions(): void {
        this.completedInterventions.clear();
        this._onDidChangeTreeData.fire(undefined);
    }

    /**
     * Refresh the tree view
     */
    refresh(): void {
        this._onDidChangeTreeData.fire(undefined);
    }
}

/**
 * Get title for an intervention request
 */
function getInterventionTitle(request: InterventionRequest): string {
    return request.description.length > 50
        ? `${request.description.substring(0, 47)}...`
        : request.description;
}

/**
 * Get tooltip for an intervention request
 */
function getInterventionTooltip(
    request: InterventionRequest,
    response?: InterventionResponse
): string {
    let tooltip = `${request.description}\n\n`;
    tooltip += `Type: ${getActionTypeLabel(request.actionType)}\n`;
    tooltip += `Level: ${getInterventionLevelLabel(request.level)}\n`;
    tooltip += `Time: ${new Date(request.timestamp).toLocaleString()}\n`;

    if (response) {
        tooltip += `\nStatus: ${response.approved ? 'Approved' : 'Rejected'}\n`;

        if (response.feedback) {
            tooltip += `Feedback: ${response.feedback}\n`;
        }

        if (response.modifiedAction) {
            tooltip += `Modified Action: ${response.modifiedAction}\n`;
        }
    }

    return tooltip;
}

/**
 * Get icon for an intervention level
 */
function getIconForInterventionLevel(level: InterventionLevel): vscode.ThemeIcon {
    switch (level) {
        case InterventionLevel.NONE:
            return new vscode.ThemeIcon('check');
        case InterventionLevel.NOTIFICATION:
            return new vscode.ThemeIcon('info');
        case InterventionLevel.APPROVAL:
            return new vscode.ThemeIcon('question');
        case InterventionLevel.GUIDANCE:
            return new vscode.ThemeIcon('lightbulb');
        case InterventionLevel.TAKEOVER:
            return new vscode.ThemeIcon('warning');
        default:
            return new vscode.ThemeIcon('question');
    }
}

/**
 * Get human-readable label for intervention level
 */
function getInterventionLevelLabel(level: InterventionLevel): string {
    switch (level) {
        case InterventionLevel.NONE:
            return 'None';
        case InterventionLevel.NOTIFICATION:
            return 'Notification';
        case InterventionLevel.APPROVAL:
            return 'Approval';
        case InterventionLevel.GUIDANCE:
            return 'Guidance';
        case InterventionLevel.TAKEOVER:
            return 'Takeover';
        default:
            return 'Unknown';
    }
}

/**
 * Get human-readable label for action type
 */
function getActionTypeLabel(actionType: ActionType): string {
    switch (actionType) {
        case ActionType.CODE_GENERATION:
            return 'Code Generation';
        case ActionType.CODE_MODIFICATION:
            return 'Code Modification';
        case ActionType.FILE_SYSTEM_ACCESS:
            return 'File System Access';
        case ActionType.EXTERNAL_API_CALL:
            return 'External API Call';
        case ActionType.SENSITIVE_DATA_ACCESS:
            return 'Sensitive Data Access';
        case ActionType.SECURITY_CRITICAL:
            return 'Security Critical';
        case ActionType.RESOURCE_INTENSIVE:
            return 'Resource Intensive';
        case ActionType.UNCERTAIN_OUTPUT:
            return 'Uncertain Output';
        default:
            return 'Unknown';
    }
}

/**
 * Register the human-in-the-loop view
 */
export function registerHumanInTheLoopView(context: vscode.ExtensionContext): HumanInTheLoopDataProvider {
    const dataProvider = new HumanInTheLoopDataProvider();

    // Register tree view
    const treeView = vscode.window.createTreeView('x10sionHumanInTheLoopView', {
        treeDataProvider: dataProvider,
        showCollapseAll: true
    });

    // Register commands
    context.subscriptions.push(
        vscode.commands.registerCommand('x10sion.respondToIntervention', async (requestId: string) => {
            // This will be implemented in the main extension.ts file
            await vscode.commands.executeCommand('x10sion.handleInterventionRequest', requestId);
        }),

        vscode.commands.registerCommand('x10sion.clearCompletedInterventions', () => {
            dataProvider.clearCompletedInterventions();
        }),

        treeView
    );

    return dataProvider;
}
