/**
 * Base Language Model Provider for X10sion
 * 
 * This module defines the base interface for language model providers,
 * allowing for consistent interaction with different LLM backends.
 */

/**
 * Language model completion options
 */
export interface CompletionOptions {
    temperature?: number;
    maxTokens?: number;
    stopSequences?: string[];
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    [key: string]: any; // Allow for provider-specific options
}

/**
 * Language model completion result
 */
export interface CompletionResult {
    text: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    finishReason: 'stop' | 'length' | 'content_filter' | 'error';
    model: string;
    [key: string]: any; // Allow for provider-specific result properties
}

/**
 * Language model embedding options
 */
export interface EmbeddingOptions {
    model?: string;
    [key: string]: any; // Allow for provider-specific options
}

/**
 * Language model embedding result
 */
export interface EmbeddingResult {
    embedding: number[];
    usage: {
        promptTokens: number;
        totalTokens: number;
    };
    model: string;
    [key: string]: any; // Allow for provider-specific result properties
}

/**
 * Language Model Provider interface
 * 
 * Defines the interface for language model providers.
 */
export interface LanguageModelProvider {
    /**
     * Get the name of the provider
     */
    getName(): string;

    /**
     * Get available models from the provider
     */
    getAvailableModels(): Promise<string[]>;

    /**
     * Generate a completion from the language model
     */
    generateCompletion(
        prompt: string,
        options?: CompletionOptions
    ): Promise<CompletionResult>;

    /**
     * Generate an embedding from the language model
     */
    generateEmbedding(
        text: string,
        options?: EmbeddingOptions
    ): Promise<EmbeddingResult>;

    /**
     * Check if the provider is available
     */
    isAvailable(): Promise<boolean>;

    /**
     * Get the default model for the provider
     */
    getDefaultModel(): string;
}
