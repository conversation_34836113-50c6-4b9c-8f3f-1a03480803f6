/**
 * Ollama Provider
 * 
 * This module provides an implementation of the LanguageModelProvider interface
 * for the Ollama API.
 */

import * as vscode from 'vscode';
import { LanguageModelProvider, CompletionOptions, CompletionResult, EmbeddingOptions, EmbeddingResult } from './base-provider';

/**
 * Ollama API response for generate endpoint
 */
interface OllamaGenerateResponse {
    model: string;
    created_at: string;
    response: string;
    done: boolean;
    context?: number[];
    total_duration?: number;
    load_duration?: number;
    prompt_eval_count?: number;
    prompt_eval_duration?: number;
    eval_count?: number;
    eval_duration?: number;
}

/**
 * Ollama API response for embeddings endpoint
 */
interface OllamaEmbeddingResponse {
    embedding: number[];
}

/**
 * Ollama API response for models endpoint
 */
interface OllamaModelsResponse {
    models: {
        name: string;
        modified_at: string;
        size: number;
        digest: string;
        details: {
            format: string;
            family: string;
            families: string[];
            parameter_size: string;
            quantization_level: string;
        };
    }[];
}

/**
 * Ollama provider options
 */
export interface OllamaProviderOptions {
    baseUrl?: string;
    defaultModel?: string;
    timeout?: number;
}

/**
 * Ollama provider implementation
 */
export class OllamaProvider implements LanguageModelProvider {
    private baseUrl: string;
    private defaultModel: string;
    private timeout: number;
    private name: string = 'ollama';

    /**
     * Create a new Ollama provider
     */
    constructor(options: OllamaProviderOptions = {}) {
        this.baseUrl = options.baseUrl || 'http://localhost:11434';
        this.defaultModel = options.defaultModel || 'gemma3:4b-it-q4_K_M';
        this.timeout = options.timeout || 30000;
    }

    /**
     * Get the name of the provider
     */
    getName(): string {
        return this.name;
    }

    /**
     * Get the default model
     */
    getDefaultModel(): string {
        return this.defaultModel;
    }

    /**
     * Check if Ollama is available
     */
    async isAvailable(): Promise<boolean> {
        try {
            const response = await fetch(`${this.baseUrl}/api/tags`, {
                method: 'GET',
                signal: AbortSignal.timeout(5000) // Short timeout for availability check
            });
            return response.ok;
        } catch (error) {
            console.error('Error checking Ollama availability:', error);
            return false;
        }
    }

    /**
     * Get available models from Ollama
     */
    async getAvailableModels(): Promise<string[]> {
        try {
            const response = await fetch(`${this.baseUrl}/api/tags`, {
                method: 'GET',
                signal: AbortSignal.timeout(this.timeout)
            });

            if (!response.ok) {
                throw new Error(`Failed to get models: ${response.statusText}`);
            }

            const data = await response.json() as OllamaModelsResponse;
            return data.models.map(model => model.name);
        } catch (error) {
            console.error('Error getting Ollama models:', error);
            return [this.defaultModel];
        }
    }

    /**
     * Generate a completion from Ollama
     */
    async generateCompletion(prompt: string, options?: CompletionOptions): Promise<CompletionResult> {
        const startTime = Date.now();
        
        try {
            const model = options?.model || this.defaultModel;
            const temperature = options?.temperature ?? 0.7;
            const maxTokens = options?.maxTokens ?? 2000;
            
            const response = await fetch(`${this.baseUrl}/api/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: model,
                    prompt: prompt,
                    stream: false,
                    temperature: temperature,
                    num_predict: maxTokens,
                    stop: options?.stopSequences || []
                }),
                signal: AbortSignal.timeout(this.timeout)
            });

            if (!response.ok) {
                throw new Error(`Failed to generate text: ${response.statusText}`);
            }

            const data = await response.json() as OllamaGenerateResponse;
            const endTime = Date.now();
            
            // Calculate token counts (approximate since Ollama doesn't always return them)
            const promptTokens = data.prompt_eval_count || Math.ceil(prompt.length / 4);
            const completionTokens = data.eval_count || Math.ceil(data.response.length / 4);
            
            return {
                text: data.response,
                model: data.model,
                usage: {
                    promptTokens,
                    completionTokens,
                    totalTokens: promptTokens + completionTokens
                },
                finishReason: data.done ? 'stop' : 'length',
                metadata: {
                    latencyMs: endTime - startTime,
                    ...data
                }
            };
        } catch (error) {
            console.error('Error generating completion with Ollama:', error);
            throw error;
        }
    }

    /**
     * Generate an embedding from Ollama
     */
    async generateEmbedding(text: string, options?: EmbeddingOptions): Promise<EmbeddingResult> {
        try {
            const model = options?.model || this.defaultModel;
            
            const response = await fetch(`${this.baseUrl}/api/embeddings`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: model,
                    prompt: text
                }),
                signal: AbortSignal.timeout(this.timeout)
            });

            if (!response.ok) {
                throw new Error(`Failed to generate embedding: ${response.statusText}`);
            }

            const data = await response.json() as OllamaEmbeddingResponse;
            
            // Calculate token count (approximate)
            const promptTokens = Math.ceil(text.length / 4);
            
            return {
                embedding: data.embedding,
                model: model,
                usage: {
                    promptTokens,
                    totalTokens: promptTokens
                }
            };
        } catch (error) {
            console.error('Error generating embedding with Ollama:', error);
            throw error;
        }
    }
}
