import * as vscode from 'vscode';
import { MCPComponent } from './types.js';

/**
 * Tree item for MCP component categories
 */
export class MCPComponentCategoryTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly hasChildren: boolean
    ) {
        super(
            label,
            hasChildren ? vscode.TreeItemCollapsibleState.Expanded : vscode.TreeItemCollapsibleState.None
        );
        this.contextValue = 'category';

        // Set icon based on category
        switch (label.toLowerCase()) {
            case 'agents':
                this.iconPath = new vscode.ThemeIcon('hubot');
                break;
            case 'tools':
                this.iconPath = new vscode.ThemeIcon('tools');
                break;
            case 'resources':
                this.iconPath = new vscode.ThemeIcon('database');
                break;
            case 'prompts':
                this.iconPath = new vscode.ThemeIcon('comment');
                break;
        }
    }
}

/**
 * Tree item for MCP components
 */
export class MCPComponentTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly description: string,
        public readonly componentId: string,
        public readonly componentType: 'agent' | 'tool' | 'resource' | 'prompt',
        public readonly enabled: boolean,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState
    ) {
        super(label, collapsibleState);
        this.tooltip = description;
        this.description = enabled ? 'Enabled' : 'Disabled';
        this.contextValue = `component-${componentType}`;

        // Set icon based on type and enabled state
        const iconName = enabled ? 'check' : 'circle-outline';
        this.iconPath = new vscode.ThemeIcon(iconName);
    }
}

/**
 * Provider for the MCP components tree view
 */
export class MCPComponentsProvider implements vscode.TreeDataProvider<MCPComponentTreeItem | MCPComponentCategoryTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<MCPComponentTreeItem | MCPComponentCategoryTreeItem | undefined | null | void> = new vscode.EventEmitter<MCPComponentTreeItem | MCPComponentCategoryTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<MCPComponentTreeItem | MCPComponentCategoryTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private components: MCPComponent[] = [
        { id: 'code-review-agent', name: 'Code Review Agent', description: 'Reviews code for quality and issues', type: 'agent', enabled: true },
        { id: 'documentation-agent', name: 'Documentation Agent', description: 'Generates and updates documentation', type: 'agent', enabled: true },
        { id: 'code-search-tool', name: 'Code Search Tool', description: 'Searches code in the workspace', type: 'tool', enabled: true },
        { id: 'file-system-resource', name: 'File System Resource', description: 'Provides access to the file system', type: 'resource', enabled: true },
        { id: 'code-review-prompt', name: 'Code Review Prompt', description: 'Prompt template for code reviews', type: 'prompt', enabled: true },
    ];

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: MCPComponentTreeItem | MCPComponentCategoryTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: MCPComponentTreeItem | MCPComponentCategoryTreeItem): Thenable<(MCPComponentTreeItem | MCPComponentCategoryTreeItem)[]> {
        if (!element) {
            // Root level - return categories
            // Group components by type
            const agents = this.components.filter(c => c.type === 'agent');
            const tools = this.components.filter(c => c.type === 'tool');
            const resources = this.components.filter(c => c.type === 'resource');
            const prompts = this.components.filter(c => c.type === 'prompt');

            // Create category items
            const agentsItem = new MCPComponentCategoryTreeItem('Agents', agents.length > 0);
            const toolsItem = new MCPComponentCategoryTreeItem('Tools', tools.length > 0);
            const resourcesItem = new MCPComponentCategoryTreeItem('Resources', resources.length > 0);
            const promptsItem = new MCPComponentCategoryTreeItem('Prompts', prompts.length > 0);

            return Promise.resolve([agentsItem, toolsItem, resourcesItem, promptsItem]);
        } else if (element instanceof MCPComponentCategoryTreeItem) {
            // Category level - return components in this category
            return Promise.resolve(this.getComponentsByCategory(element.label));
        } else {
            // Component level - no children
            return Promise.resolve([]);
        }
    }

    getComponentsByCategory(category: string): MCPComponentTreeItem[] {
        let type: 'agent' | 'tool' | 'resource' | 'prompt';

        switch (category.toLowerCase()) {
            case 'agents':
                type = 'agent';
                break;
            case 'tools':
                type = 'tool';
                break;
            case 'resources':
                type = 'resource';
                break;
            case 'prompts':
                type = 'prompt';
                break;
            default:
                return [];
        }

        return this.components
            .filter(c => c.type === type)
            .map(c => new MCPComponentTreeItem(c.name, c.description, c.id, c.type, c.enabled, vscode.TreeItemCollapsibleState.None));
    }

    toggleComponentEnabled(id: string): void {
        const component = this.components.find(c => c.id === id);
        if (component) {
            component.enabled = !component.enabled;
            this.refresh();
        }
    }

    addComponent(component: MCPComponent): void {
        this.components.push(component);
        this.refresh();
    }

    removeComponent(id: string): void {
        const index = this.components.findIndex(c => c.id === id);
        if (index !== -1) {
            this.components.splice(index, 1);
            this.refresh();
        }
    }
}
