// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import { ChatViewProvider } from './chatView.js';
import { MCPComponentsProvider, MCPComponentTreeItem } from './mcpComponents.js';
import { EditorContext } from './types.js';
import { sendToOllama } from './ollama.js';
import { HumanInTheLoopAgent, ActionType, InterventionLevel } from './agents/human-in-the-loop-agent.js';
import { getDefaultHumanInTheLoopConfig } from './agents/human-in-the-loop-config.js';
import { registerHumanInTheLoopView, HumanInTheLoopDataProvider } from './ui/human-in-the-loop-view.js';
import { LLMMonitor } from './monitoring/llm-monitor.js';
import { WorkerPool } from './parallel/worker-pool.js';
import { LanguageModelProvider } from './llm/providers/base-provider.js';
import { X10sionMcpServer } from './mcp/server.js';
import { registerEditorContextResource } from './mcp/resources/editor-context.js';
import { registerFileContentResource } from './mcp/resources/file-content.js';
import { registerGuidelinesResource } from './mcp/resources/guidelines.js';
import { registerRagKnowledgeBaseResource } from './mcp/resources/rag-knowledge-base.js';
import { registerShellCommandTool } from './mcp/tools/shell-command.js';
import { registerFileSystemTool } from './mcp/tools/file-system.js';
import { X10sionAgentSystem } from './agents/framework/agent-system.js';
import { AgentFactory, AgentType } from './agents/framework/agent-factory.js';
import { AgentOrchestrator } from './agents/framework/agent-orchestrator.js';
import { registerOllamaMonitorTestCommand } from './test/ollama-monitor-test.js';
import { OllamaProvider } from './llm/providers/ollama-provider.js';
import { TerminalMonitor } from './monitoring/terminal-monitor.js';



export function activate(context: vscode.ExtensionContext) {
    // Use the console to output diagnostic information (console.log) and errors (console.error)
    // This line of code will only be executed once when your extension is activated
    console.log('Congratulations, your extension "x10sion" is now active!');

    // Initialize core services
    const llmMonitor = new LLMMonitor();
    const workerPool = new WorkerPool();

    // Initialize terminal monitor
    const terminalMonitor = new TerminalMonitor({
        enablePatternMatching: true,
        enableErrorDetection: true,
        enableEventEmission: true
    });

    // Initialize Ollama provider
    const ollamaProvider = new OllamaProvider({
        baseUrl: 'http://localhost:11434',
        defaultModel: 'gemma3:4b-it-q4_K_M'
    });
    const llmProvider = ollamaProvider;

    // Initialize Agent System
    const agentSystem = new X10sionAgentSystem(workerPool, llmMonitor, {
        debugMode: true
    });
    context.subscriptions.push({ dispose: () => agentSystem.dispose() });

    // Add terminal monitor to disposables
    context.subscriptions.push(terminalMonitor);

    // Initialize Agent Factory
    const agentFactory = new AgentFactory(agentSystem, workerPool, llmMonitor);

    // Initialize Agent Orchestrator
    const agentOrchestrator = new AgentOrchestrator(agentSystem, workerPool);
    context.subscriptions.push({ dispose: () => agentOrchestrator.dispose() });

    // Initialize MCP server
    const mcpServer = new X10sionMcpServer({
        logging: {
            level: 'info',
            format: 'text'
        }
    });
    context.subscriptions.push({ dispose: () => mcpServer.dispose() });

    // Start the MCP server and register resources
    mcpServer.start().then(async () => {
        try {
            // Register MCP resources
            await registerEditorContextResource(mcpServer);
            await registerFileContentResource(mcpServer);
            await registerGuidelinesResource(mcpServer);
            await registerRagKnowledgeBaseResource(mcpServer);

            // Register MCP tools
            await registerShellCommandTool(mcpServer);
            await registerFileSystemTool(mcpServer);

            console.log('MCP components registered successfully');
        } catch (error) {
            console.error('Failed to register MCP resources:', error);
            vscode.window.showErrorMessage('Failed to register MCP resources: ' + (error as Error).message);
        }
    }).catch(error => {
        console.error('Failed to start MCP server:', error);
        vscode.window.showErrorMessage('Failed to start MCP server: ' + error.message);
    });

    // Initialize and register LLM provider
    agentSystem.registerLLMProvider(llmProvider);

    // Initialize human-in-the-loop agent
    const humanInTheLoopConfig = getDefaultHumanInTheLoopConfig();
    const humanInTheLoopAgent = agentFactory.createAgent({
        name: 'Human-in-the-Loop Agent',
        description: 'Manages human intervention in AI agent operations',
        type: AgentType.HUMAN_IN_THE_LOOP,
        capabilities: humanInTheLoopConfig
    });

    // Create providers
    const mcpComponentsProvider = new MCPComponentsProvider();
    const chatViewProvider = new ChatViewProvider(context);
    const humanInTheLoopViewProvider = registerHumanInTheLoopView(context);

    // Register tree views
    const mcpComponentsTreeView = vscode.window.createTreeView('x10sionMcpComponents', {
        treeDataProvider: mcpComponentsProvider,
        showCollapseAll: true
    });
    context.subscriptions.push(mcpComponentsTreeView);

    const chatTreeView = vscode.window.createTreeView('x10sionChat', {
        treeDataProvider: chatViewProvider,
        showCollapseAll: false
    });
    context.subscriptions.push(chatTreeView);
    chatViewProvider.setTreeView(chatTreeView);

    // Register commands

    // Command to get active editor context
    context.subscriptions.push(
        vscode.commands.registerCommand('x10sion.getActiveEditorContext', getActiveEditorContext)
    );

    // Command to ask AI with context from active file
    const askAiCommand = vscode.commands.registerCommand('x10sion.askAi', async () => {
        const contextInfo = await getActiveEditorContext();
        if (!contextInfo) {
            vscode.window.showWarningMessage('X10sion: No active editor or content found. Please open a file.');
            return;
        }

        const userQuery = await vscode.window.showInputBox({
            prompt: "What would you like to ask the AI about the current file/selection?",
            placeHolder: "e.g., Explain this code, suggest improvements, write documentation...",
            ignoreFocusOut: true, // Keep input box open if focus shifts
        });

        if (userQuery === undefined || userQuery.trim() === "") {
            return;
        }

        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: "X10sion: Asking AI...",
            cancellable: false // Ollama calls are not easily cancellable with basic fetch
        }, async (progress) => {
            progress.report({ increment: 0, message: "Gathering context..." });
            progress.report({ increment: 30, message: "Sending to Ollama..." });
            const aiResponseText = await sendToOllama(userQuery, contextInfo);
            progress.report({ increment: 100 });

            if (typeof aiResponseText === 'string' && aiResponseText.length > 0) {
                vscode.window.showInformationMessage(`X10sion AI: ${aiResponseText}`);
            } else if (aiResponseText === '') {
                vscode.window.showInformationMessage('X10sion AI: Received an empty response.');
            }
        });
    });
    context.subscriptions.push(askAiCommand);

    // Command to open chat view
    const openChatCommand = vscode.commands.registerCommand('x10sion.openChat', () => {
        vscode.commands.executeCommand('x10sionChat.focus');
        vscode.commands.executeCommand('x10sion.focusChatInput');
    });
    context.subscriptions.push(openChatCommand);

    // Command to refresh MCP components
    const refreshMcpComponentsCommand = vscode.commands.registerCommand('x10sion.refreshMcpComponents', () => {
        mcpComponentsProvider.refresh();
    });
    context.subscriptions.push(refreshMcpComponentsCommand);

    // Command to toggle MCP component enabled state
    const toggleMcpComponentCommand = vscode.commands.registerCommand('x10sion.toggleMcpComponent', (item: MCPComponentTreeItem) => {
        mcpComponentsProvider.toggleComponentEnabled(item.componentId);
    });
    context.subscriptions.push(toggleMcpComponentCommand);

    // Command to add new MCP component
    const addMcpComponentCommand = vscode.commands.registerCommand('x10sion.addMcpComponent', async () => {
        const componentTypes = ['agent', 'tool', 'resource', 'prompt'];
        const componentType = await vscode.window.showQuickPick(componentTypes, {
            placeHolder: 'Select component type'
        }) as 'agent' | 'tool' | 'resource' | 'prompt' | undefined;

        if (!componentType) {
            return;
        }

        const componentName = await vscode.window.showInputBox({
            prompt: 'Enter component name',
            placeHolder: 'e.g., Code Analyzer'
        });

        if (!componentName) {
            return;
        }

        const componentDescription = await vscode.window.showInputBox({
            prompt: 'Enter component description',
            placeHolder: 'e.g., Analyzes code for quality and issues'
        });

        if (!componentDescription) {
            return;
        }

        const componentId = componentName.toLowerCase().replace(/\s+/g, '-');
        mcpComponentsProvider.addComponent({
            id: componentId,
            name: componentName,
            description: componentDescription || '',
            type: componentType,
            enabled: true
        });
    });
    context.subscriptions.push(addMcpComponentCommand);

    // Command to remove MCP component
    const removeMcpComponentCommand = vscode.commands.registerCommand('x10sion.removeMcpComponent', (item: MCPComponentTreeItem) => {
        mcpComponentsProvider.removeComponent(item.componentId);
    });
    context.subscriptions.push(removeMcpComponentCommand);

    // For backward compatibility, register the old command
    const helloWorldCommand = vscode.commands.registerCommand('x10sion.helloWorld', () => {
        vscode.commands.executeCommand('x10sion.askAi');
    });
    context.subscriptions.push(helloWorldCommand);

    // Human-in-the-loop commands

    // Command to handle intervention requests
    const handleInterventionRequestCommand = vscode.commands.registerCommand(
        'x10sion.handleInterventionRequest',
        async (requestId: string) => {
            const options = ['Approve', 'Reject', 'Provide Guidance'];
            const selection = await vscode.window.showQuickPick(options, {
                placeHolder: 'How do you want to respond to this intervention request?'
            });

            if (!selection) {
                return; // User cancelled
            }

            switch (selection) {
                case 'Approve':
                    await vscode.commands.executeCommand('x10sion.approveAction', requestId);
                    break;
                case 'Reject':
                    await vscode.commands.executeCommand('x10sion.rejectAction', requestId);
                    break;
                case 'Provide Guidance':
                    await vscode.commands.executeCommand('x10sion.provideGuidance', requestId);
                    break;
            }
        }
    );
    context.subscriptions.push(handleInterventionRequestCommand);

    // Command to approve an action
    const approveActionCommand = vscode.commands.registerCommand(
        'x10sion.approveAction',
        async (requestId: string) => {
            // In a real implementation, this would call the human-in-the-loop agent
            // For now, we'll just update the UI
            const feedback = await vscode.window.showInputBox({
                prompt: 'Optional feedback for this approval',
                placeHolder: 'Enter feedback or leave empty'
            });

            humanInTheLoopViewProvider.completeIntervention(requestId, {
                requestId,
                timestamp: Date.now(),
                approved: true,
                feedback: feedback || ''
            });

            vscode.window.showInformationMessage('Action approved');
        }
    );
    context.subscriptions.push(approveActionCommand);

    // Command to reject an action
    const rejectActionCommand = vscode.commands.registerCommand(
        'x10sion.rejectAction',
        async (requestId: string) => {
            // In a real implementation, this would call the human-in-the-loop agent
            // For now, we'll just update the UI
            const feedback = await vscode.window.showInputBox({
                prompt: 'Optional feedback for this rejection',
                placeHolder: 'Enter feedback or leave empty'
            });

            humanInTheLoopViewProvider.completeIntervention(requestId, {
                requestId,
                timestamp: Date.now(),
                approved: false,
                feedback: feedback || ''
            });

            vscode.window.showInformationMessage('Action rejected');
        }
    );
    context.subscriptions.push(rejectActionCommand);

    // Command to provide guidance
    const provideGuidanceCommand = vscode.commands.registerCommand(
        'x10sion.provideGuidance',
        async (requestId: string) => {
            // In a real implementation, this would call the human-in-the-loop agent
            // For now, we'll just update the UI
            const guidance = await vscode.window.showInputBox({
                prompt: 'Provide guidance for the AI',
                placeHolder: 'Enter your guidance here'
            });

            if (!guidance) {
                return; // User cancelled
            }

            humanInTheLoopViewProvider.completeIntervention(requestId, {
                requestId,
                timestamp: Date.now(),
                approved: true,
                modifiedAction: guidance
            });

            vscode.window.showInformationMessage('Guidance provided');
        }
    );
    context.subscriptions.push(provideGuidanceCommand);

    // Command to test human-in-the-loop functionality
    const testHumanInTheLoopCommand = vscode.commands.registerCommand(
        'x10sion.testHumanInTheLoop',
        async () => {
            // Create a test intervention request
            const actionTypes = Object.values(ActionType);
            const actionType = actionTypes[Math.floor(Math.random() * actionTypes.length)] as ActionType;

            const interventionLevels = Object.values(InterventionLevel);
            const level = interventionLevels[Math.floor(Math.random() * interventionLevels.length)] as InterventionLevel;

            const requestId = `test-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

            // Add to the view
            humanInTheLoopViewProvider.addPendingIntervention({
                id: requestId,
                timestamp: Date.now(),
                actionType,
                description: `Test intervention request for ${actionType}`,
                suggestedAction: 'Suggested action for testing',
                alternatives: ['Alternative 1', 'Alternative 2'],
                level,
                timeout: 60000
            });

            vscode.window.showInformationMessage(
                `Test intervention request created with level: ${level}`,
                'View'
            ).then(selection => {
                if (selection === 'View') {
                    vscode.commands.executeCommand('x10sionHumanInTheLoopView.focus');
                }
            });
        }
    );
    context.subscriptions.push(testHumanInTheLoopCommand);

    // Add the test command to the package.json commands array
    // This is just for development purposes
    context.subscriptions.push(
        vscode.commands.registerCommand('x10sion.clearCompletedInterventions', () => {
            humanInTheLoopViewProvider.clearCompletedInterventions();
        })
    );

    // Command to test agent framework
    const testAgentFrameworkCommand = vscode.commands.registerCommand('x10sion.testAgentFramework', async () => {
        vscode.window.showInformationMessage('Testing Agent Framework...');

        try {
            // Initialize agent system if not already initialized
            await agentSystem.initialize();

            // Create a simple workflow
            const workflowId = agentOrchestrator.registerWorkflow({
                name: 'Test Workflow',
                description: 'A simple test workflow',
                steps: [
                    {
                        id: 'step1',
                        agentId: humanInTheLoopAgent.getId(),
                        task: 'test_task',
                        context: { message: 'This is a test task' }
                    }
                ]
            });

            // Execute the workflow
            vscode.window.showInformationMessage('Executing workflow...');
            const result = await agentOrchestrator.executeWorkflow(workflowId);

            // Show result
            vscode.window.showInformationMessage(`Workflow completed with status: ${result.status}`);
        } catch (error) {
            vscode.window.showErrorMessage(`Error testing agent framework: ${(error as Error).message}`);
            console.error('Error testing agent framework:', error);
        }
    });
    context.subscriptions.push(testAgentFrameworkCommand);

    // Register Ollama monitor test command
    registerOllamaMonitorTestCommand(context);

    // Command to show terminal monitor statistics
    const showTerminalMonitorStatsCommand = vscode.commands.registerCommand('x10sion.showTerminalMonitorStats', () => {
        const patterns = terminalMonitor.getAllPatterns();
        const matches = terminalMonitor.getAllMatches();

        const errorMatches = matches.filter(match => match.severity === 'error');
        const warningMatches = matches.filter(match => match.severity === 'warning');
        const infoMatches = matches.filter(match => match.severity === 'info');

        const message = [
            `Terminal Monitor Statistics:`,
            `- Registered patterns: ${patterns.size}`,
            `- Total matches: ${matches.length}`,
            `- Error matches: ${errorMatches.length}`,
            `- Warning matches: ${warningMatches.length}`,
            `- Info matches: ${infoMatches.length}`
        ].join('\n');

        vscode.window.showInformationMessage(message);
    });
    context.subscriptions.push(showTerminalMonitorStatsCommand);
}

export async function getActiveEditorContext(): Promise<EditorContext | null> {
	const editor = vscode.window.activeTextEditor;
	if (!editor) {
		return null;
	}
	const document = editor.document;
	const selection = editor.selection;
	const selectedText = selection.isEmpty ? null : document.getText(selection);
	const fileContent = document.getText();
	const filePath = document.uri.fsPath;
	const languageId = document.languageId;
	return {
		filePath,
		languageId,
		selectedText,
		fileContent
	};
}

// This method is called when your extension is deactivated
export function deactivate() {
    // The MCP server will be disposed automatically through the context.subscriptions
    console.log('X10sion extension is now deactivated!');
}
