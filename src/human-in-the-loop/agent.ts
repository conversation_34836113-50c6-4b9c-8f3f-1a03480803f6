/**
 * Human-in-the-Loop Agent
 * 
 * This module provides a human-in-the-loop agent for X10sion.
 */

import * as vscode from 'vscode';
import { EventEmitter } from 'events';

/**
 * Intervention levels
 */
export enum InterventionLevel {
    /** No intervention required */
    NONE = 'none',
    /** Notify the user but proceed automatically */
    NOTIFICATION = 'notification',
    /** Request approval from the user */
    APPROVAL = 'approval',
    /** Request guidance from the user */
    GUIDANCE = 'guidance',
    /** Request the user to take over */
    TAKEOVER = 'takeover'
}

/**
 * Intervention request
 */
export interface InterventionRequest {
    /** The intervention level */
    level: InterventionLevel;
    /** The title of the intervention */
    title: string;
    /** The message to display to the user */
    message: string;
    /** The actions available to the user */
    actions?: string[];
    /** The default action */
    defaultAction?: string;
    /** Additional data for the intervention */
    data?: any;
    /** The alternatives to present to the user (for guidance level) */
    alternatives?: string[];
}

/**
 * Intervention response
 */
export interface InterventionResponse {
    /** Whether the intervention was rejected */
    rejected: boolean;
    /** The result of the intervention */
    result?: string;
    /** The guidance provided by the user (for guidance level) */
    guidance?: string;
    /** The takeover result (for takeover level) */
    takeover?: any;
}

/**
 * Pending intervention
 */
interface PendingIntervention {
    /** The intervention request */
    request: InterventionRequest;
    /** The resolve function for the intervention promise */
    resolve: (response: InterventionResponse) => void;
    /** The reject function for the intervention promise */
    reject: (reason: any) => void;
    /** The timestamp when the intervention was requested */
    timestamp: number;
}

/**
 * Completed intervention
 */
export interface CompletedIntervention {
    /** The intervention request */
    request: InterventionRequest;
    /** The intervention response */
    response: InterventionResponse;
    /** The timestamp when the intervention was requested */
    requestTimestamp: number;
    /** The timestamp when the intervention was completed */
    completionTimestamp: number;
}

/**
 * Human-in-the-Loop Agent
 * 
 * Provides a human-in-the-loop agent for X10sion.
 */
export class HumanInTheLoopAgent extends EventEmitter {
    private static instance: HumanInTheLoopAgent;
    private pendingInterventions: Map<string, PendingIntervention>;
    private completedInterventions: CompletedIntervention[];
    private interventionCounter: number;
    
    /**
     * Get the singleton instance of the agent
     * @returns The agent instance
     */
    public static getInstance(): HumanInTheLoopAgent {
        if (!HumanInTheLoopAgent.instance) {
            HumanInTheLoopAgent.instance = new HumanInTheLoopAgent();
        }
        return HumanInTheLoopAgent.instance;
    }
    
    /**
     * Constructor
     */
    private constructor() {
        super();
        this.pendingInterventions = new Map();
        this.completedInterventions = [];
        this.interventionCounter = 0;
    }
    
    /**
     * Request an intervention from the user
     * @param request The intervention request
     * @returns The intervention response
     */
    public async requestIntervention(request: InterventionRequest): Promise<InterventionResponse> {
        // Generate a unique ID for the intervention
        const id = `intervention-${++this.interventionCounter}`;
        
        // If the intervention level is NONE, auto-approve
        if (request.level === InterventionLevel.NONE) {
            const response: InterventionResponse = {
                rejected: false,
                result: request.defaultAction || 'Approved'
            };
            
            // Add to completed interventions
            this.completedInterventions.push({
                request,
                response,
                requestTimestamp: Date.now(),
                completionTimestamp: Date.now()
            });
            
            // Emit the intervention completed event
            this.emit('interventionCompleted', id, request, response);
            
            return response;
        }
        
        // Create a promise for the intervention
        return new Promise<InterventionResponse>((resolve, reject) => {
            // Add the intervention to the pending interventions
            this.pendingInterventions.set(id, {
                request,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            // Emit the intervention requested event
            this.emit('interventionRequested', id, request);
            
            // Handle the intervention based on the level
            switch (request.level) {
                case InterventionLevel.NOTIFICATION:
                    this.handleNotification(id, request);
                    break;
                
                case InterventionLevel.APPROVAL:
                    this.handleApproval(id, request);
                    break;
                
                case InterventionLevel.GUIDANCE:
                    this.handleGuidance(id, request);
                    break;
                
                case InterventionLevel.TAKEOVER:
                    this.handleTakeover(id, request);
                    break;
            }
        });
    }
    
    /**
     * Complete an intervention
     * @param id The intervention ID
     * @param response The intervention response
     */
    public completeIntervention(id: string, response: InterventionResponse): void {
        // Get the pending intervention
        const intervention = this.pendingInterventions.get(id);
        if (!intervention) {
            return;
        }
        
        // Remove the intervention from the pending interventions
        this.pendingInterventions.delete(id);
        
        // Add to completed interventions
        this.completedInterventions.push({
            request: intervention.request,
            response,
            requestTimestamp: intervention.timestamp,
            completionTimestamp: Date.now()
        });
        
        // Resolve the intervention promise
        intervention.resolve(response);
        
        // Emit the intervention completed event
        this.emit('interventionCompleted', id, intervention.request, response);
    }
    
    /**
     * Get all pending interventions
     * @returns The pending interventions
     */
    public getPendingInterventions(): Map<string, PendingIntervention> {
        return this.pendingInterventions;
    }
    
    /**
     * Get all completed interventions
     * @returns The completed interventions
     */
    public getCompletedInterventions(): CompletedIntervention[] {
        return this.completedInterventions;
    }
    
    /**
     * Clear completed interventions
     */
    public clearCompletedInterventions(): void {
        this.completedInterventions = [];
    }
    
    /**
     * Handle a notification intervention
     * @param id The intervention ID
     * @param request The intervention request
     */
    private handleNotification(id: string, request: InterventionRequest): void {
        // Show a notification
        vscode.window.showInformationMessage(request.message).then(() => {
            // Complete the intervention
            this.completeIntervention(id, {
                rejected: false,
                result: request.defaultAction || 'Acknowledged'
            });
        });
    }
    
    /**
     * Handle an approval intervention
     * @param id The intervention ID
     * @param request The intervention request
     */
    private handleApproval(id: string, request: InterventionRequest): void {
        // Show a confirmation dialog
        const actions = request.actions || ['Approve', 'Reject'];
        vscode.window.showInformationMessage(
            request.message,
            { modal: true, detail: request.title },
            ...actions
        ).then(result => {
            // Complete the intervention
            this.completeIntervention(id, {
                rejected: !result || result === 'Reject',
                result
            });
        });
    }
    
    /**
     * Handle a guidance intervention
     * @param id The intervention ID
     * @param request The intervention request
     */
    private handleGuidance(id: string, request: InterventionRequest): void {
        // Show a quick pick
        const alternatives = request.alternatives || ['Option 1', 'Option 2', 'Option 3'];
        vscode.window.showQuickPick(
            alternatives,
            {
                placeHolder: request.message,
                title: request.title,
                canPickMany: false
            }
        ).then(result => {
            // Complete the intervention
            this.completeIntervention(id, {
                rejected: !result,
                result,
                guidance: result
            });
        });
    }
    
    /**
     * Handle a takeover intervention
     * @param id The intervention ID
     * @param request The intervention request
     */
    private handleTakeover(id: string, request: InterventionRequest): void {
        // Show an input box
        vscode.window.showInputBox({
            prompt: request.message,
            title: request.title,
            value: request.defaultAction || ''
        }).then(result => {
            // Complete the intervention
            this.completeIntervention(id, {
                rejected: !result,
                result,
                takeover: result
            });
        });
    }
}
