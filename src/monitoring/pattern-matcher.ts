/**
 * Pattern Matcher for Terminal Monitor
 * 
 * Handles pattern matching for detecting issues in terminal output.
 */

export interface PatternMatch {
    pattern: string;
    type: string;
    severity: 'low' | 'medium' | 'high';
    confidence: number;
    match: RegExpMatchArray;
}

/**
 * Pattern matcher service for terminal monitoring
 */
export class PatternMatcherService {
    private patterns: Map<string, { regex: RegExp; type: string; severity: 'low' | 'medium' | 'high' }> = new Map();

    constructor() {
        this.initializePatterns();
    }

    /**
     * Initialize default patterns for issue detection
     */
    private initializePatterns(): void {
        // Error patterns
        this.patterns.set('error', {
            regex: /\b(error|ERROR|Error)\b/gi,
            type: 'error',
            severity: 'high'
        });

        this.patterns.set('exception', {
            regex: /\b(exception|Exception|EXCEPTION)\b/gi,
            type: 'exception',
            severity: 'high'
        });

        this.patterns.set('failed', {
            regex: /\b(failed|Failed|FAILED)\b/gi,
            type: 'failure',
            severity: 'medium'
        });

        // Warning patterns
        this.patterns.set('warning', {
            regex: /\b(warning|Warning|WARNING)\b/gi,
            type: 'warning',
            severity: 'medium'
        });

        this.patterns.set('deprecated', {
            regex: /\b(deprecated|Deprecated|DEPRECATED)\b/gi,
            type: 'deprecation',
            severity: 'low'
        });

        // Loop detection patterns
        this.patterns.set('infinite_loop', {
            regex: /\b(infinite\s+loop|endless\s+loop|stuck\s+in\s+loop)\b/gi,
            type: 'loop',
            severity: 'high'
        });

        this.patterns.set('timeout', {
            regex: /\b(timeout|Timeout|TIMEOUT|timed\s+out)\b/gi,
            type: 'timeout',
            severity: 'medium'
        });

        // Memory issues
        this.patterns.set('memory_error', {
            regex: /\b(out\s+of\s+memory|memory\s+error|OutOfMemoryError)\b/gi,
            type: 'memory',
            severity: 'high'
        });

        this.patterns.set('memory_leak', {
            regex: /\b(memory\s+leak|memory\s+leakage)\b/gi,
            type: 'memory_leak',
            severity: 'high'
        });

        // Performance issues
        this.patterns.set('slow_response', {
            regex: /\b(slow\s+response|performance\s+issue|taking\s+too\s+long)\b/gi,
            type: 'performance',
            severity: 'medium'
        });

        // Network issues
        this.patterns.set('connection_error', {
            regex: /\b(connection\s+error|network\s+error|connection\s+refused)\b/gi,
            type: 'network',
            severity: 'medium'
        });

        // File system issues
        this.patterns.set('file_not_found', {
            regex: /\b(file\s+not\s+found|no\s+such\s+file|FileNotFoundError)\b/gi,
            type: 'file_system',
            severity: 'medium'
        });

        this.patterns.set('permission_denied', {
            regex: /\b(permission\s+denied|access\s+denied|PermissionError)\b/gi,
            type: 'permission',
            severity: 'medium'
        });

        // Compilation issues
        this.patterns.set('compilation_error', {
            regex: /\b(compilation\s+error|compile\s+error|syntax\s+error)\b/gi,
            type: 'compilation',
            severity: 'high'
        });

        // Package management issues
        this.patterns.set('dependency_error', {
            regex: /\b(dependency\s+error|missing\s+dependency|unresolved\s+dependency)\b/gi,
            type: 'dependency',
            severity: 'medium'
        });

        // Security issues
        this.patterns.set('security_warning', {
            regex: /\b(security\s+warning|vulnerability|insecure)\b/gi,
            type: 'security',
            severity: 'high'
        });
    }

    /**
     * Add a custom pattern
     * @param name Pattern name
     * @param regex Regular expression
     * @param type Issue type
     * @param severity Issue severity
     */
    addPattern(name: string, regex: RegExp, type: string, severity: 'low' | 'medium' | 'high'): void {
        this.patterns.set(name, { regex, type, severity });
    }

    /**
     * Remove a pattern
     * @param name Pattern name
     */
    removePattern(name: string): void {
        this.patterns.delete(name);
    }

    /**
     * Match patterns against text
     * @param text Text to analyze
     * @returns Array of pattern matches
     */
    matchPatterns(text: string): PatternMatch[] {
        const matches: PatternMatch[] = [];

        for (const [name, pattern] of this.patterns) {
            const match = text.match(pattern.regex);
            if (match) {
                matches.push({
                    pattern: name,
                    type: pattern.type,
                    severity: pattern.severity,
                    confidence: this.calculateConfidence(match, pattern.type),
                    match
                });
            }
        }

        return matches;
    }

    /**
     * Calculate confidence score for a match
     * @param match Regular expression match
     * @param type Issue type
     * @returns Confidence score (0-1)
     */
    private calculateConfidence(match: RegExpMatchArray, type: string): number {
        let confidence = 0.5; // Base confidence

        // Increase confidence based on match characteristics
        if (match.length > 1) {
            confidence += 0.1; // Multiple matches
        }

        if (match[0].length > 10) {
            confidence += 0.1; // Longer matches are more specific
        }

        // Adjust confidence based on issue type
        switch (type) {
            case 'error':
            case 'exception':
            case 'compilation':
                confidence += 0.3; // High confidence for clear error indicators
                break;
            case 'warning':
            case 'deprecation':
                confidence += 0.2; // Medium confidence for warnings
                break;
            case 'performance':
            case 'network':
                confidence += 0.1; // Lower confidence for performance issues
                break;
        }

        return Math.min(confidence, 1.0); // Cap at 1.0
    }

    /**
     * Get all pattern names
     * @returns Array of pattern names
     */
    getPatternNames(): string[] {
        return Array.from(this.patterns.keys());
    }

    /**
     * Get pattern details
     * @param name Pattern name
     * @returns Pattern details or undefined
     */
    getPattern(name: string): { regex: RegExp; type: string; severity: 'low' | 'medium' | 'high' } | undefined {
        return this.patterns.get(name);
    }

    /**
     * Clear all patterns
     */
    clearPatterns(): void {
        this.patterns.clear();
    }

    /**
     * Reset to default patterns
     */
    resetToDefaults(): void {
        this.clearPatterns();
        this.initializePatterns();
    }
}
