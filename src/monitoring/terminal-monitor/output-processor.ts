/**
 * Output Processor for Terminal Monitor
 * 
 * Handles processing of terminal output, pattern matching, and event emission.
 */

import * as vscode from 'vscode';
import { PatternMatcherService, PatternMatch } from '../pattern-matcher.js';
import { TerminalPattern } from './pattern-registry.js';

/**
 * Terminal Output Match
 */
export interface TerminalMatch {
    id: string;
    patternId: string;
    timestamp: number;
    terminalId: number;
    text: string;
    match: RegExpMatchArray;
    severity: 'info' | 'warning' | 'error';
    tags?: string[];
}

/**
 * Output Processing Options
 */
export interface OutputProcessingOptions {
    enablePatternMatching: boolean;
    enableEventEmission: boolean;
    maxHistoryItems: number;
    debugMode: boolean;
}

/**
 * Output Processor class
 */
export class OutputProcessor {
    private matches: TerminalMatch[] = [];
    private options: OutputProcessingOptions;
    private eventEmitter: vscode.EventEmitter<any>;
    private patternMatcherService: PatternMatcherService;
    private nextId: number = 0;

    constructor(
        eventEmitter: vscode.EventEmitter<any>,
        options: OutputProcessingOptions
    ) {
        this.eventEmitter = eventEmitter;
        this.options = options;
        this.patternMatcherService = new PatternMatcherService();
    }

    /**
     * Process terminal output
     */
    processOutput(terminalId: number, data: string, patterns: Map<string, TerminalPattern>): void {
        // Match patterns
        if (this.options.enablePatternMatching) {
            this.matchPatterns(terminalId, data, patterns);
        }

        // Emit event
        if (this.options.enableEventEmission) {
            this.eventEmitter.fire({
                type: 'terminal_output',
                terminalId,
                data,
                timestamp: Date.now()
            });
        }
    }

    /**
     * Match patterns in terminal output
     */
    private matchPatterns(
        terminalId: number, 
        data: string, 
        patterns: Map<string, TerminalPattern>
    ): void {
        // Use pattern matcher service
        const patternMatches = this.patternMatcherService.matchPatterns(data);

        for (const patternMatch of patternMatches) {
            // Find the corresponding terminal pattern
            const terminalPattern = Array.from(patterns.values()).find(
                p => p.name.toLowerCase().includes(patternMatch.type) ||
                     (p.tags && p.tags.some(tag => tag.includes(patternMatch.type)))
            );

            if (terminalPattern) {
                const match: TerminalMatch = {
                    id: `match-${Date.now()}-${this.nextId++}`,
                    patternId: terminalPattern.id,
                    timestamp: Date.now(),
                    terminalId,
                    text: data,
                    match: patternMatch.match,
                    severity: this.mapSeverity(patternMatch.severity),
                    tags: terminalPattern.tags
                };

                // Add to history
                this.addMatch(match);

                // Emit event
                if (this.options.enableEventEmission) {
                    this.eventEmitter.fire({
                        type: 'pattern_matched',
                        match,
                        timestamp: Date.now()
                    });
                }

                if (this.options.debugMode) {
                    console.log(`Pattern matched: ${terminalPattern.name}`, {
                        terminalId,
                        severity: match.severity,
                        match: patternMatch.match[0]
                    });
                }
            }
        }

        // Also check direct pattern matches
        this.matchDirectPatterns(terminalId, data, patterns);
    }

    /**
     * Match patterns directly against terminal patterns
     */
    private matchDirectPatterns(
        terminalId: number,
        data: string,
        patterns: Map<string, TerminalPattern>
    ): void {
        for (const pattern of patterns.values()) {
            const matches = data.match(pattern.regex);
            if (matches) {
                const match: TerminalMatch = {
                    id: `direct-match-${Date.now()}-${this.nextId++}`,
                    patternId: pattern.id,
                    timestamp: Date.now(),
                    terminalId,
                    text: data,
                    match: matches,
                    severity: pattern.severity,
                    tags: pattern.tags
                };

                // Add to history
                this.addMatch(match);

                // Emit event
                if (this.options.enableEventEmission) {
                    this.eventEmitter.fire({
                        type: 'pattern_matched',
                        match,
                        timestamp: Date.now()
                    });
                }

                if (this.options.debugMode) {
                    console.log(`Direct pattern matched: ${pattern.name}`, {
                        terminalId,
                        severity: match.severity,
                        match: matches[0]
                    });
                }
            }
        }
    }

    /**
     * Add a match to history
     */
    private addMatch(match: TerminalMatch): void {
        this.matches.push(match);

        // Trim history if it exceeds the maximum size
        if (this.matches.length > this.options.maxHistoryItems) {
            this.matches.shift();
        }
    }

    /**
     * Map pattern matcher severity to terminal monitor severity
     */
    private mapSeverity(severity: 'low' | 'medium' | 'high'): 'info' | 'warning' | 'error' {
        switch (severity) {
            case 'low':
                return 'info';
            case 'medium':
                return 'warning';
            case 'high':
                return 'error';
            default:
                return 'info';
        }
    }

    /**
     * Get matches for a terminal
     */
    getMatchesForTerminal(terminalId: number): TerminalMatch[] {
        return this.matches.filter(match => match.terminalId === terminalId);
    }

    /**
     * Get matches for a pattern
     */
    getMatchesForPattern(patternId: string): TerminalMatch[] {
        return this.matches.filter(match => match.patternId === patternId);
    }

    /**
     * Get matches by severity
     */
    getMatchesBySeverity(severity: 'info' | 'warning' | 'error'): TerminalMatch[] {
        return this.matches.filter(match => match.severity === severity);
    }

    /**
     * Get matches by time range
     */
    getMatchesInTimeRange(startTime: number, endTime: number): TerminalMatch[] {
        return this.matches.filter(match => 
            match.timestamp >= startTime && match.timestamp <= endTime
        );
    }

    /**
     * Get recent matches
     */
    getRecentMatches(count: number = 10): TerminalMatch[] {
        return this.matches.slice(-count);
    }

    /**
     * Get all matches
     */
    getAllMatches(): TerminalMatch[] {
        return [...this.matches];
    }

    /**
     * Clear matches
     */
    clearMatches(): void {
        this.matches = [];
        if (this.options.debugMode) {
            console.log('All matches cleared');
        }
    }

    /**
     * Clear matches for a terminal
     */
    clearMatchesForTerminal(terminalId: number): void {
        this.matches = this.matches.filter(match => match.terminalId !== terminalId);
        if (this.options.debugMode) {
            console.log(`Matches cleared for terminal ${terminalId}`);
        }
    }

    /**
     * Get match statistics
     */
    getMatchStatistics(): {
        total: number;
        bySeverity: Record<string, number>;
        byTerminal: Record<number, number>;
        byPattern: Record<string, number>;
        recentActivity: { timestamp: number; count: number }[];
    } {
        const bySeverity: Record<string, number> = {};
        const byTerminal: Record<number, number> = {};
        const byPattern: Record<string, number> = {};

        // Count by severity, terminal, and pattern
        for (const match of this.matches) {
            bySeverity[match.severity] = (bySeverity[match.severity] || 0) + 1;
            byTerminal[match.terminalId] = (byTerminal[match.terminalId] || 0) + 1;
            byPattern[match.patternId] = (byPattern[match.patternId] || 0) + 1;
        }

        // Calculate recent activity (last 24 hours in hourly buckets)
        const now = Date.now();
        const hourMs = 60 * 60 * 1000;
        const recentActivity: { timestamp: number; count: number }[] = [];

        for (let i = 23; i >= 0; i--) {
            const bucketStart = now - (i + 1) * hourMs;
            const bucketEnd = now - i * hourMs;
            const count = this.matches.filter(match => 
                match.timestamp >= bucketStart && match.timestamp < bucketEnd
            ).length;

            recentActivity.push({
                timestamp: bucketStart,
                count
            });
        }

        return {
            total: this.matches.length,
            bySeverity,
            byTerminal,
            byPattern,
            recentActivity
        };
    }

    /**
     * Export matches to JSON
     */
    exportMatches(): string {
        return JSON.stringify(this.matches, null, 2);
    }

    /**
     * Search matches by text content
     */
    searchMatches(query: string): TerminalMatch[] {
        const queryLower = query.toLowerCase();
        return this.matches.filter(match =>
            match.text.toLowerCase().includes(queryLower) ||
            match.match[0].toLowerCase().includes(queryLower)
        );
    }

    /**
     * Get top patterns by match count
     */
    getTopPatterns(limit: number = 10): { patternId: string; count: number }[] {
        const patternCounts: Record<string, number> = {};

        for (const match of this.matches) {
            patternCounts[match.patternId] = (patternCounts[match.patternId] || 0) + 1;
        }

        return Object.entries(patternCounts)
            .map(([patternId, count]) => ({ patternId, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, limit);
    }

    /**
     * Update processing options
     */
    updateOptions(options: Partial<OutputProcessingOptions>): void {
        this.options = { ...this.options, ...options };
    }
}
