/**
 * Terminal Tracker for Terminal Monitor
 * 
 * Handles tracking and management of VS Code terminals.
 */

import * as vscode from 'vscode';

/**
 * Terminal Info
 */
export interface TerminalInfo {
    id: number;
    terminal: vscode.Terminal;
    name: string;
    createdAt: number;
    lastActivity: number;
    isActive: boolean;
}

/**
 * Terminal Tracker class
 */
export class TerminalTracker implements vscode.Disposable {
    private terminals: Map<number, TerminalInfo> = new Map();
    private eventEmitter: vscode.EventEmitter<any>;
    private disposables: vscode.Disposable[] = [];
    private nextId: number = 0;
    private debugMode: boolean;

    constructor(eventEmitter: vscode.EventEmitter<any>, debugMode: boolean = false) {
        this.eventEmitter = eventEmitter;
        this.debugMode = debugMode;
        this.subscribeToTerminalEvents();
    }

    /**
     * Subscribe to terminal events
     */
    private subscribeToTerminalEvents(): void {
        // Track terminal creation
        const onDidOpenTerminal = vscode.window.onDidOpenTerminal(terminal => {
            this.trackTerminal(terminal);
        });
        this.disposables.push(onDidOpenTerminal);

        // Track terminal disposal
        const onDidCloseTerminal = vscode.window.onDidCloseTerminal(terminal => {
            this.untrackTerminal(terminal);
        });
        this.disposables.push(onDidCloseTerminal);

        // Track terminal state changes
        const onDidChangeActiveTerminal = vscode.window.onDidChangeActiveTerminal(terminal => {
            this.updateActiveTerminal(terminal);
        });
        this.disposables.push(onDidChangeActiveTerminal);

        // Track existing terminals
        vscode.window.terminals.forEach(terminal => {
            this.trackTerminal(terminal);
        });
    }

    /**
     * Track a terminal
     */
    trackTerminal(terminal: vscode.Terminal): number {
        const terminalId = (terminal as any).processId || this.nextId++;
        const now = Date.now();

        const terminalInfo: TerminalInfo = {
            id: terminalId,
            terminal,
            name: terminal.name,
            createdAt: now,
            lastActivity: now,
            isActive: terminal === vscode.window.activeTerminal
        };

        this.terminals.set(terminalId, terminalInfo);

        if (this.debugMode) {
            console.log(`Terminal tracked: ${terminal.name} (${terminalId})`);
        }

        // Emit event
        this.eventEmitter.fire({
            type: 'terminal_tracked',
            terminalId,
            terminalName: terminal.name,
            timestamp: now
        });

        return terminalId;
    }

    /**
     * Untrack a terminal
     */
    untrackTerminal(terminal: vscode.Terminal): void {
        let terminalId: number | undefined;

        // Find the terminal ID
        for (const [id, info] of this.terminals.entries()) {
            if (info.terminal === terminal) {
                terminalId = id;
                break;
            }
        }

        if (terminalId !== undefined) {
            this.terminals.delete(terminalId);

            if (this.debugMode) {
                console.log(`Terminal untracked: ${terminal.name} (${terminalId})`);
            }

            // Emit event
            this.eventEmitter.fire({
                type: 'terminal_untracked',
                terminalId,
                terminalName: terminal.name,
                timestamp: Date.now()
            });
        }
    }

    /**
     * Update active terminal
     */
    private updateActiveTerminal(activeTerminal: vscode.Terminal | undefined): void {
        // Mark all terminals as inactive
        for (const info of this.terminals.values()) {
            info.isActive = false;
        }

        // Mark the active terminal
        if (activeTerminal) {
            const terminalId = this.findTerminalId(activeTerminal);
            if (terminalId !== undefined) {
                const info = this.terminals.get(terminalId);
                if (info) {
                    info.isActive = true;
                    info.lastActivity = Date.now();
                }
            }
        }
    }

    /**
     * Find terminal ID by terminal instance
     */
    findTerminalId(terminal: vscode.Terminal): number | undefined {
        for (const [id, info] of this.terminals.entries()) {
            if (info.terminal === terminal) {
                return id;
            }
        }
        return undefined;
    }

    /**
     * Get terminal info by ID
     */
    getTerminalInfo(terminalId: number): TerminalInfo | undefined {
        return this.terminals.get(terminalId);
    }

    /**
     * Get all terminal info
     */
    getAllTerminalInfo(): TerminalInfo[] {
        return Array.from(this.terminals.values());
    }

    /**
     * Get active terminal info
     */
    getActiveTerminalInfo(): TerminalInfo | undefined {
        return Array.from(this.terminals.values()).find(info => info.isActive);
    }

    /**
     * Get terminal by ID
     */
    getTerminal(terminalId: number): vscode.Terminal | undefined {
        return this.terminals.get(terminalId)?.terminal;
    }

    /**
     * Check if terminal is tracked
     */
    isTerminalTracked(terminal: vscode.Terminal): boolean {
        return this.findTerminalId(terminal) !== undefined;
    }

    /**
     * Update terminal activity
     */
    updateTerminalActivity(terminalId: number): void {
        const info = this.terminals.get(terminalId);
        if (info) {
            info.lastActivity = Date.now();
        }
    }

    /**
     * Get terminal statistics
     */
    getStatistics(): {
        totalTerminals: number;
        activeTerminals: number;
        oldestTerminal?: { name: string; age: number };
        newestTerminal?: { name: string; age: number };
        mostActiveTerminal?: { name: string; lastActivity: number };
    } {
        const terminals = Array.from(this.terminals.values());
        const now = Date.now();

        if (terminals.length === 0) {
            return {
                totalTerminals: 0,
                activeTerminals: 0
            };
        }

        const activeTerminals = terminals.filter(t => t.isActive).length;
        
        // Find oldest and newest terminals
        const sortedByAge = terminals.sort((a, b) => a.createdAt - b.createdAt);
        const oldest = sortedByAge[0];
        const newest = sortedByAge[sortedByAge.length - 1];

        // Find most active terminal
        const mostActive = terminals.reduce((prev, current) => 
            current.lastActivity > prev.lastActivity ? current : prev
        );

        return {
            totalTerminals: terminals.length,
            activeTerminals,
            oldestTerminal: {
                name: oldest.name,
                age: now - oldest.createdAt
            },
            newestTerminal: {
                name: newest.name,
                age: now - newest.createdAt
            },
            mostActiveTerminal: {
                name: mostActive.name,
                lastActivity: mostActive.lastActivity
            }
        };
    }

    /**
     * Clean up inactive terminals
     */
    cleanupInactiveTerminals(maxAge: number = 24 * 60 * 60 * 1000): number {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [id, info] of this.terminals.entries()) {
            if (!info.isActive && (now - info.lastActivity) > maxAge) {
                this.terminals.delete(id);
                cleanedCount++;

                if (this.debugMode) {
                    console.log(`Cleaned up inactive terminal: ${info.name} (${id})`);
                }
            }
        }

        return cleanedCount;
    }

    /**
     * Get terminals by name pattern
     */
    getTerminalsByName(namePattern: string | RegExp): TerminalInfo[] {
        const pattern = typeof namePattern === 'string' 
            ? new RegExp(namePattern, 'i') 
            : namePattern;

        return Array.from(this.terminals.values()).filter(info => 
            pattern.test(info.name)
        );
    }

    /**
     * Get terminals created within time range
     */
    getTerminalsInTimeRange(startTime: number, endTime: number): TerminalInfo[] {
        return Array.from(this.terminals.values()).filter(info =>
            info.createdAt >= startTime && info.createdAt <= endTime
        );
    }

    /**
     * Export terminal info to JSON
     */
    exportTerminalInfo(): string {
        const terminals = Array.from(this.terminals.values()).map(info => ({
            id: info.id,
            name: info.name,
            createdAt: info.createdAt,
            lastActivity: info.lastActivity,
            isActive: info.isActive
        }));
        return JSON.stringify(terminals, null, 2);
    }

    /**
     * Dispose of the terminal tracker
     */
    dispose(): void {
        this.disposables.forEach(disposable => disposable.dispose());
        this.terminals.clear();
    }
}
