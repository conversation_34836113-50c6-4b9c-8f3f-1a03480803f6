/**
 * Pattern Registry for Terminal Monitor
 * 
 * Manages terminal output patterns and their registration.
 */

/**
 * Terminal Output Pattern
 */
export interface TerminalPattern {
    id: string;
    name: string;
    description: string;
    regex: RegExp;
    severity: 'info' | 'warning' | 'error';
    tags?: string[];
}

/**
 * Pattern Registry class
 */
export class PatternRegistry {
    private patterns: Map<string, TerminalPattern> = new Map();
    private debugMode: boolean;

    constructor(debugMode: boolean = false) {
        this.debugMode = debugMode;
        this.registerPredefinedPatterns();
    }

    /**
     * Register predefined patterns
     */
    private registerPredefinedPatterns(): void {
        // Error patterns
        this.registerPattern({
            id: 'error-generic',
            name: 'Generic Error',
            description: 'Generic error message',
            regex: /\b(error|exception|fail(ed|ure)?)\b/i,
            severity: 'error',
            tags: ['error', 'generic']
        });

        this.registerPattern({
            id: 'error-npm',
            name: 'NPM Error',
            description: 'NPM error message',
            regex: /npm ERR!.*/i,
            severity: 'error',
            tags: ['error', 'npm']
        });

        this.registerPattern({
            id: 'error-typescript',
            name: 'TypeScript Error',
            description: 'TypeScript error message',
            regex: /TS\d+:.*/i,
            severity: 'error',
            tags: ['error', 'typescript']
        });

        this.registerPattern({
            id: 'error-python',
            name: 'Python Error',
            description: 'Python error message',
            regex: /(Traceback|Error:|Exception:).*/i,
            severity: 'error',
            tags: ['error', 'python']
        });

        this.registerPattern({
            id: 'error-java',
            name: 'Java Error',
            description: 'Java error message',
            regex: /(Exception in thread|at\s+\w+\.\w+\(.*:\d+\)).*/i,
            severity: 'error',
            tags: ['error', 'java']
        });

        this.registerPattern({
            id: 'error-compilation',
            name: 'Compilation Error',
            description: 'Compilation error message',
            regex: /\b(compilation|compile)\s+(error|failed)\b/i,
            severity: 'error',
            tags: ['error', 'compilation']
        });

        // Warning patterns
        this.registerPattern({
            id: 'warning-generic',
            name: 'Generic Warning',
            description: 'Generic warning message',
            regex: /\b(warning|warn|deprecated)\b/i,
            severity: 'warning',
            tags: ['warning', 'generic']
        });

        this.registerPattern({
            id: 'warning-npm',
            name: 'NPM Warning',
            description: 'NPM warning message',
            regex: /npm WARN.*/i,
            severity: 'warning',
            tags: ['warning', 'npm']
        });

        this.registerPattern({
            id: 'warning-security',
            name: 'Security Warning',
            description: 'Security warning message',
            regex: /\b(security|vulnerability|audit)\s+(warning|issue)\b/i,
            severity: 'warning',
            tags: ['warning', 'security']
        });

        this.registerPattern({
            id: 'warning-deprecation',
            name: 'Deprecation Warning',
            description: 'Deprecation warning message',
            regex: /\b(deprecated|deprecation)\b/i,
            severity: 'warning',
            tags: ['warning', 'deprecation']
        });

        // Info patterns
        this.registerPattern({
            id: 'info-generic',
            name: 'Generic Info',
            description: 'Generic info message',
            regex: /\b(info|notice)\b/i,
            severity: 'info',
            tags: ['info', 'generic']
        });

        this.registerPattern({
            id: 'info-npm',
            name: 'NPM Info',
            description: 'NPM info message',
            regex: /npm info.*/i,
            severity: 'info',
            tags: ['info', 'npm']
        });

        this.registerPattern({
            id: 'info-success',
            name: 'Success Info',
            description: 'Success info message',
            regex: /\b(success|completed|done|finished)\b/i,
            severity: 'info',
            tags: ['info', 'success']
        });

        this.registerPattern({
            id: 'info-build',
            name: 'Build Info',
            description: 'Build info message',
            regex: /\b(build|compile|bundle)\s+(start|complete|success)\b/i,
            severity: 'info',
            tags: ['info', 'build']
        });

        this.registerPattern({
            id: 'info-test',
            name: 'Test Info',
            description: 'Test info message',
            regex: /\b(test|spec)\s+(pass|fail|complete)\b/i,
            severity: 'info',
            tags: ['info', 'test']
        });
    }

    /**
     * Register a pattern
     */
    registerPattern(pattern: TerminalPattern): void {
        this.patterns.set(pattern.id, pattern);

        if (this.debugMode) {
            console.log(`Pattern registered: ${pattern.name} (${pattern.id})`);
        }
    }

    /**
     * Unregister a pattern
     */
    unregisterPattern(patternId: string): void {
        this.patterns.delete(patternId);

        if (this.debugMode) {
            console.log(`Pattern unregistered: ${patternId}`);
        }
    }

    /**
     * Get a pattern by ID
     */
    getPattern(patternId: string): TerminalPattern | undefined {
        return this.patterns.get(patternId);
    }

    /**
     * Get all patterns
     */
    getAllPatterns(): Map<string, TerminalPattern> {
        return new Map(this.patterns);
    }

    /**
     * Get patterns by severity
     */
    getPatternsBySeverity(severity: 'info' | 'warning' | 'error'): TerminalPattern[] {
        return Array.from(this.patterns.values()).filter(pattern => pattern.severity === severity);
    }

    /**
     * Get patterns by tag
     */
    getPatternsByTag(tag: string): TerminalPattern[] {
        return Array.from(this.patterns.values()).filter(pattern => 
            pattern.tags && pattern.tags.includes(tag)
        );
    }

    /**
     * Search patterns by name or description
     */
    searchPatterns(query: string): TerminalPattern[] {
        const queryLower = query.toLowerCase();
        return Array.from(this.patterns.values()).filter(pattern =>
            pattern.name.toLowerCase().includes(queryLower) ||
            pattern.description.toLowerCase().includes(queryLower)
        );
    }

    /**
     * Get pattern statistics
     */
    getStatistics(): {
        total: number;
        byType: Record<string, number>;
        bySeverity: Record<string, number>;
    } {
        const patterns = Array.from(this.patterns.values());
        const byType: Record<string, number> = {};
        const bySeverity: Record<string, number> = {};

        for (const pattern of patterns) {
            // Count by severity
            bySeverity[pattern.severity] = (bySeverity[pattern.severity] || 0) + 1;

            // Count by tags (types)
            if (pattern.tags) {
                for (const tag of pattern.tags) {
                    byType[tag] = (byType[tag] || 0) + 1;
                }
            }
        }

        return {
            total: patterns.length,
            byType,
            bySeverity
        };
    }

    /**
     * Export patterns to JSON
     */
    exportPatterns(): string {
        const patterns = Array.from(this.patterns.values()).map(pattern => ({
            ...pattern,
            regex: pattern.regex.source // Convert RegExp to string
        }));
        return JSON.stringify(patterns, null, 2);
    }

    /**
     * Import patterns from JSON
     */
    importPatterns(json: string): void {
        try {
            const patterns = JSON.parse(json);
            for (const patternData of patterns) {
                const pattern: TerminalPattern = {
                    ...patternData,
                    regex: new RegExp(patternData.regex, 'i') // Convert string back to RegExp
                };
                this.registerPattern(pattern);
            }
        } catch (error) {
            console.error('Failed to import patterns:', error);
            throw new Error('Invalid pattern JSON format');
        }
    }

    /**
     * Clear all patterns
     */
    clearPatterns(): void {
        this.patterns.clear();
        if (this.debugMode) {
            console.log('All patterns cleared');
        }
    }

    /**
     * Reset to default patterns
     */
    resetToDefaults(): void {
        this.clearPatterns();
        this.registerPredefinedPatterns();
        if (this.debugMode) {
            console.log('Patterns reset to defaults');
        }
    }
}
