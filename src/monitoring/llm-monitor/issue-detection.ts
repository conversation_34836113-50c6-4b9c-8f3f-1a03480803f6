/**
 * LLM Monitor Issue Detection
 * 
 * This module provides functionality for detecting issues in LLM responses.
 */

import { LLMIssue, LLMIssueType, LLMRequest, LLMResponse } from './types.js';

/**
 * Issue Detection Service
 */
export class IssueDetectionService {
    private nextId: number = 0;

    /**
     * Detect issues in an LLM response
     * @param response Response text
     * @param responseId Response ID
     * @param request Optional request for context
     * @returns Array of detected issues
     */
    detectIssues(response: string, responseId: string, request?: LLMRequest): LLMIssue[] {
        const issues: LLMIssue[] = [];

        // Check for repetition
        if (this.detectRepetition(response)) {
            const issue: LLMIssue = {
                id: `issue-${Date.now()}-${this.nextId++}`,
                responseId,
                timestamp: Date.now(),
                type: LLMIssueType.REPETITION,
                description: 'Detected repetitive patterns in the response',
                severity: 'medium',
                confidence: 0.8
            };

            issues.push(issue);
        }

        // Check for incomplete response
        if (this.detectIncompleteResponse(response)) {
            const issue: LLMIssue = {
                id: `issue-${Date.now()}-${this.nextId++}`,
                responseId,
                timestamp: Date.now(),
                type: LLMIssueType.INCOMPLETE,
                description: 'Response appears to be cut off or incomplete',
                severity: 'medium',
                confidence: 0.75
            };

            issues.push(issue);
        }

        // Check for hallucinations if we have the request context
        if (request) {
            const hallucinationIssues = this.detectHallucinations(response, request);
            for (const issue of hallucinationIssues) {
                issue.responseId = responseId;
                issues.push(issue);
            }
        }

        // Check for loops in agent behavior
        if (request?.agentId) {
            const agentResponses = request.metadata?.previousResponses as string[] || [];

            if (agentResponses.length > 3) {
                const loopDetected = this.detectLoops(agentResponses);
                if (loopDetected) {
                    const issue: LLMIssue = {
                        id: `issue-${Date.now()}-${this.nextId++}`,
                        responseId,
                        timestamp: Date.now(),
                        type: LLMIssueType.LOOP_DETECTED,
                        description: 'Agent appears to be stuck in a loop',
                        severity: 'high',
                        confidence: 0.85
                    };

                    issues.push(issue);
                }
            }
        }

        return issues;
    }

    /**
     * Detect hallucinations in LLM output
     * @param response Response text
     * @param request Original request
     * @returns Array of detected hallucination issues
     */
    private detectHallucinations(response: string, request: LLMRequest): LLMIssue[] {
        const issues: LLMIssue[] = [];

        // Check for made-up references or citations
        if (response.includes('http') || response.includes('www.')) {
            // Look for URLs that don't exist in the prompt
            const urlRegex = /(https?:\/\/[^\s]+)/g;
            const urls = response.match(urlRegex) || [];

            for (const url of urls) {
                if (!request.prompt.includes(url)) {
                    // This URL wasn't in the original prompt, might be hallucinated
                    const issue: LLMIssue = {
                        id: `issue-${Date.now()}-${this.nextId++}`,
                        responseId: '',  // Will be set by the caller
                        timestamp: Date.now(),
                        type: LLMIssueType.HALLUCINATION,
                        description: `Potentially hallucinated URL: ${url}`,
                        severity: 'medium',
                        confidence: 0.7,
                        metadata: { url }
                    };

                    issues.push(issue);
                }
            }
        }

        // Check for contradictions with the prompt
        const promptKeywords = request.prompt
            .toLowerCase()
            .split(/\W+/)
            .filter(word => word.length > 3)
            .filter(word => !['this', 'that', 'with', 'from', 'have', 'what', 'when', 'where', 'which'].includes(word));

        const negations = response.match(/not|isn't|aren't|wasn't|weren't|doesn't|don't|cannot|can't|never/g) || [];

        if (negations.length > 0) {
            for (const keyword of promptKeywords) {
                // Check if any negation is close to a keyword from the prompt
                const keywordRegex = new RegExp(`(not|isn't|aren't|wasn't|weren't|doesn't|don't|cannot|can't|never)\\s+([\\w\\s]{0,20})${keyword}|${keyword}([\\w\\s]{0,20})(not|isn't|aren't|wasn't|weren't|doesn't|don't|cannot|can't|never)`, 'gi');
                const matches = response.match(keywordRegex) || [];

                if (matches.length > 0) {
                    const issue: LLMIssue = {
                        id: `issue-${Date.now()}-${this.nextId++}`,
                        responseId: '',  // Will be set by the caller
                        timestamp: Date.now(),
                        type: LLMIssueType.CONTRADICTION,
                        description: `Potential contradiction with prompt keyword: ${keyword}`,
                        severity: 'medium',
                        confidence: 0.65,
                        metadata: { keyword, matches }
                    };

                    issues.push(issue);
                }
            }
        }

        return issues;
    }

    /**
     * Detect loops in agent behavior
     * @param responses Array of recent responses from the agent
     * @returns True if a loop is detected
     */
    detectLoops(responses: string[]): boolean {
        if (responses.length < 3) {
            return false;
        }

        // Check for exact repetition
        const lastResponse = responses[responses.length - 1];
        const secondLastResponse = responses[responses.length - 2];
        const thirdLastResponse = responses[responses.length - 3];

        if (lastResponse === secondLastResponse || lastResponse === thirdLastResponse) {
            return true;
        }

        // Check for similar responses using a simple similarity metric
        const similarity1 = this.calculateSimilarity(lastResponse, secondLastResponse);
        const similarity2 = this.calculateSimilarity(lastResponse, thirdLastResponse);

        return similarity1 > 0.9 || similarity2 > 0.9;
    }

    /**
     * Calculate similarity between two strings (simple Jaccard similarity)
     * @param str1 First string
     * @param str2 Second string
     * @returns Similarity score between 0 and 1
     */
    private calculateSimilarity(str1: string, str2: string): number {
        const set1 = new Set(str1.toLowerCase().split(/\W+/).filter(s => s.length > 0));
        const set2 = new Set(str2.toLowerCase().split(/\W+/).filter(s => s.length > 0));

        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);

        return intersection.size / union.size;
    }

    /**
     * Detect repetition in text
     * @param text Text to analyze
     * @returns True if repetition is detected
     */
    detectRepetition(text: string): boolean {
        // Simple repetition detection
        const lines = text.split('\n');
        const uniqueLines = new Set(lines);

        // If there are significantly fewer unique lines than total lines, there's repetition
        if (lines.length > 10 && uniqueLines.size < lines.length * 0.7) {
            return true;
        }

        // Check for repeated phrases
        const words = text.split(/\s+/);
        const phrases: string[] = [];

        for (let i = 0; i < words.length - 3; i++) {
            phrases.push(`${words[i]} ${words[i + 1]} ${words[i + 2]}`);
        }

        const phraseCounts: Record<string, number> = {};

        for (const phrase of phrases) {
            phraseCounts[phrase] = (phraseCounts[phrase] || 0) + 1;
        }

        // If any phrase appears more than 3 times, there's repetition
        return Object.values(phraseCounts).some(count => count > 3);
    }

    /**
     * Detect incomplete response
     * @param text Text to analyze
     * @returns True if the response appears incomplete
     */
    detectIncompleteResponse(text: string): boolean {
        // Check if the text ends with an incomplete sentence
        const lastChar = text.trim().slice(-1);

        if (lastChar && !'.!?;:)"\''.includes(lastChar)) {
            return true;
        }

        // Check for code blocks that aren't closed
        const codeBlockStarts = (text.match(/```/g) || []).length;

        if (codeBlockStarts % 2 !== 0) {
            return true;
        }

        // Check for unclosed parentheses, brackets, etc.
        const openParens = (text.match(/\(/g) || []).length;
        const closeParens = (text.match(/\)/g) || []).length;
        const openBrackets = (text.match(/\[/g) || []).length;
        const closeBrackets = (text.match(/\]/g) || []).length;
        const openBraces = (text.match(/\{/g) || []).length;
        const closeBraces = (text.match(/\}/g) || []).length;

        if (openParens !== closeParens || openBrackets !== closeBrackets || openBraces !== closeBraces) {
            return true;
        }

        return false;
    }
}
