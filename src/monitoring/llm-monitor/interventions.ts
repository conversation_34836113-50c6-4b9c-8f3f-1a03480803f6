/**
 * LLM Monitor Interventions
 *
 * This module provides functionality for applying interventions to address LLM issues.
 */

import * as vscode from 'vscode';
import {
    LLMIssue,
    LLMIssueType,
    LLMIntervention,
    InterventionStrategy,
    LLMRequest,
    LLMMonitorOptions
} from './types.js';
import { EventHandlerService } from './event-handler.js';

/**
 * Intervention Service
 */
export class InterventionService {
    private nextId: number = 0;
    private options: LLMMonitorOptions;
    private statusBarItem: vscode.StatusBarItem;
    private eventHandlerService: EventHandlerService;

    constructor(
        options: LLMMonitorOptions,
        statusBarItem: vscode.StatusBarItem,
        eventHandlerService: EventHandlerService
    ) {
        this.options = options;
        this.statusBarItem = statusBarItem;
        this.eventHandlerService = eventHandlerService;
    }

    /**
     * Apply interventions for detected issues
     * @param issues Detected issues
     * @param request Original request
     * @returns Array of applied interventions
     */
    applyInterventions(issues: LLMIssue[], request?: LLMRequest, previousInterventions: LLMIntervention[] = []): LLMIntervention[] {
        if (!request || !this.options.enableInterventions) {
            return [];
        }

        const appliedInterventions: LLMIntervention[] = [];

        for (const issue of issues) {
            // Skip issues with low confidence
            if (issue.confidence < this.options.issueDetectionConfidenceThreshold!) {
                continue;
            }

            // Determine the appropriate intervention strategy
            const strategy = this.determineInterventionStrategy(issue, request, previousInterventions);

            // Create intervention record
            const intervention: LLMIntervention = {
                id: `int-${Date.now()}-${this.nextId++}`,
                issueId: issue.id,
                timestamp: Date.now(),
                strategy,
                description: `Intervention for ${issue.type}: ${issue.description}`
            };

            // Apply the intervention
            this.executeIntervention(intervention, issue, request);

            // Add to result
            appliedInterventions.push(intervention);

            // Emit event
            this.eventHandlerService.emitInterventionEvent(intervention, issue);

            // If this is an agent-related issue, notify the agent system
            if (request.agentId) {
                this.eventHandlerService.emitAgentInterventionEvent(request.agentId, intervention, issue);
            }

            if (this.options.debugMode) {
                console.log(`LLM intervention applied: ${strategy}`, {
                    id: intervention.id,
                    issueId: issue.id,
                    issueType: issue.type,
                    agentId: request.agentId || 'none'
                });
            }
        }

        return appliedInterventions;
    }

    /**
     * Apply agent-specific intervention
     * @param agentId Agent ID
     * @param issueType Type of issue to address
     * @param description Description of the issue
     * @param previousInterventions Previous interventions
     * @returns Intervention if applied, undefined otherwise
     */
    applyAgentIntervention(
        agentId: string,
        issueType: LLMIssueType,
        description: string,
        previousInterventions: LLMIntervention[] = []
    ): LLMIntervention | undefined {
        if (!this.options.enableInterventions) {
            return undefined;
        }

        // Create a synthetic issue
        const issue: LLMIssue = {
            id: `issue-${Date.now()}-${this.nextId++}`,
            responseId: '',  // No specific response
            timestamp: Date.now(),
            type: issueType,
            description,
            severity: 'medium',
            confidence: 0.9,  // High confidence since this is manually triggered
            agentId
        };

        // Create a synthetic request
        const request: LLMRequest = {
            id: `req-${Date.now()}-${this.nextId++}`,
            timestamp: Date.now(),
            modelName: 'unknown',
            prompt: '',
            options: {},
            agentId
        };

        // Determine intervention strategy
        const strategy = this.determineInterventionStrategy(issue, request, previousInterventions);

        // Create intervention record
        const intervention: LLMIntervention = {
            id: `int-${Date.now()}-${this.nextId++}`,
            issueId: issue.id,
            timestamp: Date.now(),
            strategy,
            description: `Agent intervention for ${issueType}: ${description}`
        };

        // Apply the intervention
        this.executeIntervention(intervention, issue, request);

        // Emit event
        this.eventHandlerService.emitAgentInterventionEvent(agentId, intervention, issue);

        if (this.options.debugMode) {
            console.log(`Agent intervention applied: ${strategy}`, {
                id: intervention.id,
                issueId: issue.id,
                issueType,
                agentId
            });
        }

        return intervention;
    }

    /**
     * Determine the appropriate intervention strategy for an issue
     * @param issue Detected issue
     * @param request Original request
     * @param previousInterventions Previous interventions
     * @returns Intervention strategy
     */
    private determineInterventionStrategy(
        issue: LLMIssue,
        request: LLMRequest,
        previousInterventions: LLMIntervention[]
    ): InterventionStrategy {
        // Get available strategies
        const availableStrategies = this.options.interventionStrategies || [
            InterventionStrategy.RETRY,
            InterventionStrategy.REFINE_PROMPT,
            InterventionStrategy.SWITCH_MODEL,
            InterventionStrategy.FALLBACK
        ];

        // If we've already tried too many interventions, ask for human help
        if (previousInterventions.length >= this.options.maxRetries!) {
            return InterventionStrategy.HUMAN_ASSISTANCE;
        }

        // Choose strategy based on issue type
        switch (issue.type) {
            case LLMIssueType.HALLUCINATION:
            case LLMIssueType.FACTUAL_ERROR:
                // For hallucinations, try switching to a larger model if available
                return availableStrategies.includes(InterventionStrategy.SWITCH_MODEL)
                    ? InterventionStrategy.SWITCH_MODEL
                    : InterventionStrategy.REFINE_PROMPT;

            case LLMIssueType.REPETITION:
            case LLMIssueType.LOOP_DETECTED:
                // For repetition or loops, try refining the prompt
                return availableStrategies.includes(InterventionStrategy.REFINE_PROMPT)
                    ? InterventionStrategy.REFINE_PROMPT
                    : InterventionStrategy.RETRY;

            case LLMIssueType.INCOMPLETE:
            case LLMIssueType.FORMAT_ERROR:
                // For incomplete responses or format errors, try a simple retry
                return InterventionStrategy.RETRY;

            case LLMIssueType.CONTEXT_WINDOW_EXCEEDED:
            case LLMIssueType.TOKEN_LIMIT_EXCEEDED:
                // For context window or token limit issues, reduce complexity
                return availableStrategies.includes(InterventionStrategy.REDUCE_COMPLEXITY)
                    ? InterventionStrategy.REDUCE_COMPLEXITY
                    : InterventionStrategy.FALLBACK;

            default:
                // Default to retry
                return InterventionStrategy.RETRY;
        }
    }

    /**
     * Execute an intervention
     * @param intervention Intervention to execute
     * @param issue Issue that triggered the intervention
     * @param request Original request
     */
    private executeIntervention(intervention: LLMIntervention, issue: LLMIssue, request: LLMRequest): void {
        // Update status bar to show intervention
        this.statusBarItem.text = `$(alert) LLM Monitor: Intervention`;
        this.statusBarItem.tooltip = `Applying ${intervention.strategy} for ${issue.type}`;

        // Execute the intervention based on the strategy
        switch (intervention.strategy) {
            case InterventionStrategy.RETRY:
                // Simply retry the request
                this.notifyIntervention(
                    'Retrying LLM request',
                    `Detected ${issue.type}. Retrying the request.`
                );
                break;

            case InterventionStrategy.REFINE_PROMPT:
                // Refine the prompt to address the issue
                this.notifyIntervention(
                    'Refining prompt',
                    `Detected ${issue.type}. Refining the prompt to address the issue.`
                );
                break;

            case InterventionStrategy.SWITCH_MODEL:
                // Switch to a larger model
                this.notifyIntervention(
                    'Switching to larger model',
                    `Detected ${issue.type}. Switching to a larger model for better results.`
                );
                break;

            case InterventionStrategy.REDUCE_COMPLEXITY:
                // Reduce the complexity of the request
                this.notifyIntervention(
                    'Reducing complexity',
                    `Detected ${issue.type}. Reducing the complexity of the request.`
                );
                break;

            case InterventionStrategy.HUMAN_ASSISTANCE:
                // Request human assistance
                this.requestHumanAssistance(issue, request);
                break;

            case InterventionStrategy.FALLBACK:
                // Use a fallback response
                this.notifyIntervention(
                    'Using fallback response',
                    `Detected ${issue.type}. Using a fallback response.`
                );
                break;

            default:
                // No intervention
                break;
        }

        // Reset status bar after a delay
        setTimeout(() => {
            this.statusBarItem.text = '$(eye) LLM Monitor';
            this.statusBarItem.tooltip = 'LLM Monitor is active';
        }, 5000);
    }

    /**
     * Request human assistance for an issue
     * @param issue Issue that triggered the intervention
     * @param request Original request
     */
    private requestHumanAssistance(issue: LLMIssue, request: LLMRequest): void {
        vscode.window.showErrorMessage(
            `LLM issue detected: ${issue.type}. Human assistance required.`,
            'View Details'
        ).then(selection => {
            if (selection === 'View Details') {
                // Show details in a webview or information message
                const details = [
                    `Issue Type: ${issue.type}`,
                    `Description: ${issue.description}`,
                    `Severity: ${issue.severity}`,
                    `Confidence: ${issue.confidence}`,
                    `Model: ${request.modelName}`,
                    `Prompt: ${request.prompt.substring(0, 100)}...`
                ].join('\n');

                vscode.window.showInformationMessage(details);
            }
        });
    }

    /**
     * Show a notification for an intervention
     * @param title Notification title
     * @param message Notification message
     */
    private notifyIntervention(title: string, message: string): void {
        vscode.window.showInformationMessage(`${title}: ${message}`);
    }
}
