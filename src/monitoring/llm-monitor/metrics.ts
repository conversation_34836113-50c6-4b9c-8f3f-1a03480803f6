/**
 * LLM Monitor Metrics
 *
 * This module provides functionality for tracking and analyzing LLM usage metrics.
 */

import { AgentStatus } from '../../agents/base-agent.js';
import {
    LLMRequest,
    LLMResponse,
    LLMError,
    LLMIssue,
    LLMUsageStats,
    AgentBehaviorAnalysis
} from './types.js';
import { TimeStatsService } from './time-stats.js';
import { AgentMetricsService } from './agent-metrics.js';

/**
 * Metrics Service
 */
export class MetricsService {
    private timeStatsService: TimeStatsService;
    private agentMetricsService: AgentMetricsService;

    constructor() {
        this.timeStatsService = new TimeStatsService();
        this.agentMetricsService = new AgentMetricsService();
    }
    /**
     * Calculate usage statistics
     * @param requests Array of LLM requests
     * @param responses Array of LLM responses
     * @param errors Array of LLM errors
     * @param issues Array of LLM issues
     * @returns Usage statistics
     */
    calculateUsageStats(
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[],
        issues: LLMIssue[]
    ): LLMUsageStats {
        const now = Date.now();
        const last24Hours = now - 24 * 60 * 60 * 1000;
        const last7Days = now - 7 * 24 * 60 * 60 * 1000;
        const last30Days = now - 30 * 24 * 60 * 60 * 1000;

        // Calculate total stats
        const totalRequests = requests.length;
        const totalResponses = responses.length;
        const totalErrors = errors.length;
        const totalTokens = responses.reduce((sum, res) => sum + (res.tokenCount || 0), 0);
        const totalLatency = responses.reduce((sum, res) => sum + res.latencyMs, 0);
        const averageLatencyMs = totalResponses > 0 ? totalLatency / totalResponses : 0;
        const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;
        const issueRate = totalResponses > 0 ? issues.length / totalResponses : 0;

        // Calculate model-specific stats
        const modelStats: Record<string, any> = {};

        // Get unique model names
        const modelNames = [...new Set(requests.map(req => req.modelName))];

        for (const modelName of modelNames) {
            const modelRequests = requests.filter(req => req.modelName === modelName);
            const modelResponses = responses.filter(res => res.modelName === modelName);
            const modelErrors = errors.filter(err => err.modelName === modelName);
            const modelIssues = issues.filter(issue => {
                const response = responses.find(res => res.id === issue.responseId);
                return response && response.modelName === modelName;
            });

            const requestCount = modelRequests.length;
            const responseCount = modelResponses.length;
            const errorCount = modelErrors.length;
            const tokens = modelResponses.reduce((sum, res) => sum + (res.tokenCount || 0), 0);
            const latency = modelResponses.reduce((sum, res) => sum + res.latencyMs, 0);
            const avgLatency = responseCount > 0 ? latency / responseCount : 0;
            const errRate = requestCount > 0 ? errorCount / requestCount : 0;
            const issRate = responseCount > 0 ? modelIssues.length / responseCount : 0;

            modelStats[modelName] = {
                requests: requestCount,
                responses: responseCount,
                errors: errorCount,
                tokens,
                averageLatencyMs: avgLatency,
                errorRate: errRate,
                issueRate: issRate
            };
        }

        // Calculate time-based stats using service
        const timeStats = this.timeStatsService.calculateTimeStats(requests, responses, errors);

        return {
            totalRequests,
            totalResponses,
            totalErrors,
            totalTokens,
            averageLatencyMs,
            errorRate,
            issueRate,
            modelStats,
            timeStats
        };
    }

    /**
     * Calculate agent-specific usage statistics
     * @param agentId Agent ID
     * @param requests Array of LLM requests
     * @param responses Array of LLM responses
     * @param errors Array of LLM errors
     * @param issues Array of LLM issues
     * @returns Agent-specific usage statistics
     */
    calculateAgentStats(
        agentId: string,
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[],
        issues: LLMIssue[]
    ): LLMUsageStats {
        return this.agentMetricsService.calculateAgentStats(
            agentId,
            requests,
            responses,
            errors,
            issues
        );
    }

    /**
     * Analyze agent behavior patterns
     * @param agentId Agent ID
     * @param responses Array of agent responses
     * @param issues Array of agent issues
     * @param errors Array of agent errors
     * @returns Analysis of agent behavior patterns
     */
    analyzeAgentBehavior(
        agentId: string,
        responses: LLMResponse[],
        issues: LLMIssue[],
        errors: LLMError[]
    ): AgentBehaviorAnalysis {
        return this.agentMetricsService.analyzeAgentBehavior(
            agentId,
            responses,
            issues,
            errors
        );
    }

}
