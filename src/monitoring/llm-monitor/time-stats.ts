/**
 * Time-based Statistics Calculator for LLM Monitor
 * 
 * Handles calculation of time-based statistics for LLM monitoring.
 */

import { LLMRequest, LLMResponse, LLMError } from './types.js';

export interface TimeWindow {
    requests: number;
    responses: number;
    errors: number;
    tokens: number;
}

export interface TimeStats {
    last24Hours: TimeWindow;
    last7Days: TimeWindow;
    last30Days: TimeWindow;
}

/**
 * Time-based statistics calculator service
 */
export class TimeStatsService {
    /**
     * Calculate time-based statistics
     * @param requests All requests
     * @param responses All responses
     * @param errors All errors
     * @returns Time-based statistics
     */
    calculateTimeStats(
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[]
    ): TimeStats {
        const now = Date.now();
        const last24Hours = now - 24 * 60 * 60 * 1000;
        const last7Days = now - 7 * 24 * 60 * 60 * 1000;
        const last30Days = now - 30 * 24 * 60 * 60 * 1000;

        return {
            last24Hours: this.calculateTimeWindow(requests, responses, errors, last24Hours),
            last7Days: this.calculateTimeWindow(requests, responses, errors, last7Days),
            last30Days: this.calculateTimeWindow(requests, responses, errors, last30Days)
        };
    }

    /**
     * Calculate statistics for a specific time window
     * @param requests All requests
     * @param responses All responses
     * @param errors All errors
     * @param cutoffTime Cutoff timestamp
     * @returns Time window statistics
     */
    private calculateTimeWindow(
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[],
        cutoffTime: number
    ): TimeWindow {
        const recentRequests = requests.filter(req => req.timestamp >= cutoffTime);
        const recentResponses = responses.filter(res => res.timestamp >= cutoffTime);
        const recentErrors = errors.filter(err => err.timestamp >= cutoffTime);
        const recentTokens = recentResponses.reduce((sum, res) => sum + (res.tokenCount || 0), 0);

        return {
            requests: recentRequests.length,
            responses: recentResponses.length,
            errors: recentErrors.length,
            tokens: recentTokens
        };
    }

    /**
     * Calculate agent-specific time statistics
     * @param agentId Agent ID
     * @param requests All requests
     * @param responses All responses
     * @param errors All errors
     * @returns Agent-specific time statistics
     */
    calculateAgentTimeStats(
        agentId: string,
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[]
    ): TimeStats {
        // Filter data for this agent
        const agentRequests = requests.filter(req => req.agentId === agentId);
        const agentResponses = responses.filter(res => res.agentId === agentId);
        const agentErrors = errors.filter(err => err.agentId === agentId);

        return this.calculateTimeStats(agentRequests, agentResponses, agentErrors);
    }

    /**
     * Calculate hourly statistics for the last 24 hours
     * @param requests All requests
     * @param responses All responses
     * @param errors All errors
     * @returns Hourly statistics array
     */
    calculateHourlyStats(
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[]
    ): Array<{ hour: number; requests: number; responses: number; errors: number; tokens: number }> {
        const now = Date.now();
        const hourlyStats: Array<{ hour: number; requests: number; responses: number; errors: number; tokens: number }> = [];

        for (let i = 23; i >= 0; i--) {
            const hourStart = now - (i + 1) * 60 * 60 * 1000;
            const hourEnd = now - i * 60 * 60 * 1000;

            const hourRequests = requests.filter(req => req.timestamp >= hourStart && req.timestamp < hourEnd);
            const hourResponses = responses.filter(res => res.timestamp >= hourStart && res.timestamp < hourEnd);
            const hourErrors = errors.filter(err => err.timestamp >= hourStart && err.timestamp < hourEnd);
            const hourTokens = hourResponses.reduce((sum, res) => sum + (res.tokenCount || 0), 0);

            hourlyStats.push({
                hour: 23 - i,
                requests: hourRequests.length,
                responses: hourResponses.length,
                errors: hourErrors.length,
                tokens: hourTokens
            });
        }

        return hourlyStats;
    }

    /**
     * Calculate daily statistics for the last 30 days
     * @param requests All requests
     * @param responses All responses
     * @param errors All errors
     * @returns Daily statistics array
     */
    calculateDailyStats(
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[]
    ): Array<{ day: number; requests: number; responses: number; errors: number; tokens: number }> {
        const now = Date.now();
        const dailyStats: Array<{ day: number; requests: number; responses: number; errors: number; tokens: number }> = [];

        for (let i = 29; i >= 0; i--) {
            const dayStart = now - (i + 1) * 24 * 60 * 60 * 1000;
            const dayEnd = now - i * 24 * 60 * 60 * 1000;

            const dayRequests = requests.filter(req => req.timestamp >= dayStart && req.timestamp < dayEnd);
            const dayResponses = responses.filter(res => res.timestamp >= dayStart && res.timestamp < dayEnd);
            const dayErrors = errors.filter(err => err.timestamp >= dayStart && err.timestamp < dayEnd);
            const dayTokens = dayResponses.reduce((sum, res) => sum + (res.tokenCount || 0), 0);

            dailyStats.push({
                day: 29 - i,
                requests: dayRequests.length,
                responses: dayResponses.length,
                errors: dayErrors.length,
                tokens: dayTokens
            });
        }

        return dailyStats;
    }

    /**
     * Calculate peak usage times
     * @param requests All requests
     * @returns Peak usage information
     */
    calculatePeakUsage(requests: LLMRequest[]): {
        peakHour: number;
        peakDay: number;
        peakRequestsPerHour: number;
        peakRequestsPerDay: number;
    } {
        const hourCounts = new Array(24).fill(0);
        const dayCounts = new Array(7).fill(0);

        for (const request of requests) {
            const date = new Date(request.timestamp);
            const hour = date.getHours();
            const day = date.getDay();

            hourCounts[hour]++;
            dayCounts[day]++;
        }

        const peakHour = hourCounts.indexOf(Math.max(...hourCounts));
        const peakDay = dayCounts.indexOf(Math.max(...dayCounts));
        const peakRequestsPerHour = Math.max(...hourCounts);
        const peakRequestsPerDay = Math.max(...dayCounts);

        return {
            peakHour,
            peakDay,
            peakRequestsPerHour,
            peakRequestsPerDay
        };
    }

    /**
     * Calculate average response time over time
     * @param responses All responses
     * @param timeWindow Time window in milliseconds
     * @returns Array of average response times
     */
    calculateResponseTimeOverTime(
        responses: LLMResponse[],
        timeWindow: number = 60 * 60 * 1000 // 1 hour default
    ): Array<{ timestamp: number; averageLatency: number; count: number }> {
        if (responses.length === 0) {
            return [];
        }

        const sortedResponses = responses.sort((a, b) => a.timestamp - b.timestamp);
        const firstTimestamp = sortedResponses[0].timestamp;
        const lastTimestamp = sortedResponses[sortedResponses.length - 1].timestamp;
        const timeSlots: Array<{ timestamp: number; averageLatency: number; count: number }> = [];

        for (let timestamp = firstTimestamp; timestamp <= lastTimestamp; timestamp += timeWindow) {
            const windowResponses = responses.filter(
                res => res.timestamp >= timestamp && res.timestamp < timestamp + timeWindow
            );

            if (windowResponses.length > 0) {
                const averageLatency = windowResponses.reduce((sum, res) => sum + res.latencyMs, 0) / windowResponses.length;
                timeSlots.push({
                    timestamp,
                    averageLatency,
                    count: windowResponses.length
                });
            }
        }

        return timeSlots;
    }

    /**
     * Calculate error rate over time
     * @param requests All requests
     * @param errors All errors
     * @param timeWindow Time window in milliseconds
     * @returns Array of error rates
     */
    calculateErrorRateOverTime(
        requests: LLMRequest[],
        errors: LLMError[],
        timeWindow: number = 60 * 60 * 1000 // 1 hour default
    ): Array<{ timestamp: number; errorRate: number; requestCount: number; errorCount: number }> {
        if (requests.length === 0) {
            return [];
        }

        const sortedRequests = requests.sort((a, b) => a.timestamp - b.timestamp);
        const firstTimestamp = sortedRequests[0].timestamp;
        const lastTimestamp = sortedRequests[sortedRequests.length - 1].timestamp;
        const timeSlots: Array<{ timestamp: number; errorRate: number; requestCount: number; errorCount: number }> = [];

        for (let timestamp = firstTimestamp; timestamp <= lastTimestamp; timestamp += timeWindow) {
            const windowRequests = requests.filter(
                req => req.timestamp >= timestamp && req.timestamp < timestamp + timeWindow
            );
            const windowErrors = errors.filter(
                err => err.timestamp >= timestamp && err.timestamp < timestamp + timeWindow
            );

            if (windowRequests.length > 0) {
                const errorRate = windowErrors.length / windowRequests.length;
                timeSlots.push({
                    timestamp,
                    errorRate,
                    requestCount: windowRequests.length,
                    errorCount: windowErrors.length
                });
            }
        }

        return timeSlots;
    }
}
