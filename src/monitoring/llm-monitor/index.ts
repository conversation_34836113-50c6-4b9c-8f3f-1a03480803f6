/**
 * LLM Monitor for X10sion
 *
 * This module provides monitoring capabilities for language model interactions,
 * including tracking usage, performance, and potential issues.
 */

import * as vscode from 'vscode';
import { BaseAgent, AgentStatus } from '../../agents/base-agent.js';
import { IssueDetectionService } from './issue-detection.js';
import { InterventionService } from './interventions.js';
import { MetricsService } from './metrics.js';
import { PersistenceService } from './persistence.js';
import { EventHandlerService } from './event-handler.js';
import { AgentManagerService } from './agent-manager.js';
import {
    LLMRequest,
    LLMResponse,
    LLMError,
    LLMIssue,
    LLMIntervention,
    LLMUsageStats,
    LLMMonitorOptions,
    LLMIssueType,
    InterventionStrategy,
    AgentBehaviorAnalysis
} from './types.js';

// Re-export types for external use
export {
    LLMRequest,
    LLMResponse,
    LLMError,
    LLMIssue,
    LLMIntervention,
    LLMUsageStats,
    LLMMonitorOptions,
    LLMIssueType,
    InterventionStrategy,
    AgentBehaviorAnalysis
};

/**
 * LLM Monitor class
 *
 * Monitors language model interactions for performance, usage, and issues.
 */
export class LLMMonitor {
    private requests: LLMRequest[] = [];
    private responses: LLMResponse[] = [];
    private errors: LLMError[] = [];
    private issues: LLMIssue[] = [];
    private interventions: LLMIntervention[] = [];
    private options: LLMMonitorOptions;
    private disposables: vscode.Disposable[] = [];
    private nextId: number = 0;
    private statusBarItem: vscode.StatusBarItem;

    // Services
    private issueDetectionService: IssueDetectionService;
    private interventionService: InterventionService;
    private metricsService: MetricsService;
    private persistenceService: PersistenceService;
    private eventHandlerService: EventHandlerService;
    private agentManagerService: AgentManagerService;

    constructor(options: LLMMonitorOptions = {}) {
        this.options = {
            maxHistoryItems: 1000,
            enableIssueDetection: true,
            issueDetectionConfidenceThreshold: 0.7,
            enableTokenCounting: true,
            enablePersistence: false,
            persistencePath: '',
            debugMode: false,
            enableInterventions: true,
            interventionStrategies: [
                InterventionStrategy.RETRY,
                InterventionStrategy.REFINE_PROMPT,
                InterventionStrategy.SWITCH_MODEL,
                InterventionStrategy.FALLBACK
            ],
            maxRetries: 3,
            smallModelThreshold: 8192, // 8K context window is considered small
            ...options
        };

        console.log('LLM Monitor initialized with options:', this.options);

        // Create status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.statusBarItem.text = '$(eye) LLM Monitor';
        this.statusBarItem.tooltip = 'LLM Monitor is active';
        this.statusBarItem.command = 'x10sion.showLLMMonitorStats';
        this.statusBarItem.show();
        this.disposables.push(this.statusBarItem);

        // Initialize services
        this.issueDetectionService = new IssueDetectionService();
        this.eventHandlerService = new EventHandlerService();
        this.interventionService = new InterventionService(
            this.options,
            this.statusBarItem,
            this.eventHandlerService
        );
        this.metricsService = new MetricsService();
        this.persistenceService = new PersistenceService(this.options);
        this.agentManagerService = new AgentManagerService(
            this.metricsService,
            this.interventionService,
            this.options
        );

        // Load persisted data if enabled
        if (this.options.enablePersistence && this.options.persistencePath) {
            const persistedData = this.persistenceService.loadPersistedData();
            this.requests = persistedData.requests;
            this.responses = persistedData.responses;
            this.errors = persistedData.errors;
            this.issues = persistedData.issues;
            this.interventions = persistedData.interventions;
        }
    }

    /**
     * Register an agent with the monitor
     * @param agent Agent to register
     */
    registerAgent(agent: BaseAgent): void {
        this.agentManagerService.registerAgent(agent);
    }

    /**
     * Unregister an agent from the monitor
     * @param agent Agent to unregister
     */
    unregisterAgent(agent: BaseAgent): void {
        this.agentManagerService.unregisterAgent(agent);
    }

    /**
     * Track a language model request
     * @param modelName Model name
     * @param prompt Prompt text
     * @param options Request options
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Request ID
     */
    trackRequest(
        modelName: string,
        prompt: string,
        options: any,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        const id = `req-${Date.now()}-${this.nextId++}`;

        const request: LLMRequest = {
            id,
            timestamp: Date.now(),
            modelName,
            prompt,
            options,
            agentId,
            metadata
        };

        this.requests.push(request);

        // Trim history if it exceeds the maximum size
        if (this.requests.length > this.options.maxHistoryItems!) {
            this.requests.shift();
        }

        // Emit event
        this.eventHandlerService.emitRequestEvent(request);

        if (this.options.debugMode) {
            console.log(`LLM request tracked: ${modelName}`, {
                id,
                agentId,
                promptLength: prompt.length
            });
        }

        return id;
    }

    /**
     * Track a language model response
     * @param requestId Request ID
     * @param modelName Model name
     * @param response Response text
     * @param latencyMs Latency in milliseconds
     * @param tokenCount Optional token count
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Response ID
     */
    trackResponse(
        requestId: string,
        modelName: string,
        response: string,
        latencyMs: number,
        tokenCount?: number,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        const id = `res-${Date.now()}-${this.nextId++}`;

        const llmResponse: LLMResponse = {
            id,
            requestId,
            timestamp: Date.now(),
            modelName,
            response,
            latencyMs,
            tokenCount,
            agentId,
            metadata
        };

        this.responses.push(llmResponse);

        // Trim history if it exceeds the maximum size
        if (this.responses.length > this.options.maxHistoryItems!) {
            this.responses.shift();
        }

        // Detect issues if enabled
        if (this.options.enableIssueDetection) {
            // Find the corresponding request
            const request = this.requests.find(req => req.id === requestId);

            // Detect issues with request context if available
            const newIssues = this.issueDetectionService.detectIssues(response, id, request);

            // Add issues to history
            for (const issue of newIssues) {
                this.issues.push(issue);
            }

            if (newIssues.length > 0) {
                // Trim history if it exceeds the maximum size
                if (this.issues.length > this.options.maxHistoryItems!) {
                    this.issues.shift();
                }

                // Apply interventions if enabled
                if (this.options.enableInterventions && request) {
                    const newInterventions = this.interventionService.applyInterventions(
                        newIssues,
                        request,
                        this.interventions
                    );

                    // Add interventions to history
                    for (const intervention of newInterventions) {
                        this.interventions.push(intervention);
                    }

                    // Trim history if it exceeds the maximum size
                    if (this.interventions.length > this.options.maxHistoryItems!) {
                        this.interventions.shift();
                    }
                }

                // Update status bar to show issues
                this.updateStatusBar(newIssues.length);

                // Emit issue events
                for (const issue of newIssues) {
                    this.eventHandlerService.emitIssueEvent(issue);
                }
            }
        }

        // Emit event
        this.eventHandlerService.emitResponseEvent(llmResponse);

        if (this.options.debugMode) {
            console.log(`LLM response tracked: ${modelName}, latency: ${latencyMs}ms`, {
                id,
                requestId,
                agentId,
                responseLength: response.length,
                tokenCount
            });
        }

        return id;
    }

    /**
     * Track an error in language model interaction
     * @param requestId Request ID
     * @param modelName Model name
     * @param error Error object
     * @param agentId Optional agent ID
     * @param metadata Optional metadata
     * @returns Error ID
     */
    trackError(
        requestId: string,
        modelName: string,
        error: Error,
        agentId?: string,
        metadata?: Record<string, any>
    ): string {
        const id = `err-${Date.now()}-${this.nextId++}`;

        const llmError: LLMError = {
            id,
            requestId,
            timestamp: Date.now(),
            modelName,
            error,
            agentId,
            metadata
        };

        this.errors.push(llmError);

        // Trim history if it exceeds the maximum size
        if (this.errors.length > this.options.maxHistoryItems!) {
            this.errors.shift();
        }

        // Emit event
        this.eventHandlerService.emitErrorEvent(llmError);

        console.error(`LLM error tracked: ${modelName}`, {
            id,
            requestId,
            agentId,
            error: error.message
        });

        return id;
    }

    /**
     * Get usage statistics
     * @returns Usage statistics
     */
    getUsageStats(): LLMUsageStats {
        return this.metricsService.calculateUsageStats(
            this.requests,
            this.responses,
            this.errors,
            this.issues
        );
    }

    /**
     * Get agent status
     * @returns Map of agent IDs to their status
     */
    getAgentStatus(): Map<string, AgentStatus> {
        return this.agentManagerService.getAgentStatus();
    }

    /**
     * Subscribe to monitor events
     * @param listener Event listener
     * @returns Disposable
     */
    onEvent(listener: (event: any) => void): vscode.Disposable {
        return this.eventHandlerService.onEvent(listener);
    }

    /**
     * Apply agent-specific intervention
     * @param agentId Agent ID
     * @param issueType Type of issue to address
     * @param description Description of the issue
     * @returns Intervention ID if applied, undefined otherwise
     */
    applyAgentIntervention(agentId: string, issueType: LLMIssueType, description: string): string | undefined {
        // Check if agent exists and interventions are enabled
        if (!this.agentManagerService.hasAgent(agentId) || !this.options.enableInterventions) {
            return undefined;
        }

        // Get previous interventions for this agent
        const previousInterventions = this.interventions.filter(
            int => this.issues.find(i => i.id === int.issueId)?.agentId === agentId
        );

        // Apply intervention
        const intervention = this.agentManagerService.applyAgentIntervention(
            agentId,
            issueType,
            description,
            previousInterventions
        );

        if (intervention) {
            // Add to history
            this.interventions.push(intervention);

            // Trim history if it exceeds the maximum size
            if (this.interventions.length > this.options.maxHistoryItems!) {
                this.interventions.shift();
            }

            return intervention.id;
        }

        return undefined;
    }

    /**
     * Get interventions for a specific issue
     * @param issueId Issue ID
     * @returns Array of interventions
     */
    getInterventionsForIssue(issueId: string): LLMIntervention[] {
        return this.interventions.filter(int => int.issueId === issueId);
    }

    /**
     * Get all interventions
     * @returns Array of all interventions
     */
    getAllInterventions(): LLMIntervention[] {
        return [...this.interventions];
    }

    /**
     * Get all issues
     * @returns Array of all issues
     */
    getAllIssues(): LLMIssue[] {
        return [...this.issues];
    }

    /**
     * Get agent-specific usage statistics
     * @param agentId Agent ID
     * @returns Agent-specific usage statistics
     */
    getAgentStats(agentId: string): LLMUsageStats | undefined {
        // Check if agent exists
        if (!this.agentManagerService.hasAgent(agentId)) {
            return undefined;
        }

        return this.metricsService.calculateAgentStats(
            agentId,
            this.requests,
            this.responses,
            this.errors,
            this.issues
        );
    }

    /**
     * Analyze agent behavior patterns
     * @param agentId Agent ID
     * @returns Analysis of agent behavior patterns
     */
    analyzeAgentBehavior(agentId: string): AgentBehaviorAnalysis {
        // Check if agent exists
        if (!this.agentManagerService.hasAgent(agentId)) {
            return { patterns: [], anomalies: [], recommendations: [] };
        }

        return this.metricsService.analyzeAgentBehavior(
            agentId,
            this.responses,
            this.issues,
            this.errors
        );
    }

    /**
     * Update the status bar with issue count
     * @param issueCount Number of issues
     */
    private updateStatusBar(issueCount: number): void {
        if (issueCount > 0) {
            this.statusBarItem.text = `$(alert) LLM Monitor: ${issueCount} issue${issueCount > 1 ? 's' : ''}`;
            this.statusBarItem.tooltip = `${issueCount} LLM issue${issueCount > 1 ? 's' : ''} detected`;

            // Reset after a delay
            setTimeout(() => {
                this.statusBarItem.text = '$(eye) LLM Monitor';
                this.statusBarItem.tooltip = 'LLM Monitor is active';
            }, 10000);
        }
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        // Save data if persistence is enabled
        if (this.options.enablePersistence && this.options.persistencePath) {
            this.persistenceService.saveData(
                this.requests,
                this.responses,
                this.errors,
                this.issues,
                this.interventions
            );
        }

        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Dispose of services
        this.eventHandlerService.dispose();
        this.agentManagerService.clear();

        // Clear data
        this.requests = [];
        this.responses = [];
        this.errors = [];
        this.issues = [];
        this.interventions = [];

        console.log('LLM Monitor disposed');
    }
}
