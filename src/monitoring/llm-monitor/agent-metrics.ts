/**
 * Agent-specific Metrics Calculator for LLM Monitor
 * 
 * Handles calculation of agent-specific metrics and behavior analysis.
 */

import { LLMRequest, LLMResponse, LLMError, LLMIssue, LLMUsageStats, LLMIssueType, AgentBehaviorAnalysis } from './types.js';
import { TimeStatsService } from './time-stats.js';

/**
 * Agent metrics calculator service
 */
export class AgentMetricsService {
    private timeStatsService: TimeStatsService;

    constructor() {
        this.timeStatsService = new TimeStatsService();
    }

    /**
     * Calculate agent-specific usage statistics
     * @param agentId Agent ID
     * @param requests All requests
     * @param responses All responses
     * @param errors All errors
     * @param issues All issues
     * @returns Agent-specific usage statistics
     */
    calculateAgentStats(
        agentId: string,
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[],
        issues: LLMIssue[]
    ): LLMUsageStats {
        // Filter data for this agent
        const agentRequests = requests.filter(req => req.agentId === agentId);
        const agentResponses = responses.filter(res => res.agentId === agentId);
        const agentErrors = errors.filter(err => err.agentId === agentId);
        const agentIssues = issues.filter(issue => {
            const response = responses.find(res => res.id === issue.responseId);
            return response && response.agentId === agentId;
        });

        // Calculate basic stats
        const totalRequests = agentRequests.length;
        const totalResponses = agentResponses.length;
        const totalErrors = agentErrors.length;
        const totalTokens = agentResponses.reduce((sum, res) => sum + (res.tokenCount || 0), 0);
        const totalLatency = agentResponses.reduce((sum, res) => sum + res.latencyMs, 0);
        const averageLatencyMs = totalResponses > 0 ? totalLatency / totalResponses : 0;
        const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;
        const issueRate = totalResponses > 0 ? agentIssues.length / totalResponses : 0;

        // Calculate model-specific stats
        const modelStats = this.calculateModelStats(agentRequests, agentResponses, agentErrors);

        // Calculate time-based stats
        const timeStats = this.timeStatsService.calculateAgentTimeStats(
            agentId,
            requests,
            responses,
            errors
        );

        return {
            totalRequests,
            totalResponses,
            totalErrors,
            totalTokens,
            averageLatencyMs,
            errorRate,
            issueRate,
            modelStats,
            timeStats
        };
    }

    /**
     * Calculate model-specific statistics for an agent
     * @param requests Agent requests
     * @param responses Agent responses
     * @param errors Agent errors
     * @returns Model-specific statistics
     */
    private calculateModelStats(
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[]
    ): Record<string, any> {
        const modelStats: Record<string, any> = {};
        const modelNames = [...new Set(requests.map(req => req.modelName))];

        for (const modelName of modelNames) {
            const modelRequests = requests.filter(req => req.modelName === modelName);
            const modelResponses = responses.filter(res => res.modelName === modelName);
            const modelErrors = errors.filter(err => err.modelName === modelName);

            modelStats[modelName] = {
                requests: modelRequests.length,
                responses: modelResponses.length,
                errors: modelErrors.length,
                tokens: modelResponses.reduce((sum, res) => sum + (res.tokenCount || 0), 0),
                averageLatencyMs: modelResponses.length > 0
                    ? modelResponses.reduce((sum, res) => sum + res.latencyMs, 0) / modelResponses.length
                    : 0,
                errorRate: modelRequests.length > 0 ? modelErrors.length / modelRequests.length : 0
            };
        }

        return modelStats;
    }

    /**
     * Analyze agent behavior patterns
     * @param agentId Agent ID
     * @param responses All responses
     * @param issues All issues
     * @param errors All errors
     * @returns Analysis of agent behavior patterns
     */
    analyzeAgentBehavior(
        agentId: string,
        responses: LLMResponse[],
        issues: LLMIssue[],
        errors: LLMError[]
    ): AgentBehaviorAnalysis {
        const patterns: string[] = [];
        const anomalies: string[] = [];
        const recommendations: string[] = [];

        // Get agent data
        const agentResponses = responses.filter(res => res.agentId === agentId);
        const agentIssues = issues.filter(issue => {
            const response = responses.find(res => res.id === issue.responseId);
            return response && response.agentId === agentId;
        });
        const agentErrors = errors.filter(err => err.agentId === agentId);

        // Check for repetitive patterns
        if (agentResponses.length >= 3) {
            const responseTexts = agentResponses.map(res => res.response);
            const loopDetected = this.detectLoops(responseTexts);

            if (loopDetected) {
                patterns.push('Agent shows repetitive response patterns');
                anomalies.push('Agent may be stuck in a loop');
                recommendations.push('Consider resetting agent state or providing clearer instructions');
            }
        }

        // Check for hallucination patterns
        const hallucinationIssues = agentIssues.filter(
            issue => issue.type === LLMIssueType.HALLUCINATION || issue.type === LLMIssueType.FACTUAL_ERROR
        );

        if (hallucinationIssues.length > 0) {
            patterns.push(`Agent has ${hallucinationIssues.length} hallucination issues`);

            if (hallucinationIssues.length >= 3) {
                anomalies.push('Agent shows a pattern of hallucinations');
                recommendations.push('Consider using a larger model or providing more context');
            }
        }

        // Check for error patterns
        if (agentErrors.length > 0) {
            patterns.push(`Agent has ${agentErrors.length} errors`);

            if (agentErrors.length >= 3) {
                anomalies.push('Agent shows a pattern of errors');
                recommendations.push('Check agent configuration and model compatibility');
            }
        }

        // Check for latency patterns
        if (agentResponses.length > 0) {
            const latencies = agentResponses.map(res => res.latencyMs);
            const avgLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
            const maxLatency = Math.max(...latencies);

            patterns.push(`Average response time: ${avgLatency.toFixed(0)}ms`);

            if (maxLatency > avgLatency * 3) {
                anomalies.push('Agent has occasional very slow responses');
                recommendations.push('Consider optimizing prompts or using a faster model for time-sensitive tasks');
            }
        }

        // Check for token usage patterns
        if (agentResponses.length > 0) {
            const tokenCounts = agentResponses.map(res => res.tokenCount || 0);
            const avgTokens = tokenCounts.reduce((sum, count) => sum + count, 0) / tokenCounts.length;
            const maxTokens = Math.max(...tokenCounts);

            patterns.push(`Average token usage: ${avgTokens.toFixed(0)} tokens`);

            if (maxTokens > avgTokens * 5) {
                anomalies.push('Agent has occasional very high token usage');
                recommendations.push('Consider implementing token usage limits or optimizing prompts');
            }
        }

        // Check for issue type patterns
        const issueTypes = agentIssues.map(issue => issue.type);
        const issueTypeCounts = this.countIssueTypes(issueTypes);

        for (const [issueType, count] of Object.entries(issueTypeCounts)) {
            if (count >= 2) {
                patterns.push(`Agent has ${count} ${issueType} issues`);

                if (count >= 3) {
                    anomalies.push(`Agent shows a pattern of ${issueType} issues`);
                    recommendations.push(this.getRecommendationForIssueType(issueType as LLMIssueType));
                }
            }
        }

        return { patterns, anomalies, recommendations };
    }

    /**
     * Detect loops in response texts
     * @param responseTexts Array of response texts
     * @returns True if loops are detected
     */
    private detectLoops(responseTexts: string[]): boolean {
        if (responseTexts.length < 3) {
            return false;
        }

        // Check for exact repetitions
        for (let i = 0; i < responseTexts.length - 2; i++) {
            if (responseTexts[i] === responseTexts[i + 1] && responseTexts[i] === responseTexts[i + 2]) {
                return true;
            }
        }

        // Check for similar patterns (simplified)
        const similarities = [];
        for (let i = 0; i < responseTexts.length - 1; i++) {
            const similarity = this.calculateSimilarity(responseTexts[i], responseTexts[i + 1]);
            similarities.push(similarity);
        }

        // If multiple consecutive responses are very similar, it might be a loop
        const highSimilarityCount = similarities.filter(sim => sim > 0.8).length;
        return highSimilarityCount >= 2;
    }

    /**
     * Calculate similarity between two strings
     * @param str1 First string
     * @param str2 Second string
     * @returns Similarity score (0-1)
     */
    private calculateSimilarity(str1: string, str2: string): number {
        if (str1 === str2) return 1;
        if (str1.length === 0 || str2.length === 0) return 0;

        // Simple character-based similarity
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;

        if (longer.length === 0) return 1;

        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    /**
     * Calculate Levenshtein distance between two strings
     * @param str1 First string
     * @param str2 Second string
     * @returns Edit distance
     */
    private levenshteinDistance(str1: string, str2: string): number {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    /**
     * Count issue types
     * @param issueTypes Array of issue types
     * @returns Count of each issue type
     */
    private countIssueTypes(issueTypes: LLMIssueType[]): Record<string, number> {
        const counts: Record<string, number> = {};

        for (const issueType of issueTypes) {
            counts[issueType] = (counts[issueType] || 0) + 1;
        }

        return counts;
    }

    /**
     * Get recommendation for a specific issue type
     * @param issueType Issue type
     * @returns Recommendation string
     */
    private getRecommendationForIssueType(issueType: LLMIssueType): string {
        switch (issueType) {
            case LLMIssueType.HALLUCINATION:
                return 'Use fact-checking mechanisms and provide more reliable sources';
            case LLMIssueType.FACTUAL_ERROR:
                return 'Implement verification steps and use authoritative data sources';
            case LLMIssueType.REPETITION:
                return 'Add variety to prompts and implement repetition detection';
            case LLMIssueType.LOOP_DETECTED:
                return 'Implement loop breaking mechanisms and state management';
            case LLMIssueType.INCOMPLETE:
                return 'Increase context window or break down complex tasks';
            case LLMIssueType.FORMAT_ERROR:
                return 'Provide clearer format specifications and examples';
            case LLMIssueType.CONTEXT_WINDOW_EXCEEDED:
                return 'Implement context management and summarization techniques';
            case LLMIssueType.TOKEN_LIMIT_EXCEEDED:
                return 'Optimize prompts and implement token budgeting';
            default:
                return 'Monitor agent behavior and adjust configuration as needed';
        }
    }
}
