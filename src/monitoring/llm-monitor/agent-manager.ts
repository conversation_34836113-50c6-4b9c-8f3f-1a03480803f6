/**
 * Agent Manager for LLM Monitor
 * 
 * Manages agent registration and provides agent-related functionality.
 */

import { BaseAgent, AgentStatus } from '../../agents/base-agent.js';
import { LLMRequest, LLMResponse, LLMError, LLMIssue, LLMIntervention, LLMUsageStats, LLMIssueType, AgentBehaviorAnalysis, LLMMonitorOptions } from './types.js';
import { MetricsService } from './metrics.js';
import { InterventionService } from './interventions.js';

/**
 * Agent manager service for LLM Monitor
 */
export class AgentManagerService {
    private agents: Map<string, BaseAgent> = new Map();
    private metricsService: MetricsService;
    private interventionService: InterventionService;
    private options: LLMMonitorOptions;

    constructor(
        metricsService: MetricsService,
        interventionService: InterventionService,
        options: LLMMonitorOptions
    ) {
        this.metricsService = metricsService;
        this.interventionService = interventionService;
        this.options = options;
    }

    /**
     * Register an agent with the monitor
     * @param agent Agent to register
     */
    registerAgent(agent: BaseAgent): void {
        this.agents.set(agent.getId(), agent);

        if (this.options.debugMode) {
            console.log(`Agent registered with LLM Monitor: ${agent.getId()} (${agent.getName()})`);
        }
    }

    /**
     * Unregister an agent from the monitor
     * @param agent Agent to unregister
     */
    unregisterAgent(agent: BaseAgent): void {
        this.agents.delete(agent.getId());

        if (this.options.debugMode) {
            console.log(`Agent unregistered from LLM Monitor: ${agent.getId()} (${agent.getName()})`);
        }
    }

    /**
     * Check if an agent is registered
     * @param agentId Agent ID
     * @returns True if agent is registered
     */
    hasAgent(agentId: string): boolean {
        return this.agents.has(agentId);
    }

    /**
     * Get an agent by ID
     * @param agentId Agent ID
     * @returns Agent or undefined if not found
     */
    getAgent(agentId: string): BaseAgent | undefined {
        return this.agents.get(agentId);
    }

    /**
     * Get all registered agents
     * @returns Map of agent IDs to agents
     */
    getAllAgents(): Map<string, BaseAgent> {
        return new Map(this.agents);
    }

    /**
     * Get agent status
     * @returns Map of agent IDs to their status
     */
    getAgentStatus(): Map<string, AgentStatus> {
        const agentStatus = new Map<string, AgentStatus>();

        for (const [id, agent] of this.agents) {
            agentStatus.set(id, agent.getStatus());
        }

        return agentStatus;
    }

    /**
     * Get agent-specific usage statistics
     * @param agentId Agent ID
     * @param requests All requests
     * @param responses All responses
     * @param errors All errors
     * @param issues All issues
     * @returns Agent-specific usage statistics
     */
    getAgentStats(
        agentId: string,
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[],
        issues: LLMIssue[]
    ): LLMUsageStats | undefined {
        // Check if agent exists
        if (!this.agents.has(agentId)) {
            return undefined;
        }

        return this.metricsService.calculateAgentStats(
            agentId,
            requests,
            responses,
            errors,
            issues
        );
    }

    /**
     * Analyze agent behavior patterns
     * @param agentId Agent ID
     * @param responses All responses
     * @param issues All issues
     * @param errors All errors
     * @returns Analysis of agent behavior patterns
     */
    analyzeAgentBehavior(
        agentId: string,
        responses: LLMResponse[],
        issues: LLMIssue[],
        errors: LLMError[]
    ): AgentBehaviorAnalysis {
        // Check if agent exists
        if (!this.agents.has(agentId)) {
            return { patterns: [], anomalies: [], recommendations: [] };
        }

        return this.metricsService.analyzeAgentBehavior(
            agentId,
            responses,
            issues,
            errors
        );
    }

    /**
     * Apply agent-specific intervention
     * @param agentId Agent ID
     * @param issueType Type of issue to address
     * @param description Description of the issue
     * @param previousInterventions Previous interventions for this agent
     * @returns Intervention if applied, undefined otherwise
     */
    applyAgentIntervention(
        agentId: string,
        issueType: LLMIssueType,
        description: string,
        previousInterventions: LLMIntervention[]
    ): LLMIntervention | undefined {
        // Check if agent exists
        if (!this.agents.has(agentId)) {
            return undefined;
        }

        return this.interventionService.applyAgentIntervention(
            agentId,
            issueType,
            description,
            previousInterventions
        );
    }

    /**
     * Clear all agents
     */
    clear(): void {
        this.agents.clear();
    }

    /**
     * Get the number of registered agents
     * @returns Number of registered agents
     */
    getAgentCount(): number {
        return this.agents.size;
    }
}
