/**
 * LLM Monitor Persistence
 * 
 * This module provides functionality for persisting LLM monitor data.
 */

import * as fs from 'fs';
import * as path from 'path';
import { 
    LLMRequest, 
    LLMResponse, 
    LLMError, 
    LLMIssue, 
    LLMIntervention,
    LLMMonitorOptions 
} from './types.js';

/**
 * Persistence Service
 */
export class PersistenceService {
    private options: LLMMonitorOptions;

    constructor(options: LLMMonitorOptions) {
        this.options = options;
    }

    /**
     * Load persisted data
     * @returns Object containing loaded data
     */
    loadPersistedData(): {
        requests: LLMRequest[];
        responses: LLMResponse[];
        errors: LLMError[];
        issues: LLMIssue[];
        interventions: LLMIntervention[];
    } {
        if (!this.options.enablePersistence || !this.options.persistencePath) {
            return {
                requests: [],
                responses: [],
                errors: [],
                issues: [],
                interventions: []
            };
        }

        try {
            if (this.options.debugMode) {
                console.log('Loading persisted LLM Monitor data from:', this.options.persistencePath);
            }

            // Ensure directory exists
            const directory = path.dirname(this.options.persistencePath);
            if (!fs.existsSync(directory)) {
                fs.mkdirSync(directory, { recursive: true });
                return {
                    requests: [],
                    responses: [],
                    errors: [],
                    issues: [],
                    interventions: []
                };
            }

            // Check if file exists
            if (!fs.existsSync(this.options.persistencePath)) {
                return {
                    requests: [],
                    responses: [],
                    errors: [],
                    issues: [],
                    interventions: []
                };
            }

            // Read and parse data
            const data = fs.readFileSync(this.options.persistencePath, 'utf-8');
            const parsedData = JSON.parse(data);

            return {
                requests: parsedData.requests || [],
                responses: parsedData.responses || [],
                errors: parsedData.errors || [],
                issues: parsedData.issues || [],
                interventions: parsedData.interventions || []
            };
        } catch (error) {
            console.error('Error loading persisted LLM Monitor data:', error);
            return {
                requests: [],
                responses: [],
                errors: [],
                issues: [],
                interventions: []
            };
        }
    }

    /**
     * Save data to persistence
     * @param requests Array of LLM requests
     * @param responses Array of LLM responses
     * @param errors Array of LLM errors
     * @param issues Array of LLM issues
     * @param interventions Array of LLM interventions
     */
    saveData(
        requests: LLMRequest[],
        responses: LLMResponse[],
        errors: LLMError[],
        issues: LLMIssue[],
        interventions: LLMIntervention[]
    ): void {
        if (!this.options.enablePersistence || !this.options.persistencePath) {
            return;
        }

        try {
            if (this.options.debugMode) {
                console.log('Saving LLM Monitor data to:', this.options.persistencePath);
            }

            // Ensure directory exists
            const directory = path.dirname(this.options.persistencePath);
            if (!fs.existsSync(directory)) {
                fs.mkdirSync(directory, { recursive: true });
            }

            // Prepare data
            const data = {
                requests,
                responses,
                errors,
                issues,
                interventions,
                timestamp: Date.now()
            };

            // Write data
            fs.writeFileSync(
                this.options.persistencePath,
                JSON.stringify(data, null, 2),
                'utf-8'
            );
        } catch (error) {
            console.error('Error saving LLM Monitor data:', error);
        }
    }
}
