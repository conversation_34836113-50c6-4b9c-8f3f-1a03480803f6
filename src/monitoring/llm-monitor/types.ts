/**
 * LLM Monitor Types
 * 
 * This module defines the types and interfaces used by the LLM Monitor.
 */

import { BaseAgent } from '../../agents/base-agent.js';

/**
 * LLM Request
 */
export interface LLMRequest {
    id: string;
    timestamp: number;
    modelName: string;
    prompt: string;
    options: any;
    agentId?: string;
    metadata?: Record<string, any>;
}

/**
 * LLM Response
 */
export interface LLMResponse {
    id: string;
    requestId: string;
    timestamp: number;
    modelName: string;
    response: string;
    latencyMs: number;
    tokenCount?: number;
    agentId?: string;
    metadata?: Record<string, any>;
}

/**
 * LLM Error
 */
export interface LLMError {
    id: string;
    requestId: string;
    timestamp: number;
    modelName: string;
    error: Error;
    agentId?: string;
    metadata?: Record<string, any>;
}

/**
 * LLM Issue Type
 */
export enum LLMIssueType {
    HALLUCINATION = 'hallucination',
    REPETITION = 'repetition',
    CONTRADICTION = 'contradiction',
    INCOMPLETE = 'incomplete',
    IRRELEVANT = 'irrelevant',
    HARMFUL = 'harmful',
    CONTEXT_WINDOW_EXCEEDED = 'context_window_exceeded',
    TOKEN_LIMIT_EXCEEDED = 'token_limit_exceeded',
    LOOP_DETECTED = 'loop_detected',
    FORMAT_ERROR = 'format_error',
    FACTUAL_ERROR = 'factual_error'
}

/**
 * LLM Issue
 */
export interface LLMIssue {
    id: string;
    responseId: string;
    timestamp: number;
    type: LLMIssueType;
    description: string;
    severity: 'low' | 'medium' | 'high';
    confidence: number;
    metadata?: Record<string, any>;
    agentId?: string;
}

/**
 * LLM Usage Statistics
 */
export interface LLMUsageStats {
    totalRequests: number;
    totalResponses: number;
    totalErrors: number;
    totalTokens: number;
    averageLatencyMs: number;
    errorRate: number;
    issueRate: number;
    modelStats: Record<string, {
        requests: number;
        responses: number;
        errors: number;
        tokens: number;
        averageLatencyMs: number;
        errorRate: number;
        issueRate: number;
    }>;
    timeStats: {
        last24Hours: {
            requests: number;
            responses: number;
            errors: number;
            tokens: number;
        };
        last7Days: {
            requests: number;
            responses: number;
            errors: number;
            tokens: number;
        };
        last30Days: {
            requests: number;
            responses: number;
            errors: number;
            tokens: number;
        };
    };
}

/**
 * LLM Intervention Strategy
 */
export enum InterventionStrategy {
    NONE = 'none',
    RETRY = 'retry',
    REFINE_PROMPT = 'refine_prompt',
    SWITCH_MODEL = 'switch_model',
    REDUCE_COMPLEXITY = 'reduce_complexity',
    HUMAN_ASSISTANCE = 'human_assistance',
    FALLBACK = 'fallback'
}

/**
 * LLM Intervention
 */
export interface LLMIntervention {
    id: string;
    issueId: string;
    timestamp: number;
    strategy: InterventionStrategy;
    description: string;
    result?: 'success' | 'failure';
    metadata?: Record<string, any>;
}

/**
 * LLM Monitor Options
 */
export interface LLMMonitorOptions {
    maxHistoryItems?: number;
    enableIssueDetection?: boolean;
    issueDetectionConfidenceThreshold?: number;
    enableTokenCounting?: boolean;
    enablePersistence?: boolean;
    persistencePath?: string;
    debugMode?: boolean;
    enableInterventions?: boolean;
    interventionStrategies?: InterventionStrategy[];
    maxRetries?: number;
    smallModelThreshold?: number; // Token limit below which a model is considered "small"
}

/**
 * Agent Behavior Analysis
 */
export interface AgentBehaviorAnalysis {
    patterns: string[];
    anomalies: string[];
    recommendations: string[];
}
