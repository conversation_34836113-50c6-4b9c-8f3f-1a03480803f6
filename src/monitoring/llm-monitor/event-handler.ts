/**
 * Event Handler for LLM Monitor
 * 
 * Handles event emission and subscription for the LLM Monitor.
 */

import * as vscode from 'vscode';
import { LLMRequest, LLMResponse, LLMError, LLMIssue, LLMIntervention } from './types.js';

export interface LLMMonitorEvent {
    type: 'request' | 'response' | 'error' | 'issue' | 'intervention' | 'agent_intervention';
    request?: LLMRequest;
    response?: LLMResponse;
    error?: LLMError;
    issue?: LLMIssue;
    intervention?: LLMIntervention;
    agentId?: string;
}

/**
 * Event handler service for LLM Monitor
 */
export class EventHandlerService {
    private eventEmitter: vscode.EventEmitter<LLMMonitorEvent>;
    private disposables: vscode.Disposable[] = [];

    constructor() {
        this.eventEmitter = new vscode.EventEmitter<LLMMonitorEvent>();
        this.disposables.push(this.eventEmitter);
    }

    /**
     * Emit a request event
     * @param request The request that was tracked
     */
    emitRequestEvent(request: LLMRequest): void {
        this.eventEmitter.fire({
            type: 'request',
            request
        });
    }

    /**
     * Emit a response event
     * @param response The response that was tracked
     */
    emitResponseEvent(response: LLMResponse): void {
        this.eventEmitter.fire({
            type: 'response',
            response
        });
    }

    /**
     * Emit an error event
     * @param error The error that was tracked
     */
    emitErrorEvent(error: LLMError): void {
        this.eventEmitter.fire({
            type: 'error',
            error
        });
    }

    /**
     * Emit an issue event
     * @param issue The issue that was detected
     */
    emitIssueEvent(issue: LLMIssue): void {
        this.eventEmitter.fire({
            type: 'issue',
            issue
        });
    }

    /**
     * Emit an intervention event
     * @param intervention The intervention that was applied
     * @param issue The issue that triggered the intervention
     */
    emitInterventionEvent(intervention: LLMIntervention, issue: LLMIssue): void {
        this.eventEmitter.fire({
            type: 'intervention',
            intervention,
            issue
        });
    }

    /**
     * Emit an agent intervention event
     * @param agentId The agent ID
     * @param intervention The intervention that was applied
     * @param issue The issue that triggered the intervention
     */
    emitAgentInterventionEvent(agentId: string, intervention: LLMIntervention, issue: LLMIssue): void {
        this.eventEmitter.fire({
            type: 'agent_intervention',
            agentId,
            intervention,
            issue
        });
    }

    /**
     * Subscribe to monitor events
     * @param listener Event listener
     * @returns Disposable
     */
    onEvent(listener: (event: LLMMonitorEvent) => void): vscode.Disposable {
        return this.eventEmitter.event(listener);
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
}
