/**
 * Buffer Manager for Terminal Monitor
 * 
 * Manages terminal output buffers and provides efficient text processing.
 */

export interface BufferEntry {
    timestamp: number;
    content: string;
    terminalId: number;
    lineNumber: number;
}

export interface BufferStats {
    totalLines: number;
    totalSize: number;
    oldestEntry: number;
    newestEntry: number;
    terminalCount: number;
}

/**
 * Buffer manager service for terminal monitoring
 */
export class BufferManagerService {
    private buffers: Map<number, BufferEntry[]> = new Map();
    private maxBufferSize: number;
    private maxLineLength: number;
    private retentionTime: number; // in milliseconds

    constructor(
        maxBufferSize: number = 10000,
        maxLineLength: number = 1000,
        retentionTime: number = 24 * 60 * 60 * 1000 // 24 hours
    ) {
        this.maxBufferSize = maxBufferSize;
        this.maxLineLength = maxLineLength;
        this.retentionTime = retentionTime;

        // Start cleanup timer
        this.startCleanupTimer();
    }

    /**
     * Add content to a terminal buffer
     * @param terminalId Terminal ID
     * @param content Content to add
     */
    addToBuffer(terminalId: number, content: string): void {
        if (!this.buffers.has(terminalId)) {
            this.buffers.set(terminalId, []);
        }

        const buffer = this.buffers.get(terminalId)!;
        const lines = content.split('\n');
        const timestamp = Date.now();

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.trim().length === 0 && i === lines.length - 1) {
                continue; // Skip empty last line
            }

            // Truncate long lines
            const truncatedLine = line.length > this.maxLineLength 
                ? line.substring(0, this.maxLineLength) + '...'
                : line;

            const entry: BufferEntry = {
                timestamp,
                content: truncatedLine,
                terminalId,
                lineNumber: buffer.length
            };

            buffer.push(entry);

            // Trim buffer if it exceeds max size
            if (buffer.length > this.maxBufferSize) {
                buffer.shift();
                // Update line numbers
                buffer.forEach((entry, index) => {
                    entry.lineNumber = index;
                });
            }
        }
    }

    /**
     * Get buffer content for a terminal
     * @param terminalId Terminal ID
     * @param maxLines Maximum number of lines to return
     * @returns Array of buffer entries
     */
    getBuffer(terminalId: number, maxLines?: number): BufferEntry[] {
        const buffer = this.buffers.get(terminalId) || [];
        
        if (maxLines && maxLines > 0) {
            return buffer.slice(-maxLines);
        }
        
        return [...buffer];
    }

    /**
     * Get recent content from a terminal
     * @param terminalId Terminal ID
     * @param timeWindow Time window in milliseconds
     * @returns Array of buffer entries
     */
    getRecentContent(terminalId: number, timeWindow: number): BufferEntry[] {
        const buffer = this.buffers.get(terminalId) || [];
        const cutoffTime = Date.now() - timeWindow;
        
        return buffer.filter(entry => entry.timestamp >= cutoffTime);
    }

    /**
     * Search for content in a terminal buffer
     * @param terminalId Terminal ID
     * @param searchTerm Search term (string or regex)
     * @param maxResults Maximum number of results
     * @returns Array of matching buffer entries
     */
    searchBuffer(terminalId: number, searchTerm: string | RegExp, maxResults: number = 100): BufferEntry[] {
        const buffer = this.buffers.get(terminalId) || [];
        const results: BufferEntry[] = [];
        
        const regex = typeof searchTerm === 'string' 
            ? new RegExp(searchTerm, 'gi')
            : searchTerm;

        for (const entry of buffer) {
            if (regex.test(entry.content)) {
                results.push(entry);
                if (results.length >= maxResults) {
                    break;
                }
            }
        }

        return results;
    }

    /**
     * Get all content from a terminal as a single string
     * @param terminalId Terminal ID
     * @param separator Line separator
     * @returns Combined content
     */
    getAllContent(terminalId: number, separator: string = '\n'): string {
        const buffer = this.buffers.get(terminalId) || [];
        return buffer.map(entry => entry.content).join(separator);
    }

    /**
     * Clear buffer for a terminal
     * @param terminalId Terminal ID
     */
    clearBuffer(terminalId: number): void {
        this.buffers.delete(terminalId);
    }

    /**
     * Clear all buffers
     */
    clearAllBuffers(): void {
        this.buffers.clear();
    }

    /**
     * Get buffer statistics
     * @returns Buffer statistics
     */
    getBufferStats(): BufferStats {
        let totalLines = 0;
        let totalSize = 0;
        let oldestEntry = Date.now();
        let newestEntry = 0;

        for (const buffer of this.buffers.values()) {
            totalLines += buffer.length;
            
            for (const entry of buffer) {
                totalSize += entry.content.length;
                oldestEntry = Math.min(oldestEntry, entry.timestamp);
                newestEntry = Math.max(newestEntry, entry.timestamp);
            }
        }

        return {
            totalLines,
            totalSize,
            oldestEntry: oldestEntry === Date.now() ? 0 : oldestEntry,
            newestEntry,
            terminalCount: this.buffers.size
        };
    }

    /**
     * Get terminal IDs with active buffers
     * @returns Array of terminal IDs
     */
    getActiveTerminals(): number[] {
        return Array.from(this.buffers.keys());
    }

    /**
     * Check if a terminal has a buffer
     * @param terminalId Terminal ID
     * @returns True if terminal has a buffer
     */
    hasBuffer(terminalId: number): boolean {
        return this.buffers.has(terminalId);
    }

    /**
     * Get buffer size for a terminal
     * @param terminalId Terminal ID
     * @returns Number of entries in buffer
     */
    getBufferSize(terminalId: number): number {
        const buffer = this.buffers.get(terminalId);
        return buffer ? buffer.length : 0;
    }

    /**
     * Start cleanup timer to remove old entries
     */
    private startCleanupTimer(): void {
        setInterval(() => {
            this.cleanupOldEntries();
        }, 60000); // Run every minute
    }

    /**
     * Remove old entries from all buffers
     */
    private cleanupOldEntries(): void {
        const cutoffTime = Date.now() - this.retentionTime;

        for (const [terminalId, buffer] of this.buffers) {
            const filteredBuffer = buffer.filter(entry => entry.timestamp >= cutoffTime);
            
            if (filteredBuffer.length !== buffer.length) {
                // Update line numbers after cleanup
                filteredBuffer.forEach((entry, index) => {
                    entry.lineNumber = index;
                });
                
                this.buffers.set(terminalId, filteredBuffer);
            }

            // Remove empty buffers
            if (filteredBuffer.length === 0) {
                this.buffers.delete(terminalId);
            }
        }
    }

    /**
     * Set buffer configuration
     * @param maxBufferSize Maximum buffer size
     * @param maxLineLength Maximum line length
     * @param retentionTime Retention time in milliseconds
     */
    setConfiguration(maxBufferSize: number, maxLineLength: number, retentionTime: number): void {
        this.maxBufferSize = maxBufferSize;
        this.maxLineLength = maxLineLength;
        this.retentionTime = retentionTime;
    }

    /**
     * Get buffer configuration
     * @returns Buffer configuration
     */
    getConfiguration(): { maxBufferSize: number; maxLineLength: number; retentionTime: number } {
        return {
            maxBufferSize: this.maxBufferSize,
            maxLineLength: this.maxLineLength,
            retentionTime: this.retentionTime
        };
    }
}
