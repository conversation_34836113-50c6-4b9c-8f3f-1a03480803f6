/**
 * Prompt Builder Service for Code Generation
 * 
 * Provides utilities for building prompts for code generation.
 */

import { CodeGenerationRequest } from '../code-generation-agent.js';
import { getLanguageDisplayName, getLanguageFeatures } from './language-detector.js';

/**
 * Create a base prompt for code generation
 */
export function createBasePrompt(request: CodeGenerationRequest): string {
    let prompt = `Generate ${getLanguageDisplayName(request.language!)} code based on the following requirements:\n\n${request.requirements}\n\n`;

    if (request.framework) {
        prompt += `Use the ${request.framework} framework.\n\n`;
    }

    // Add language-specific guidance
    const features = getLanguageFeatures(request.language!);
    if (features.isStaticallyTyped) {
        prompt += 'Include proper type annotations and ensure type safety.\n';
    }

    if (features.supportsAsync) {
        prompt += 'Use async/await patterns where appropriate for asynchronous operations.\n';
    }

    if (features.supportsClasses && features.supportsInterfaces) {
        prompt += 'Follow object-oriented principles and use interfaces where appropriate.\n';
    }

    prompt += 'The code should be well-structured, efficient, and follow best practices.\n';

    if (request.includeTests) {
        prompt += 'Include unit tests for the code.\n';
    }

    if (request.includeDocumentation) {
        prompt += 'Include comprehensive documentation for the code.\n';
    }

    return prompt;
}

/**
 * Create a simple prompt without enhancement
 */
export function createSimplePrompt(
    request: CodeGenerationRequest,
    contextItems: any[]
): string {
    let prompt = createBasePrompt(request);

    // Add context items
    if (contextItems.length > 0) {
        prompt += '\nContext:\n';

        for (const item of contextItems) {
            prompt += `\n--- ${item.type.toUpperCase()} ---\n${item.content}\n`;
        }
    }

    // Add specific formatting instructions
    prompt += '\nPlease format your response as follows:\n';
    prompt += '1. Start with a brief explanation of the approach\n';
    prompt += '2. Provide the main code in a code block\n';
    
    if (request.includeTests) {
        prompt += '3. Include test code in a separate code block\n';
    }
    
    if (request.includeDocumentation) {
        prompt += '4. Include documentation or comments explaining the code\n';
    }

    return prompt;
}

/**
 * Create a prompt for test generation
 */
export function createTestPrompt(
    code: string,
    language: string,
    framework?: string
): string {
    const languageDisplay = getLanguageDisplayName(language);
    let prompt = `Generate comprehensive unit tests for the following ${languageDisplay} code:\n\n`;
    
    prompt += '```' + language + '\n' + code + '\n```\n\n';
    
    if (framework) {
        prompt += `Use the ${framework} testing framework.\n`;
    }
    
    prompt += 'The tests should:\n';
    prompt += '- Cover all public methods and functions\n';
    prompt += '- Include edge cases and error scenarios\n';
    prompt += '- Follow testing best practices\n';
    prompt += '- Be well-documented with clear test names\n';
    prompt += '- Include setup and teardown if needed\n\n';
    
    prompt += 'Please provide the test code in a code block.';
    
    return prompt;
}

/**
 * Create a prompt for documentation generation
 */
export function createDocumentationPrompt(
    code: string,
    language: string,
    includeExamples: boolean = true
): string {
    const languageDisplay = getLanguageDisplayName(language);
    let prompt = `Generate comprehensive documentation for the following ${languageDisplay} code:\n\n`;
    
    prompt += '```' + language + '\n' + code + '\n```\n\n';
    
    prompt += 'The documentation should include:\n';
    prompt += '- Overview of what the code does\n';
    prompt += '- Description of each function/method with parameters and return values\n';
    prompt += '- Description of each class and its properties\n';
    prompt += '- Any important implementation details\n';
    
    if (includeExamples) {
        prompt += '- Usage examples\n';
    }
    
    prompt += '- Any dependencies or requirements\n\n';
    
    prompt += 'Format the documentation using appropriate comment syntax for the language.';
    
    return prompt;
}

/**
 * Create a prompt for code refactoring
 */
export function createRefactoringPrompt(
    code: string,
    language: string,
    refactoringGoals: string[]
): string {
    const languageDisplay = getLanguageDisplayName(language);
    let prompt = `Refactor the following ${languageDisplay} code to improve:\n`;
    
    for (const goal of refactoringGoals) {
        prompt += `- ${goal}\n`;
    }
    
    prompt += '\nOriginal code:\n';
    prompt += '```' + language + '\n' + code + '\n```\n\n';
    
    prompt += 'Please provide:\n';
    prompt += '1. The refactored code\n';
    prompt += '2. Explanation of the changes made\n';
    prompt += '3. Benefits of the refactoring\n';
    
    return prompt;
}

/**
 * Create a prompt for code optimization
 */
export function createOptimizationPrompt(
    code: string,
    language: string,
    optimizationTargets: string[]
): string {
    const languageDisplay = getLanguageDisplayName(language);
    let prompt = `Optimize the following ${languageDisplay} code for:\n`;
    
    for (const target of optimizationTargets) {
        prompt += `- ${target}\n`;
    }
    
    prompt += '\nCode to optimize:\n';
    prompt += '```' + language + '\n' + code + '\n```\n\n';
    
    prompt += 'Please provide:\n';
    prompt += '1. The optimized code\n';
    prompt += '2. Explanation of optimizations applied\n';
    prompt += '3. Expected performance improvements\n';
    prompt += '4. Any trade-offs made\n';
    
    return prompt;
}

/**
 * Create a prompt for code review
 */
export function createCodeReviewPrompt(
    code: string,
    language: string,
    reviewCriteria: string[]
): string {
    const languageDisplay = getLanguageDisplayName(language);
    let prompt = `Review the following ${languageDisplay} code for:\n`;
    
    for (const criteria of reviewCriteria) {
        prompt += `- ${criteria}\n`;
    }
    
    prompt += '\nCode to review:\n';
    prompt += '```' + language + '\n' + code + '\n```\n\n';
    
    prompt += 'Please provide:\n';
    prompt += '1. Overall assessment of code quality\n';
    prompt += '2. Specific issues found (if any)\n';
    prompt += '3. Suggestions for improvement\n';
    prompt += '4. Positive aspects of the code\n';
    prompt += '5. Compliance with best practices\n';
    
    return prompt;
}

/**
 * Create a prompt for API documentation generation
 */
export function createApiDocumentationPrompt(
    code: string,
    language: string,
    format: 'markdown' | 'html' | 'json' = 'markdown'
): string {
    const languageDisplay = getLanguageDisplayName(language);
    let prompt = `Generate API documentation in ${format} format for the following ${languageDisplay} code:\n\n`;
    
    prompt += '```' + language + '\n' + code + '\n```\n\n';
    
    prompt += 'The API documentation should include:\n';
    prompt += '- API overview and purpose\n';
    prompt += '- All public methods/functions with:\n';
    prompt += '  - Description\n';
    prompt += '  - Parameters (name, type, description)\n';
    prompt += '  - Return value (type, description)\n';
    prompt += '  - Example usage\n';
    prompt += '  - Error conditions\n';
    prompt += '- Data models/interfaces\n';
    prompt += '- Authentication requirements (if applicable)\n';
    prompt += '- Rate limiting information (if applicable)\n';
    
    return prompt;
}

/**
 * Add context-specific instructions to a prompt
 */
export function addContextInstructions(
    prompt: string,
    context: {
        projectType?: string;
        targetAudience?: string;
        performanceRequirements?: string;
        securityRequirements?: string;
        scalabilityRequirements?: string;
    }
): string {
    let enhancedPrompt = prompt;

    if (context.projectType) {
        enhancedPrompt += `\nProject Type: ${context.projectType}\n`;
    }

    if (context.targetAudience) {
        enhancedPrompt += `Target Audience: ${context.targetAudience}\n`;
    }

    if (context.performanceRequirements) {
        enhancedPrompt += `Performance Requirements: ${context.performanceRequirements}\n`;
    }

    if (context.securityRequirements) {
        enhancedPrompt += `Security Requirements: ${context.securityRequirements}\n`;
    }

    if (context.scalabilityRequirements) {
        enhancedPrompt += `Scalability Requirements: ${context.scalabilityRequirements}\n`;
    }

    return enhancedPrompt;
}

/**
 * Create a prompt template for specific code patterns
 */
export function createPatternPrompt(
    pattern: string,
    language: string,
    requirements: string
): string {
    const languageDisplay = getLanguageDisplayName(language);
    
    const patterns: Record<string, string> = {
        'singleton': `Implement the Singleton design pattern in ${languageDisplay}`,
        'factory': `Implement the Factory design pattern in ${languageDisplay}`,
        'observer': `Implement the Observer design pattern in ${languageDisplay}`,
        'strategy': `Implement the Strategy design pattern in ${languageDisplay}`,
        'decorator': `Implement the Decorator design pattern in ${languageDisplay}`,
        'mvc': `Implement the Model-View-Controller (MVC) pattern in ${languageDisplay}`,
        'repository': `Implement the Repository pattern in ${languageDisplay}`,
        'dependency-injection': `Implement Dependency Injection in ${languageDisplay}`
    };

    let prompt = patterns[pattern.toLowerCase()] || `Implement the ${pattern} pattern in ${languageDisplay}`;
    prompt += ` based on the following requirements:\n\n${requirements}\n\n`;
    
    prompt += 'The implementation should:\n';
    prompt += '- Follow the pattern correctly\n';
    prompt += '- Be well-documented\n';
    prompt += '- Include usage examples\n';
    prompt += '- Follow best practices for the language\n';
    
    return prompt;
}
