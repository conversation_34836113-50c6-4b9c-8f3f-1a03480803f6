/**
 * Code Parser Service for Code Generation
 * 
 * Provides utilities for parsing and extracting information from generated code.
 */

import { getLanguagePatterns } from './language-detector.js';

export interface CodeBlock {
    language: string;
    code: string;
}

/**
 * Extract code blocks from the generated text
 */
export function extractCodeBlocks(text: string): CodeBlock[] {
    const codeBlockRegex = /```(\w*)\n([\s\S]*?)```/g;
    const codeBlocks: CodeBlock[] = [];

    let match;
    while ((match = codeBlockRegex.exec(text)) !== null) {
        codeBlocks.push({
            language: match[1].toLowerCase(),
            code: match[2].trim()
        });
    }

    // If no code blocks found, treat the entire text as code
    if (codeBlocks.length === 0) {
        codeBlocks.push({
            language: '',
            code: text.trim()
        });
    }

    return codeBlocks;
}

/**
 * Extract imports from the code
 */
export function extractImports(code: string, language: string): string[] {
    const imports: string[] = [];

    switch (language.toLowerCase()) {
        case 'typescript':
        case 'javascript':
            // Match import statements
            const jsImportRegex = /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"]([^'"]+)['"]/g;
            let jsMatch;
            while ((jsMatch = jsImportRegex.exec(code)) !== null) {
                imports.push(jsMatch[1]);
            }

            // Match require statements
            const requireRegex = /(?:const|let|var)\s+(?:{[^}]*}|\w+)\s+=\s+require\(['"]([^'"]+)['"]\)/g;
            let requireMatch;
            while ((requireMatch = requireRegex.exec(code)) !== null) {
                imports.push(requireMatch[1]);
            }
            break;

        case 'python':
            // Match import statements
            const pyImportRegex = /(?:import\s+(\w+)(?:\s+as\s+\w+)?|from\s+([^\s]+)\s+import\s+[^#\n]+)/g;
            let pyMatch;
            while ((pyMatch = pyImportRegex.exec(code)) !== null) {
                imports.push(pyMatch[1] || pyMatch[2]);
            }
            break;

        case 'java':
            // Match import statements
            const javaImportRegex = /import\s+([^;]+);/g;
            let javaMatch;
            while ((javaMatch = javaImportRegex.exec(code)) !== null) {
                imports.push(javaMatch[1]);
            }
            break;

        case 'csharp':
            // Match using statements
            const csImportRegex = /using\s+([^;]+);/g;
            let csMatch;
            while ((csMatch = csImportRegex.exec(code)) !== null) {
                imports.push(csMatch[1]);
            }
            break;

        case 'go':
            // Match import statements
            const goImportRegex = /import\s+(?:"([^"]+)"|`([^`]+)`)/g;
            let goMatch;
            while ((goMatch = goImportRegex.exec(code)) !== null) {
                imports.push(goMatch[1] || goMatch[2]);
            }
            break;

        case 'rust':
            // Match use statements
            const rustImportRegex = /use\s+([^;]+);/g;
            let rustMatch;
            while ((rustMatch = rustImportRegex.exec(code)) !== null) {
                imports.push(rustMatch[1]);
            }
            break;

        // Add more languages as needed
    }

    return [...new Set(imports)]; // Remove duplicates
}

/**
 * Extract dependencies from the code
 */
export function extractDependencies(code: string, language: string): string[] {
    const imports = extractImports(code, language);
    const dependencies: string[] = [];

    // Filter out standard library and relative imports
    for (const importPath of imports) {
        if (isExternalDependency(importPath, language)) {
            dependencies.push(getPackageName(importPath, language));
        }
    }

    return [...new Set(dependencies)]; // Remove duplicates
}

/**
 * Check if an import is an external dependency
 */
function isExternalDependency(importPath: string, language: string): boolean {
    // Relative imports
    if (importPath.startsWith('.') || importPath.startsWith('/')) {
        return false;
    }

    // Language-specific standard library checks
    switch (language.toLowerCase()) {
        case 'typescript':
        case 'javascript':
            // Node.js built-in modules
            const nodeBuiltins = [
                'fs', 'path', 'http', 'https', 'url', 'crypto', 'os', 'util',
                'events', 'stream', 'buffer', 'child_process', 'cluster'
            ];
            return !nodeBuiltins.includes(importPath);

        case 'python':
            // Python standard library (partial list)
            const pythonBuiltins = [
                'os', 'sys', 'json', 'datetime', 'math', 'random', 'urllib',
                'collections', 'itertools', 'functools', 're', 'typing'
            ];
            return !pythonBuiltins.includes(importPath.split('.')[0]);

        case 'java':
            // Java standard library
            return !importPath.startsWith('java.') && !importPath.startsWith('javax.');

        case 'csharp':
            // .NET standard library
            return !importPath.startsWith('System');

        case 'go':
            // Go standard library (partial list)
            const goBuiltins = [
                'fmt', 'os', 'io', 'net', 'http', 'json', 'time', 'strings',
                'strconv', 'context', 'sync', 'errors'
            ];
            return !goBuiltins.includes(importPath.split('/')[0]);

        default:
            return true;
    }
}

/**
 * Extract package name from import path
 */
function getPackageName(importPath: string, language: string): string {
    switch (language.toLowerCase()) {
        case 'typescript':
        case 'javascript':
            // For npm packages, get the package name (handle scoped packages)
            if (importPath.startsWith('@')) {
                const parts = importPath.split('/');
                return parts.length >= 2 ? `${parts[0]}/${parts[1]}` : importPath;
            }
            return importPath.split('/')[0];

        case 'python':
            // For Python packages, get the top-level package name
            return importPath.split('.')[0];

        case 'java':
            // For Java packages, get the group and artifact (simplified)
            const parts = importPath.split('.');
            return parts.length >= 2 ? `${parts[0]}.${parts[1]}` : importPath;

        default:
            return importPath;
    }
}

/**
 * Extract explanation from the generated text
 */
export function extractExplanation(text: string): string {
    // Remove code blocks
    const textWithoutCode = text.replace(/```[\s\S]*?```/g, '');
    
    // Extract the first paragraph or meaningful text
    const lines = textWithoutCode.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    if (lines.length === 0) {
        return 'Generated code without explanation.';
    }

    // Take the first few lines as explanation
    const explanation = lines.slice(0, 3).join(' ');
    
    return explanation.length > 10 ? explanation : 'Generated code based on requirements.';
}

/**
 * Extract functions from code
 */
export function extractFunctions(code: string, language: string): string[] {
    const patterns = getLanguagePatterns(language);
    const functions: string[] = [];

    let match;
    while ((match = patterns.functionPattern.exec(code)) !== null) {
        functions.push(match[1]);
    }

    return functions;
}

/**
 * Extract classes from code
 */
export function extractClasses(code: string, language: string): string[] {
    const patterns = getLanguagePatterns(language);
    const classes: string[] = [];

    let match;
    while ((match = patterns.classPattern.exec(code)) !== null) {
        classes.push(match[1]);
    }

    return classes;
}

/**
 * Remove comments from code
 */
export function removeComments(code: string, language: string): string {
    const patterns = getLanguagePatterns(language);
    return code.replace(patterns.commentPattern, '');
}

/**
 * Count lines of code (excluding comments and empty lines)
 */
export function countLinesOfCode(code: string, language: string): number {
    const codeWithoutComments = removeComments(code, language);
    const lines = codeWithoutComments.split('\n').filter(line => line.trim().length > 0);
    return lines.length;
}

/**
 * Validate code syntax (basic validation)
 */
export function validateCodeSyntax(code: string, language: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Basic syntax checks
    switch (language.toLowerCase()) {
        case 'typescript':
        case 'javascript':
            // Check for balanced braces
            if (!areBalanced(code, '{', '}')) {
                errors.push('Unbalanced curly braces');
            }
            if (!areBalanced(code, '(', ')')) {
                errors.push('Unbalanced parentheses');
            }
            if (!areBalanced(code, '[', ']')) {
                errors.push('Unbalanced square brackets');
            }
            break;

        case 'python':
            // Check for proper indentation (simplified)
            const lines = code.split('\n');
            let indentLevel = 0;
            for (const line of lines) {
                const trimmed = line.trim();
                if (trimmed.length === 0) continue;
                
                const currentIndent = line.length - line.trimStart().length;
                if (trimmed.endsWith(':')) {
                    indentLevel += 4;
                } else if (currentIndent < indentLevel && !trimmed.startsWith('#')) {
                    indentLevel = currentIndent;
                }
            }
            break;

        // Add more language-specific validations
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Check if brackets are balanced
 */
function areBalanced(code: string, open: string, close: string): boolean {
    let count = 0;
    for (const char of code) {
        if (char === open) {
            count++;
        } else if (char === close) {
            count--;
            if (count < 0) {
                return false;
            }
        }
    }
    return count === 0;
}
