/**
 * Language Detection Service for Code Generation
 * 
 * Provides utilities for detecting and working with programming languages.
 */

/**
 * Infer the programming language from the task description
 */
export function inferLanguage(task: string): string {
    const taskLower = task.toLowerCase();

    // Check for explicit language mentions
    if (taskLower.includes('typescript') || taskLower.includes('ts')) {
        return 'typescript';
    } else if (taskLower.includes('javascript') || taskLower.includes('js')) {
        return 'javascript';
    } else if (taskLower.includes('python') || taskLower.includes('py')) {
        return 'python';
    } else if (taskLower.includes('java')) {
        return 'java';
    } else if (taskLower.includes('c#') || taskLower.includes('csharp') || taskLower.includes('c sharp')) {
        return 'csharp';
    } else if (taskLower.includes('c++') || taskLower.includes('cpp')) {
        return 'cpp';
    } else if (taskLower.includes('go') || taskLower.includes('golang')) {
        return 'go';
    } else if (taskLower.includes('rust')) {
        return 'rust';
    } else if (taskLower.includes('ruby')) {
        return 'ruby';
    } else if (taskLower.includes('php')) {
        return 'php';
    }

    // Default to TypeScript for this extension
    return 'typescript';
}

/**
 * Check if two languages are compatible
 */
export function isCompatibleLanguage(lang1: string, lang2: string): boolean {
    const compatibilityMap: Record<string, string[]> = {
        'typescript': ['ts', 'javascript', 'js'],
        'javascript': ['js', 'typescript', 'ts'],
        'python': ['py'],
        'csharp': ['cs', 'c#'],
        'cpp': ['c++', 'c'],
        'java': [],
        'go': ['golang'],
        'rust': [],
        'ruby': ['rb'],
        'php': []
    };

    const normalizedLang1 = lang1.toLowerCase();
    const normalizedLang2 = lang2.toLowerCase();

    if (normalizedLang1 === normalizedLang2) {
        return true;
    }

    return compatibilityMap[normalizedLang1]?.includes(normalizedLang2) ||
           compatibilityMap[normalizedLang2]?.includes(normalizedLang1) ||
           false;
}

/**
 * Get language-specific configuration
 */
export function getLanguageConfig(language: string): {
    fileExtension: string;
    commentStyle: string;
    testFramework?: string;
    packageManager?: string;
} {
    const configs: Record<string, any> = {
        'typescript': {
            fileExtension: '.ts',
            commentStyle: '//',
            testFramework: 'jest',
            packageManager: 'npm'
        },
        'javascript': {
            fileExtension: '.js',
            commentStyle: '//',
            testFramework: 'jest',
            packageManager: 'npm'
        },
        'python': {
            fileExtension: '.py',
            commentStyle: '#',
            testFramework: 'pytest',
            packageManager: 'pip'
        },
        'java': {
            fileExtension: '.java',
            commentStyle: '//',
            testFramework: 'junit',
            packageManager: 'maven'
        },
        'csharp': {
            fileExtension: '.cs',
            commentStyle: '//',
            testFramework: 'nunit',
            packageManager: 'nuget'
        },
        'cpp': {
            fileExtension: '.cpp',
            commentStyle: '//',
            testFramework: 'gtest',
            packageManager: 'conan'
        },
        'go': {
            fileExtension: '.go',
            commentStyle: '//',
            testFramework: 'testing',
            packageManager: 'go mod'
        },
        'rust': {
            fileExtension: '.rs',
            commentStyle: '//',
            testFramework: 'cargo test',
            packageManager: 'cargo'
        },
        'ruby': {
            fileExtension: '.rb',
            commentStyle: '#',
            testFramework: 'rspec',
            packageManager: 'gem'
        },
        'php': {
            fileExtension: '.php',
            commentStyle: '//',
            testFramework: 'phpunit',
            packageManager: 'composer'
        }
    };

    return configs[language.toLowerCase()] || {
        fileExtension: '.txt',
        commentStyle: '//',
        testFramework: 'unknown',
        packageManager: 'unknown'
    };
}

/**
 * Get common patterns for a language
 */
export function getLanguagePatterns(language: string): {
    importPattern: RegExp;
    functionPattern: RegExp;
    classPattern: RegExp;
    commentPattern: RegExp;
} {
    const patterns: Record<string, any> = {
        'typescript': {
            importPattern: /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"]([^'"]+)['"]/g,
            functionPattern: /(?:export\s+)?(?:async\s+)?function\s+(\w+)/g,
            classPattern: /(?:export\s+)?class\s+(\w+)/g,
            commentPattern: /\/\/.*$|\/\*[\s\S]*?\*\//gm
        },
        'javascript': {
            importPattern: /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"]([^'"]+)['"]/g,
            functionPattern: /(?:export\s+)?(?:async\s+)?function\s+(\w+)/g,
            classPattern: /(?:export\s+)?class\s+(\w+)/g,
            commentPattern: /\/\/.*$|\/\*[\s\S]*?\*\//gm
        },
        'python': {
            importPattern: /(?:import\s+(\w+)(?:\s+as\s+\w+)?|from\s+([^\s]+)\s+import\s+[^#\n]+)/g,
            functionPattern: /def\s+(\w+)\s*\(/g,
            classPattern: /class\s+(\w+)/g,
            commentPattern: /#.*$|"""[\s\S]*?"""|'''[\s\S]*?'''/gm
        },
        'java': {
            importPattern: /import\s+([^;]+);/g,
            functionPattern: /(?:public|private|protected)?\s*(?:static\s+)?(?:\w+\s+)+(\w+)\s*\(/g,
            classPattern: /(?:public\s+)?class\s+(\w+)/g,
            commentPattern: /\/\/.*$|\/\*[\s\S]*?\*\//gm
        }
    };

    return patterns[language.toLowerCase()] || patterns['typescript'];
}

/**
 * Normalize language name
 */
export function normalizeLanguageName(language: string): string {
    const normalizations: Record<string, string> = {
        'ts': 'typescript',
        'js': 'javascript',
        'py': 'python',
        'cs': 'csharp',
        'c#': 'csharp',
        'c++': 'cpp',
        'golang': 'go',
        'rb': 'ruby'
    };

    const normalized = language.toLowerCase();
    return normalizations[normalized] || normalized;
}

/**
 * Get language display name
 */
export function getLanguageDisplayName(language: string): string {
    const displayNames: Record<string, string> = {
        'typescript': 'TypeScript',
        'javascript': 'JavaScript',
        'python': 'Python',
        'java': 'Java',
        'csharp': 'C#',
        'cpp': 'C++',
        'go': 'Go',
        'rust': 'Rust',
        'ruby': 'Ruby',
        'php': 'PHP'
    };

    return displayNames[language.toLowerCase()] || language;
}

/**
 * Check if language supports specific features
 */
export function getLanguageFeatures(language: string): {
    supportsClasses: boolean;
    supportsInterfaces: boolean;
    supportsGenerics: boolean;
    supportsAsync: boolean;
    supportsModules: boolean;
    isStaticallyTyped: boolean;
} {
    const features: Record<string, any> = {
        'typescript': {
            supportsClasses: true,
            supportsInterfaces: true,
            supportsGenerics: true,
            supportsAsync: true,
            supportsModules: true,
            isStaticallyTyped: true
        },
        'javascript': {
            supportsClasses: true,
            supportsInterfaces: false,
            supportsGenerics: false,
            supportsAsync: true,
            supportsModules: true,
            isStaticallyTyped: false
        },
        'python': {
            supportsClasses: true,
            supportsInterfaces: false,
            supportsGenerics: true,
            supportsAsync: true,
            supportsModules: true,
            isStaticallyTyped: false
        },
        'java': {
            supportsClasses: true,
            supportsInterfaces: true,
            supportsGenerics: true,
            supportsAsync: false,
            supportsModules: true,
            isStaticallyTyped: true
        },
        'csharp': {
            supportsClasses: true,
            supportsInterfaces: true,
            supportsGenerics: true,
            supportsAsync: true,
            supportsModules: true,
            isStaticallyTyped: true
        },
        'go': {
            supportsClasses: false,
            supportsInterfaces: true,
            supportsGenerics: true,
            supportsAsync: true,
            supportsModules: true,
            isStaticallyTyped: true
        },
        'rust': {
            supportsClasses: false,
            supportsInterfaces: true,
            supportsGenerics: true,
            supportsAsync: true,
            supportsModules: true,
            isStaticallyTyped: true
        }
    };

    return features[language.toLowerCase()] || {
        supportsClasses: false,
        supportsInterfaces: false,
        supportsGenerics: false,
        supportsAsync: false,
        supportsModules: false,
        isStaticallyTyped: false
    };
}
