/**
 * Code Analysis Agent for X10sion
 *
 * This module implements a specialized agent for analyzing code, identifying issues,
 * and suggesting improvements. It can analyze code quality, security vulnerabilities,
 * performance issues, and more.
 *
 * Based on May 2025 best practices for code analysis.
 */

import * as vscode from 'vscode';
import { BaseAgent, AgentOptions } from './base-agent.js';
import { WorkerPool, TaskPriority, Task } from '../parallel/worker-pool.js';
import { LLMMonitor } from '../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../llm/providers/base-provider.js';

/**
 * Create a task for the worker pool
 */
function createTask<T>(fn: () => T | Promise<T>, priority: TaskPriority = TaskPriority.NORMAL): Task<T> {
    return {
        execute: async () => {
            const result = fn();
            return result instanceof Promise ? await result : result;
        },
        priority
    };
}

/**
 * Issue Severity
 */
export enum IssueSeverity {
    INFO = 'info',
    WARNING = 'warning',
    ERROR = 'error',
    CRITICAL = 'critical'
}

/**
 * Issue Category
 */
export enum IssueCategory {
    QUALITY = 'quality',
    SECURITY = 'security',
    PERFORMANCE = 'performance',
    MAINTAINABILITY = 'maintainability',
    COMPATIBILITY = 'compatibility',
    ACCESSIBILITY = 'accessibility',
    BEST_PRACTICE = 'best_practice'
}

/**
 * Code Issue
 */
export interface CodeIssue {
    id: string;
    title: string;
    description: string;
    severity: IssueSeverity;
    category: IssueCategory;
    lineNumber?: number;
    columnNumber?: number;
    codeSnippet?: string;
    suggestion?: string;
    fixSuggestion?: string;
}

/**
 * Code Metric
 */
export interface CodeMetric {
    name: string;
    value: number;
    description: string;
    threshold?: number;
    status: 'good' | 'warning' | 'bad';
}

/**
 * Code Analysis Result
 */
export interface CodeAnalysisResult {
    issues: CodeIssue[];
    metrics: CodeMetric[];
    summary: string;
    recommendations: string[];
    codeQualityScore: number; // 0-100
}

/**
 * Code Analysis Request
 */
export interface CodeAnalysisRequest {
    code: string;
    language: string;
    filePath?: string;
    categories?: IssueCategory[];
    minSeverity?: IssueSeverity;
    includeMetrics?: boolean;
    includeRecommendations?: boolean;
    projectConventions?: string;
}

/**
 * Code Analysis Agent Options
 */
export interface CodeAnalysisAgentOptions extends AgentOptions {
    defaultCategories?: IssueCategory[];
    defaultMinSeverity?: IssueSeverity;
    includeMetricsByDefault?: boolean;
    includeRecommendationsByDefault?: boolean;
    maxIssues?: number;
}

/**
 * Code Analysis Agent Implementation
 */
export class CodeAnalysisAgent extends BaseAgent {
    private agentOptions: CodeAnalysisAgentOptions;

    constructor(
        id: string,
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider,
        options: CodeAnalysisAgentOptions = {}
    ) {
        super(
            id,
            'Code Analysis Agent',
            'Analyzes code for issues and suggests improvements',
            workerPool,
            llmMonitor,
            llmProvider,
            options
        );

        // Set default options
        this.agentOptions = {
            defaultCategories: Object.values(IssueCategory),
            defaultMinSeverity: IssueSeverity.INFO,
            includeMetricsByDefault: true,
            includeRecommendationsByDefault: true,
            maxIssues: 20,
            ...options
        };
    }

    /**
     * Execute a task with the agent
     */
    async execute(task: string, context: any = {}): Promise<CodeAnalysisResult> {
        try {
            // Parse the task as a code analysis request
            const request = this.parseRequest(task, context);

            // Create a prompt for code analysis
            const prompt = this.createAnalysisPrompt(request);

            // Generate analysis using the LLM
            const analysisText = await this.generateAnalysis(prompt);

            // Parse the analysis result
            const analysisResult = await this.parseAnalysisResult(analysisText, request);

            return analysisResult;
        } catch (error) {
            console.error('Error in CodeAnalysisAgent.execute:', error);
            throw error;
        }
    }

    /**
     * Parse the task into a code analysis request
     */
    private parseRequest(task: string, context: any): CodeAnalysisRequest {
        // Check if the task is already a code analysis request
        if (typeof task === 'object' && 'code' in task && 'language' in task) {
            return task as unknown as CodeAnalysisRequest;
        }

        // Start with defaults
        const request: CodeAnalysisRequest = {
            code: '',
            language: '',
            categories: this.agentOptions.defaultCategories,
            minSeverity: this.agentOptions.defaultMinSeverity,
            includeMetrics: this.agentOptions.includeMetricsByDefault,
            includeRecommendations: this.agentOptions.includeRecommendationsByDefault
        };

        // If task is a string, try to extract code and language
        if (typeof task === 'string') {
            // Check if the task contains code blocks
            const codeBlocks = this.extractCodeBlocks(task);
            if (codeBlocks.length > 0) {
                request.code = codeBlocks[0].code;
                request.language = codeBlocks[0].language || this.inferLanguage(task);
            } else {
                // Assume the task is a file path or a request to analyze code
                request.code = context.code || '';
                request.language = context.language || this.inferLanguage(task);
                request.filePath = task.match(/^[\w\/\.-]+\.\w+$/) ? task : context.filePath;
            }
        }

        // Override with context if provided
        if (context.code) {
            request.code = context.code;
        }

        if (context.language) {
            request.language = context.language;
        }

        if (context.filePath) {
            request.filePath = context.filePath;
        }

        if (context.categories) {
            request.categories = context.categories;
        }

        if (context.minSeverity) {
            request.minSeverity = context.minSeverity;
        }

        if (context.includeMetrics !== undefined) {
            request.includeMetrics = context.includeMetrics;
        }

        if (context.includeRecommendations !== undefined) {
            request.includeRecommendations = context.includeRecommendations;
        }

        if (context.projectConventions) {
            request.projectConventions = context.projectConventions;
        }

        // If we still don't have code, try to read from the file path
        if (!request.code && request.filePath) {
            try {
                const document = vscode.workspace.textDocuments.find(
                    doc => doc.uri.fsPath === request.filePath
                );

                if (document) {
                    request.code = document.getText();
                    request.language = document.languageId;
                }
            } catch (error) {
                console.warn(`Failed to read file: ${request.filePath}`, error);
            }
        }

        return request;
    }

    /**
     * Extract code blocks from text
     */
    private extractCodeBlocks(text: string): { language: string; code: string }[] {
        const codeBlockRegex = /```(\w*)\n([\s\S]*?)```/g;
        const codeBlocks: { language: string; code: string }[] = [];

        let match;
        while ((match = codeBlockRegex.exec(text)) !== null) {
            codeBlocks.push({
                language: match[1].toLowerCase(),
                code: match[2].trim()
            });
        }

        return codeBlocks;
    }

    /**
     * Infer the programming language from text
     */
    private inferLanguage(text: string): string {
        const textLower = text.toLowerCase();

        // Check for explicit language mentions
        if (textLower.includes('typescript') || textLower.includes('ts')) {
            return 'typescript';
        } else if (textLower.includes('javascript') || textLower.includes('js')) {
            return 'javascript';
        } else if (textLower.includes('python') || textLower.includes('py')) {
            return 'python';
        } else if (textLower.includes('java')) {
            return 'java';
        } else if (textLower.includes('c#') || textLower.includes('csharp') || textLower.includes('c sharp')) {
            return 'csharp';
        } else if (textLower.includes('c++') || textLower.includes('cpp')) {
            return 'cpp';
        } else if (textLower.includes('go') || textLower.includes('golang')) {
            return 'go';
        } else if (textLower.includes('rust')) {
            return 'rust';
        } else if (textLower.includes('ruby')) {
            return 'ruby';
        } else if (textLower.includes('php')) {
            return 'php';
        }

        // Default to TypeScript for this extension
        return 'typescript';
    }

    /**
     * Create a prompt for code analysis
     */
    private createAnalysisPrompt(request: CodeAnalysisRequest): string {
        let prompt = `Analyze the following ${request.language} code for issues and provide detailed feedback:

\`\`\`${request.language}
${request.code}
\`\`\`

Analyze the code for the following categories:
${request.categories?.map(category => `- ${category}`).join('\n')}

Minimum severity level: ${request.minSeverity}

${request.includeMetrics ? 'Include metrics for code quality, complexity, and maintainability.' : ''}
${request.includeRecommendations ? 'Provide specific recommendations for improving the code.' : ''}
${request.projectConventions ? `Consider the following project conventions:\n${request.projectConventions}` : ''}

For each issue found, provide:
1. A title
2. A description
3. The severity (info, warning, error, critical)
4. The category
5. The line number (if applicable)
6. A code snippet showing the issue
7. A suggestion for fixing the issue

Also provide:
1. A summary of the analysis
2. A code quality score (0-100)
3. Key recommendations for improvement

Format the response as JSON with the following structure:
{
  "issues": [
    {
      "id": "unique-id",
      "title": "Issue title",
      "description": "Detailed description",
      "severity": "severity-level",
      "category": "issue-category",
      "lineNumber": 123,
      "codeSnippet": "code with issue",
      "suggestion": "how to fix"
    }
  ],
  "metrics": [
    {
      "name": "metric-name",
      "value": 123,
      "description": "metric description",
      "threshold": 100,
      "status": "good|warning|bad"
    }
  ],
  "summary": "Overall analysis summary",
  "recommendations": [
    "recommendation 1",
    "recommendation 2"
  ],
  "codeQualityScore": 85
}`;

        return prompt;
    }

    /**
     * Generate analysis using the LLM
     */
    private async generateAnalysis(prompt: string): Promise<string> {
        const completion = await this.llmProvider.generateCompletion(prompt, {
            maxTokens: 2000,
            temperature: 0.1, // Lower temperature for more deterministic analysis
            stopSequences: []
        });

        return completion.text;
    }

    /**
     * Parse the analysis result from the LLM response
     */
    private async parseAnalysisResult(
        analysisText: string,
        request: CodeAnalysisRequest
    ): Promise<CodeAnalysisResult> {
        // Use worker pool for parallel processing
        return await this.workerPool.execute(createTask(() => {
            try {
                // Extract JSON from the response
                const jsonMatch = analysisText.match(/```json\n([\s\S]*?)```/) ||
                                 analysisText.match(/```\n([\s\S]*?)```/) ||
                                 analysisText.match(/{[\s\S]*}/);

                let jsonStr = jsonMatch ? jsonMatch[0] : analysisText;

                // Clean up the JSON string
                jsonStr = jsonStr.replace(/```json\n|```\n|```/g, '').trim();

                // Parse the JSON
                const result = JSON.parse(jsonStr) as CodeAnalysisResult;

                // Ensure all required properties exist
                result.issues = result.issues || [];
                result.metrics = result.metrics || [];
                result.summary = result.summary || 'No summary provided';
                result.recommendations = result.recommendations || [];
                result.codeQualityScore = result.codeQualityScore || 0;

                // Filter issues by minimum severity if needed
                if (request.minSeverity) {
                    const severityOrder = {
                        [IssueSeverity.INFO]: 0,
                        [IssueSeverity.WARNING]: 1,
                        [IssueSeverity.ERROR]: 2,
                        [IssueSeverity.CRITICAL]: 3
                    };

                    const minSeverityLevel = severityOrder[request.minSeverity];

                    result.issues = result.issues.filter(issue =>
                        severityOrder[issue.severity as IssueSeverity] >= minSeverityLevel
                    );
                }

                // Limit the number of issues if needed
                if (this.agentOptions.maxIssues && result.issues.length > this.agentOptions.maxIssues) {
                    result.issues = result.issues.slice(0, this.agentOptions.maxIssues);
                }

                return result;
            } catch (error) {
                console.error('Error parsing analysis result:', error);

                // Return a fallback result
                return {
                    issues: [],
                    metrics: [],
                    summary: 'Failed to parse analysis result',
                    recommendations: ['Try analyzing the code again'],
                    codeQualityScore: 0
                };
            }
        }, TaskPriority.NORMAL));
    }

    /**
     * Analyze a file in the workspace
     */
    async analyzeFile(filePath: string, options: Partial<CodeAnalysisRequest> = {}): Promise<CodeAnalysisResult> {
        try {
            // Read the file
            const document = await vscode.workspace.openTextDocument(filePath);
            const code = document.getText();
            const language = document.languageId;

            // Create a request
            const request: CodeAnalysisRequest = {
                code,
                language,
                filePath,
                ...options
            };

            // Execute the analysis
            return await this.execute(JSON.stringify(request), {});
        } catch (error) {
            console.error(`Error analyzing file ${filePath}:`, error);
            throw error;
        }
    }

    /**
     * Analyze multiple files in the workspace
     */
    async analyzeFiles(
        filePaths: string[],
        options: Partial<CodeAnalysisRequest> = {}
    ): Promise<Record<string, CodeAnalysisResult>> {
        const results: Record<string, CodeAnalysisResult> = {};

        // Analyze each file in parallel
        await Promise.all(
            filePaths.map(async (filePath) => {
                try {
                    results[filePath] = await this.analyzeFile(filePath, options);
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    console.error(`Error analyzing file ${filePath}:`, errorMessage);
                    // Add a placeholder result for failed analyses
                    results[filePath] = {
                        issues: [],
                        metrics: [],
                        summary: `Failed to analyze file: ${errorMessage}`,
                        recommendations: [],
                        codeQualityScore: 0
                    };
                }
            })
        );

        return results;
    }
}
