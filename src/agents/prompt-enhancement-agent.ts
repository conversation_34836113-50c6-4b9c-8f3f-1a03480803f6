/**
 * Prompt Enhancement Agent for X10sion
 *
 * This module implements a specialized agent for enhancing prompts to improve
 * the quality of responses from language models. It analyzes prompts, prioritizes
 * context, manages token budgets, and generates optimized prompts.
 *
 * Based on May 2025 best practices for prompt engineering.
 */

import * as vscode from 'vscode';
import { BaseAgent, AgentOptions } from './base-agent.js';
import { WorkerPool, TaskPriority, Task } from '../parallel/worker-pool.js';
import { LLMMonitor } from '../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../llm/providers/base-provider.js';
import { PromptAnalyzer, PromptAnalysisResult } from './prompt-enhancement/prompt-analyzer.js';
import { ContextPrioritizer, ScoredContextItem, ContextPrioritizationStrategy } from './prompt-enhancement/context-prioritizer.js';
import { TokenManager, TokenBudget } from './prompt-enhancement/token-manager.js';

/**
 * Create a task for the worker pool
 */
function createTask<T>(fn: () => T | Promise<T>, priority: TaskPriority = TaskPriority.NORMAL): Task<T> {
    return {
        execute: async () => {
            const result = fn();
            return result instanceof Promise ? await result : result;
        },
        priority
    };
}

// Re-export types for backward compatibility
export { PromptAnalysisResult } from './prompt-enhancement/prompt-analyzer.js';
export { ScoredContextItem, ContextPrioritizationStrategy } from './prompt-enhancement/context-prioritizer.js';
export { TokenBudget } from './prompt-enhancement/token-manager.js';

/**
 * Prompt Template
 */
export interface PromptTemplate {
    id: string;
    name: string;
    template: string;
    placeholders: string[];
    description: string;
    modelType: string;
    tokenEstimate: number;
}

/**
 * Enhanced Prompt
 */
export interface EnhancedPrompt {
    originalPrompt: string;
    enhancedPrompt: string;
    analysis: PromptAnalysisResult;
    includedContext: ScoredContextItem[];
    tokenBudget: TokenBudget;
    template: PromptTemplate;
}

/**
 * Prompt Enhancement Agent Options
 */
export interface PromptEnhancementAgentOptions extends AgentOptions {
    defaultTokenBudget?: number;
    promptTemplates?: PromptTemplate[];
    keywordExtractionThreshold?: number;
    minRelevanceScore?: number;
    contextPrioritizationStrategy?: ContextPrioritizationStrategy;
    enableTokenCompression?: boolean;
}

/**
 * Prompt Enhancement Agent Implementation
 */
export class PromptEnhancementAgent extends BaseAgent {
    private promptTemplates: Map<string, PromptTemplate> = new Map();
    private agentOptions: PromptEnhancementAgentOptions;

    // Modular components
    private promptAnalyzer: PromptAnalyzer;
    private contextPrioritizer: ContextPrioritizer;
    private tokenManager: TokenManager;

    constructor(
        id: string,
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider,
        options: PromptEnhancementAgentOptions = {}
    ) {
        super(
            id,
            'Prompt Enhancement Agent',
            'Analyzes and enhances prompts for better LLM responses',
            workerPool,
            llmMonitor,
            llmProvider,
            options
        );

        // Set default options
        this.agentOptions = {
            defaultTokenBudget: 4000,
            keywordExtractionThreshold: 0.3,
            minRelevanceScore: 0.5,
            contextPrioritizationStrategy: 'hybrid',
            enableTokenCompression: true,
            ...options
        };

        // Initialize modular components
        this.promptAnalyzer = new PromptAnalyzer();
        this.contextPrioritizer = new ContextPrioritizer({
            strategy: this.agentOptions.contextPrioritizationStrategy || 'hybrid',
            minRelevanceScore: this.agentOptions.minRelevanceScore || 0.5
        });
        this.tokenManager = new TokenManager({
            defaultTokenBudget: this.agentOptions.defaultTokenBudget || 4000,
            enableCompression: this.agentOptions.enableTokenCompression !== false
        });

        // Initialize prompt templates
        this.initializePromptTemplates();
    }

    /**
     * Initialize prompt templates
     */
    private initializePromptTemplates(): void {
        // Add default templates if none provided
        if (!this.agentOptions.promptTemplates || this.agentOptions.promptTemplates.length === 0) {
            const defaultTemplates: PromptTemplate[] = [
                {
                    id: 'general',
                    name: 'General Purpose',
                    template: 'I want you to respond to the following prompt:\n\n{prompt}\n\nContext:\n{context}\n\nPlease provide a detailed response.',
                    placeholders: ['prompt', 'context'],
                    description: 'A general-purpose template for most queries',
                    modelType: 'any',
                    tokenEstimate: 50
                },
                {
                    id: 'code',
                    name: 'Code Generation',
                    template: 'I want you to generate code based on the following requirements:\n\n{prompt}\n\nContext:\n{context}\n\nPlease provide well-documented, efficient code that follows best practices.',
                    placeholders: ['prompt', 'context'],
                    description: 'A template optimized for code generation',
                    modelType: 'any',
                    tokenEstimate: 70
                },
                {
                    id: 'explanation',
                    name: 'Code Explanation',
                    template: 'I want you to explain the following code:\n\n{context}\n\nPrompt: {prompt}\n\nPlease provide a clear, detailed explanation.',
                    placeholders: ['prompt', 'context'],
                    description: 'A template optimized for code explanation',
                    modelType: 'any',
                    tokenEstimate: 60
                }
            ];

            this.agentOptions.promptTemplates = defaultTemplates;
        }

        // Register templates
        for (const template of this.agentOptions.promptTemplates) {
            this.promptTemplates.set(template.id, template);
        }
    }

    /**
     * Execute a task with the agent
     */
    async execute(task: string, context: any = {}): Promise<EnhancedPrompt> {
        try {
            // Extract the original prompt from the task or context
            const originalPrompt = context.prompt || task;

            // Analyze the prompt using the prompt analyzer
            const analysis = await this.promptAnalyzer.analyzePrompt(originalPrompt);

            // Prioritize context using the context prioritizer
            const scoredContext = await this.contextPrioritizer.prioritizeContext(originalPrompt, analysis, context.contextItems || []);

            // Manage token budget using the token manager
            const tokenBudget = this.tokenManager.manageTokenBudget(originalPrompt, scoredContext, context.maxTokens);

            // Select context based on token budget
            const selectedContext = this.tokenManager.selectContextWithinBudget(scoredContext, tokenBudget.context);

            // Select appropriate template
            const template = this.selectTemplate(analysis, context.templateId);

            // Generate enhanced prompt
            const enhancedPrompt = await this.generatePrompt(originalPrompt, selectedContext, template);

            // Return the enhanced prompt
            return {
                originalPrompt,
                enhancedPrompt,
                analysis,
                includedContext: selectedContext,
                tokenBudget,
                template
            };
        } catch (error) {
            console.error('Error in PromptEnhancementAgent.execute:', error);
            throw error;
        }
    }









    /**
     * Select an appropriate template based on prompt analysis
     */
    private selectTemplate(
        analysis: PromptAnalysisResult,
        templateId?: string
    ): PromptTemplate {
        // If template ID is provided, use it
        if (templateId && this.promptTemplates.has(templateId)) {
            return this.promptTemplates.get(templateId)!;
        }

        // Select based on intent
        switch (analysis.intent) {
            case 'code_generation':
                return this.promptTemplates.get('code') || this.promptTemplates.get('general')!;

            case 'explanation':
                return this.promptTemplates.get('explanation') || this.promptTemplates.get('general')!;

            default:
                return this.promptTemplates.get('general')!;
        }
    }

    /**
     * Generate an enhanced prompt using the template and context
     */
    private async generatePrompt(
        originalPrompt: string,
        contextItems: ScoredContextItem[],
        template: PromptTemplate
    ): Promise<string> {
        // Format context items into a string
        const contextString = contextItems.map(item => {
            return `--- ${item.type.toUpperCase()} ---\n${item.content}\n`;
        }).join('\n');

        // Replace placeholders in the template
        let enhancedPrompt = template.template;

        // Replace {prompt} with the original prompt
        enhancedPrompt = enhancedPrompt.replace(/{prompt}/g, originalPrompt);

        // Replace {context} with the formatted context
        enhancedPrompt = enhancedPrompt.replace(/{context}/g, contextString);

        return enhancedPrompt;
    }


}
