/**
 * Configuration for Human-in-the-Loop Agent
 *
 * This module provides configuration options for the human-in-the-loop agent,
 * including default settings and utility functions.
 */

import * as vscode from 'vscode';
import {
    InterventionLevel,
    ActionType,
    HumanInTheLoopCapabilities
} from './human-in-the-loop-agent.js';

/**
 * Default configuration for human-in-the-loop capabilities
 */
export function getDefaultHumanInTheLoopConfig(): HumanInTheLoopCapabilities {
    // Get user settings
    const config = vscode.workspace.getConfiguration('x10sion.humanInTheLoop');

    // Create action type settings map with default values
    const actionTypeSettings = new Map<ActionType, InterventionLevel>();

    // Set default intervention levels for different action types
    actionTypeSettings.set(ActionType.CODE_GENERATION,
        getInterventionLevelFromConfig(config, 'codeGeneration', InterventionLevel.NOTIFICATION));

    actionTypeSettings.set(ActionType.CODE_MODIFICATION,
        getInterventionLevelFromConfig(config, 'codeModification', InterventionLevel.APPROVAL));

    actionTypeSettings.set(ActionType.FILE_SYSTEM_ACCESS,
        getInterventionLevelFromConfig(config, 'fileSystemAccess', InterventionLevel.APPROVAL));

    actionTypeSettings.set(ActionType.EXTERNAL_API_CALL,
        getInterventionLevelFromConfig(config, 'externalApiCall', InterventionLevel.APPROVAL));

    actionTypeSettings.set(ActionType.SENSITIVE_DATA_ACCESS,
        getInterventionLevelFromConfig(config, 'sensitiveDataAccess', InterventionLevel.TAKEOVER));

    actionTypeSettings.set(ActionType.SECURITY_CRITICAL,
        getInterventionLevelFromConfig(config, 'securityCritical', InterventionLevel.TAKEOVER));

    actionTypeSettings.set(ActionType.RESOURCE_INTENSIVE,
        getInterventionLevelFromConfig(config, 'resourceIntensive', InterventionLevel.APPROVAL));

    actionTypeSettings.set(ActionType.UNCERTAIN_OUTPUT,
        getInterventionLevelFromConfig(config, 'uncertainOutput', InterventionLevel.GUIDANCE));

    return {
        defaultInterventionLevel: getInterventionLevelFromConfig(
            config,
            'defaultLevel',
            InterventionLevel.APPROVAL
        ),
        actionTypeSettings,
        timeoutMs: config.get<number>('timeoutMs', 30000), // Default 30 seconds
        allowAutoApprovalForLowRisk: config.get<boolean>('allowAutoApprovalForLowRisk', false),
        collectFeedback: config.get<boolean>('collectFeedback', true),
        adaptiveMode: config.get<boolean>('adaptiveMode', true)
    };
}

/**
 * Get intervention level from configuration
 */
function getInterventionLevelFromConfig(
    config: vscode.WorkspaceConfiguration,
    key: string,
    defaultValue: InterventionLevel
): InterventionLevel {
    const value = config.get<string>(`interventionLevels.${key}`);

    if (!value) {
        return defaultValue;
    }

    switch (value.toLowerCase()) {
        case 'none':
            return InterventionLevel.NONE;
        case 'notification':
            return InterventionLevel.NOTIFICATION;
        case 'approval':
            return InterventionLevel.APPROVAL;
        case 'guidance':
            return InterventionLevel.GUIDANCE;
        case 'takeover':
            return InterventionLevel.TAKEOVER;
        default:
            return defaultValue;
    }
}

/**
 * Register configuration contribution in package.json
 *
 * This is a helper function to document the configuration that should be added
 * to the package.json file for the extension.
 */
export function getPackageJsonConfig(): any {
    return {
        "contributes": {
            "configuration": {
                "title": "X10sion",
                "properties": {
                    "x10sion.humanInTheLoop.defaultLevel": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "approval",
                        "description": "Default intervention level for AI actions"
                    },
                    "x10sion.humanInTheLoop.timeoutMs": {
                        "type": "number",
                        "default": 30000,
                        "description": "Timeout in milliseconds for human intervention requests"
                    },
                    "x10sion.humanInTheLoop.allowAutoApprovalForLowRisk": {
                        "type": "boolean",
                        "default": false,
                        "description": "Automatically approve low-risk actions on timeout"
                    },
                    "x10sion.humanInTheLoop.collectFeedback": {
                        "type": "boolean",
                        "default": true,
                        "description": "Collect feedback from users on interventions"
                    },
                    "x10sion.humanInTheLoop.adaptiveMode": {
                        "type": "boolean",
                        "default": true,
                        "description": "Adjust intervention levels based on user feedback"
                    },
                    "x10sion.humanInTheLoop.interventionLevels.codeGeneration": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "notification",
                        "description": "Intervention level for code generation"
                    },
                    "x10sion.humanInTheLoop.interventionLevels.codeModification": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "approval",
                        "description": "Intervention level for code modification"
                    },
                    "x10sion.humanInTheLoop.interventionLevels.fileSystemAccess": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "approval",
                        "description": "Intervention level for file system access"
                    },
                    "x10sion.humanInTheLoop.interventionLevels.externalApiCall": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "approval",
                        "description": "Intervention level for external API calls"
                    },
                    "x10sion.humanInTheLoop.interventionLevels.sensitiveDataAccess": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "takeover",
                        "description": "Intervention level for sensitive data access"
                    },
                    "x10sion.humanInTheLoop.interventionLevels.securityCritical": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "takeover",
                        "description": "Intervention level for security-critical operations"
                    },
                    "x10sion.humanInTheLoop.interventionLevels.resourceIntensive": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "approval",
                        "description": "Intervention level for resource-intensive operations"
                    },
                    "x10sion.humanInTheLoop.interventionLevels.uncertainOutput": {
                        "type": "string",
                        "enum": ["none", "notification", "approval", "guidance", "takeover"],
                        "default": "guidance",
                        "description": "Intervention level for uncertain AI outputs"
                    }
                }
            }
        }
    };
}
