/**
 * Base Agent for X10sion
 *
 * This module defines the base class for all AI agents in the X10sion system.
 */

import { WorkerPool, TaskPriority } from '../parallel/worker-pool.js';
import { LLMMonitor } from '../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../llm/providers/base-provider.js';
import * as vscode from 'vscode';

/**
 * Agent Status
 */
export enum AgentStatus {
    IDLE = 'idle',
    BUSY = 'busy',
    ERROR = 'error',
    DISPOSED = 'disposed'
}

/**
 * Agent Memory Item
 */
export interface AgentMemoryItem {
    id: string;
    timestamp: number;
    type: string;
    content: any;
    metadata?: Record<string, any>;
}

/**
 * Agent Task
 */
export interface AgentTask {
    id: string;
    task: string;
    context: any;
    priority: TaskPriority;
    timestamp: number;
    status: 'pending' | 'running' | 'completed' | 'failed';
    result?: any;
    error?: Error;
}

/**
 * Agent Options
 */
export interface AgentOptions {
    maxMemoryItems?: number;
    contextWindowSize?: number;
    maxTokens?: number;
    temperature?: number;
    useCache?: boolean;
    cacheExpiration?: number;
    debugMode?: boolean;
    timeout?: number;
    retryCount?: number;
    retryDelay?: number;
    [key: string]: any;
}

/**
 * Base Agent class
 *
 * All AI agents in the system should extend this class.
 */
export class BaseAgent {
    protected id: string;
    protected name: string;
    protected description: string;
    protected workerPool: WorkerPool;
    protected llmMonitor: LLMMonitor;
    protected llmProvider: LanguageModelProvider;
    protected options: AgentOptions;
    protected status: AgentStatus = AgentStatus.IDLE;
    protected memory: AgentMemoryItem[] = [];
    protected tasks: Map<string, AgentTask> = new Map();
    protected eventEmitter: vscode.EventEmitter<any> = new vscode.EventEmitter<any>();
    protected disposables: vscode.Disposable[] = [];
    protected lastActivity: number = Date.now();
    protected collaboratingAgents: Set<BaseAgent> = new Set();

    /**
     * Constructor for BaseAgent
     */
    constructor(
        id: string,
        name: string,
        description: string,
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider,
        options: AgentOptions = {}
    ) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.workerPool = workerPool;
        this.llmMonitor = llmMonitor;
        this.llmProvider = llmProvider;

        // Set default options
        this.options = {
            maxMemoryItems: 100,
            contextWindowSize: 4000,
            maxTokens: 1000,
            temperature: 0.7,
            useCache: true,
            cacheExpiration: 3600000, // 1 hour
            debugMode: false,
            timeout: 30000, // 30 seconds
            retryCount: 3,
            retryDelay: 1000, // 1 second
            ...options
        };

        // Register with the LLM monitor
        this.llmMonitor.registerAgent(this);
    }

    /**
     * Get agent ID
     */
    getId(): string {
        return this.id;
    }

    /**
     * Get agent name
     */
    getName(): string {
        return this.name;
    }

    /**
     * Get agent description
     */
    getDescription(): string {
        return this.description;
    }

    /**
     * Get agent status
     */
    getStatus(): AgentStatus {
        return this.status;
    }

    /**
     * Get the LLM provider used by this agent
     */
    getLLMProvider(): LanguageModelProvider {
        return this.llmProvider;
    }

    /**
     * Set the LLM provider for this agent
     * @param provider New LLM provider
     */
    setLLMProvider(provider: LanguageModelProvider): void {
        this.llmProvider = provider;

        // Add to memory
        this.addToMemory('provider_changed', {
            providerName: provider.getName(),
            timestamp: Date.now()
        });
    }

    /**
     * Execute a task with the agent
     */
    async execute(task: string, context: any = {}): Promise<any> {
        // This is a placeholder implementation
        // Subclasses should override this method with their specific implementation
        throw new Error('Method not implemented. Subclasses must implement execute().');
    }

    /**
     * Send a request to the LLM with monitoring
     * @param prompt Prompt to send
     * @param options LLM options
     * @returns LLM response
     */
    protected async sendToLLM(prompt: string, options: any = {}): Promise<string> {
        // Set default options
        const llmOptions = {
            temperature: this.options.temperature,
            maxTokens: this.options.maxTokens,
            ...options
        };

        // Track the request with the LLM monitor
        const requestId = this.llmMonitor.trackRequest(
            this.llmProvider.getDefaultModel(),
            prompt,
            llmOptions,
            this.id
        );

        try {
            // Generate completion
            const startTime = Date.now();
            const completion = await this.llmProvider.generateCompletion(prompt, {
                temperature: llmOptions.temperature,
                maxTokens: llmOptions.maxTokens
            });
            const endTime = Date.now();

            // Track the response with the LLM monitor
            this.llmMonitor.trackResponse(
                requestId,
                this.llmProvider.getDefaultModel(),
                completion.text,
                endTime - startTime,
                completion.usage?.totalTokens,
                this.id
            );

            return completion.text;
        } catch (error) {
            // Track the error with the LLM monitor
            this.llmMonitor.trackError(
                requestId,
                this.llmProvider.getDefaultModel(),
                error instanceof Error ? error : new Error(String(error)),
                this.id
            );

            throw error;
        }
    }

    /**
     * Queue a task for execution
     * @param task Task to execute
     * @param context Task context
     * @param priority Task priority
     * @returns Task ID
     */
    queueTask(task: string, context: any = {}, priority: TaskPriority = TaskPriority.NORMAL): string {
        const taskId = `task-${this.id}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        const agentTask: AgentTask = {
            id: taskId,
            task,
            context,
            priority,
            timestamp: Date.now(),
            status: 'pending'
        };

        this.tasks.set(taskId, agentTask);
        this.processTaskQueue();

        return taskId;
    }

    /**
     * Process the task queue
     */
    protected async processTaskQueue(): Promise<void> {
        if (this.status === AgentStatus.BUSY || this.status === AgentStatus.DISPOSED) {
            return;
        }

        // Find the highest priority pending task
        let highestPriorityTask: AgentTask | undefined;
        let highestPriority = -1;

        for (const task of this.tasks.values()) {
            if (task.status === 'pending' && task.priority > highestPriority) {
                highestPriorityTask = task;
                highestPriority = task.priority;
            }
        }

        if (!highestPriorityTask) {
            return;
        }

        // Execute the task
        this.status = AgentStatus.BUSY;
        highestPriorityTask.status = 'running';

        try {
            const result = await this.execute(highestPriorityTask.task, highestPriorityTask.context);

            highestPriorityTask.status = 'completed';
            highestPriorityTask.result = result;

            // Add to memory
            this.addToMemory('task_result', {
                taskId: highestPriorityTask.id,
                task: highestPriorityTask.task,
                result
            });

            // Emit event
            this.eventEmitter.fire({
                type: 'task_completed',
                taskId: highestPriorityTask.id,
                result
            });

            return result;
        } catch (error) {
            highestPriorityTask.status = 'failed';
            highestPriorityTask.error = error as Error;

            // Add to memory
            this.addToMemory('task_error', {
                taskId: highestPriorityTask.id,
                task: highestPriorityTask.task,
                error
            });

            // Emit event
            this.eventEmitter.fire({
                type: 'task_failed',
                taskId: highestPriorityTask.id,
                error
            });

            this.status = AgentStatus.ERROR;
            throw error;
        } finally {
            this.lastActivity = Date.now();
            this.status = AgentStatus.IDLE;

            // Process the next task
            setTimeout(() => this.processTaskQueue(), 0);
        }
    }

    /**
     * Get task status
     * @param taskId Task ID
     * @returns Task status or undefined if not found
     */
    getTaskStatus(taskId: string): AgentTask | undefined {
        return this.tasks.get(taskId);
    }

    /**
     * Add an item to agent memory
     * @param type Memory item type
     * @param content Memory item content
     * @param metadata Memory item metadata
     * @returns Memory item ID
     */
    protected addToMemory(type: string, content: any, metadata?: Record<string, any>): string {
        const id = `memory-${this.id}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

        const memoryItem: AgentMemoryItem = {
            id,
            timestamp: Date.now(),
            type,
            content,
            metadata
        };

        this.memory.push(memoryItem);

        // Trim memory if it exceeds the maximum size
        if (this.memory.length > this.options.maxMemoryItems!) {
            this.memory.shift();
        }

        return id;
    }

    /**
     * Get memory items
     * @param type Optional memory item type filter
     * @param limit Optional limit on number of items to return
     * @returns Memory items
     */
    getMemory(type?: string, limit?: number): AgentMemoryItem[] {
        let items = this.memory;

        if (type) {
            items = items.filter(item => item.type === type);
        }

        // Sort by timestamp (newest first)
        items = items.sort((a, b) => b.timestamp - a.timestamp);

        if (limit) {
            items = items.slice(0, limit);
        }

        return items;
    }

    /**
     * Clear memory
     * @param type Optional memory item type filter
     */
    clearMemory(type?: string): void {
        if (type) {
            this.memory = this.memory.filter(item => item.type !== type);
        } else {
            this.memory = [];
        }
    }

    /**
     * Add a collaborating agent
     * @param agent Agent to collaborate with
     */
    addCollaborator(agent: BaseAgent): void {
        this.collaboratingAgents.add(agent);
    }

    /**
     * Remove a collaborating agent
     * @param agent Agent to remove from collaboration
     */
    removeCollaborator(agent: BaseAgent): void {
        this.collaboratingAgents.delete(agent);
    }

    /**
     * Get collaborating agents
     * @returns Set of collaborating agents
     */
    getCollaborators(): Set<BaseAgent> {
        return this.collaboratingAgents;
    }

    /**
     * Send a message to a collaborating agent
     * @param agentId ID of the agent to send the message to
     * @param message Message to send
     * @param metadata Message metadata
     * @returns Promise that resolves when the message is sent
     */
    async sendMessage(agentId: string, message: any, metadata?: Record<string, any>): Promise<void> {
        // Find the agent
        const agent = Array.from(this.collaboratingAgents).find(a => a.getId() === agentId);

        if (!agent) {
            throw new Error(`Agent ${agentId} not found in collaborators`);
        }

        // Add to memory
        this.addToMemory('message_sent', {
            recipientId: agentId,
            message,
            metadata
        });

        // Send the message
        await agent.receiveMessage(this.id, message, metadata);
    }

    /**
     * Receive a message from another agent
     * @param senderId ID of the agent that sent the message
     * @param message Message content
     * @param metadata Message metadata
     */
    async receiveMessage(senderId: string, message: any, metadata?: Record<string, any>): Promise<void> {
        // Add to memory
        this.addToMemory('message_received', {
            senderId,
            message,
            metadata
        });

        // Emit event
        this.eventEmitter.fire({
            type: 'message_received',
            senderId,
            message,
            metadata
        });

        // Subclasses should override this method to handle messages
    }

    /**
     * Subscribe to agent events
     * @param listener Event listener
     * @returns Disposable
     */
    onEvent(listener: (event: any) => void): vscode.Disposable {
        return this.eventEmitter.event(listener);
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        this.status = AgentStatus.DISPOSED;

        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Clear memory and tasks
        this.memory = [];
        this.tasks.clear();

        // Remove from collaborators
        for (const agent of this.collaboratingAgents) {
            agent.removeCollaborator(this);
        }

        // Clear collaborators
        this.collaboratingAgents.clear();

        // Unregister from the LLM monitor
        this.llmMonitor.unregisterAgent(this);

        // Dispose of event emitter
        this.eventEmitter.dispose();
    }
}
