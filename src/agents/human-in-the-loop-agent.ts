/**
 * Human-in-the-Loop Agent for X10sion
 *
 * This module implements a human-in-the-loop system for AI agents, allowing for
 * human intervention, approval, and feedback at various stages of AI operation.
 *
 * Based on May 2025 best practices for human-in-the-loop AI systems.
 */

import * as vscode from 'vscode';
import { BaseAgent } from './base-agent.js';
import { LLMMonitor } from '../monitoring/llm-monitor.js';
import { WorkerPool } from '../parallel/worker-pool.js';
import { LanguageModelProvider } from '../llm/providers/base-provider.js';

// Intervention levels for human-in-the-loop
export enum InterventionLevel {
    NONE = 'none',               // No human intervention required
    NOTIFICATION = 'notification', // Notify human but proceed
    APPROVAL = 'approval',       // Require human approval before proceeding
    GUIDANCE = 'guidance',       // Request human guidance/input
    TAKEOVER = 'takeover'        // Human takes over the task completely
}

// Types of actions that might require human intervention
export enum ActionType {
    CODE_GENERATION = 'code_generation',
    CODE_MODIFICATION = 'code_modification',
    FILE_SYSTEM_ACCESS = 'file_system_access',
    EXTERNAL_API_CALL = 'external_api_call',
    SENSITIVE_DATA_ACCESS = 'sensitive_data_access',
    SECURITY_CRITICAL = 'security_critical',
    RESOURCE_INTENSIVE = 'resource_intensive',
    UNCERTAIN_OUTPUT = 'uncertain_output'
}

// Interface for human intervention request
export interface InterventionRequest {
    id: string;
    timestamp: number;
    actionType: ActionType;
    description: string;
    suggestedAction?: string;
    alternatives?: string[];
    context?: any;
    level: InterventionLevel;
    timeout?: number; // Timeout in milliseconds
}

// Interface for human intervention response
export interface InterventionResponse {
    requestId: string;
    timestamp: number;
    approved: boolean;
    feedback?: string;
    modifiedAction?: string;
    selectedAlternative?: number;
}

// Human-in-the-Loop Agent capabilities
export interface HumanInTheLoopCapabilities {
    defaultInterventionLevel: InterventionLevel;
    actionTypeSettings: Map<ActionType, InterventionLevel>;
    timeoutMs: number;
    allowAutoApprovalForLowRisk: boolean;
    collectFeedback: boolean;
    adaptiveMode: boolean; // Adjust intervention levels based on feedback
}

/**
 * Human-in-the-Loop Agent implementation
 */
export class HumanInTheLoopAgent extends BaseAgent {
    private pendingInterventions: Map<string, InterventionRequest> = new Map();
    private interventionResponses: Map<string, InterventionResponse> = new Map();
    private capabilities: HumanInTheLoopCapabilities;
    // Use protected instead of private to match the base class
    protected localDisposables: vscode.Disposable[] = [];

    constructor(
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        llmProvider: LanguageModelProvider,
        capabilities: HumanInTheLoopCapabilities
    ) {
        super(
            'human-in-the-loop',
            'Human-in-the-Loop Agent',
            'Manages human intervention in AI agent operations',
            workerPool,
            llmMonitor,
            llmProvider,
            {}
        );

        this.capabilities = capabilities;
        this.initialize();
    }

    private initialize(): void {
        // Register commands for human intervention
        this.localDisposables.push(
            vscode.commands.registerCommand('x10sion.approveAction', async (requestId: string) => {
                await this.handleApproval(requestId, true);
            }),
            vscode.commands.registerCommand('x10sion.rejectAction', async (requestId: string) => {
                await this.handleApproval(requestId, false);
            }),
            vscode.commands.registerCommand('x10sion.provideGuidance', async (requestId: string) => {
                await this.handleGuidance(requestId);
            })
        );

        // Add to base class disposables
        this.disposables.push(...this.localDisposables);
    }

    /**
     * Request human intervention for an action
     */
    public async requestIntervention(
        actionType: ActionType,
        description: string,
        suggestedAction?: string,
        alternatives?: string[],
        context?: any,
        level?: InterventionLevel
    ): Promise<InterventionResponse> {
        // Determine intervention level if not specified
        const interventionLevel = level || this.getInterventionLevelForAction(actionType);

        // If no intervention needed, return auto-approved response
        if (interventionLevel === InterventionLevel.NONE) {
            return {
                requestId: `auto-${Date.now()}`,
                timestamp: Date.now(),
                approved: true
            };
        }

        // Create intervention request
        const request: InterventionRequest = {
            id: `req-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            timestamp: Date.now(),
            actionType,
            description,
            suggestedAction,
            alternatives,
            context,
            level: interventionLevel,
            timeout: this.capabilities.timeoutMs
        };

        // Store the request
        this.pendingInterventions.set(request.id, request);

        // Handle the intervention based on level
        switch (interventionLevel) {
            case InterventionLevel.NOTIFICATION:
                await this.showNotification(request);
                return {
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved: true
                };

            case InterventionLevel.APPROVAL:
                return await this.requestApproval(request);

            case InterventionLevel.GUIDANCE:
                return await this.requestGuidance(request);

            case InterventionLevel.TAKEOVER:
                return await this.requestTakeover(request);

            default:
                return {
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved: false,
                    feedback: 'Unknown intervention level'
                };
        }
    }

    /**
     * Get the intervention level for a specific action type
     */
    private getInterventionLevelForAction(actionType: ActionType): InterventionLevel {
        return this.capabilities.actionTypeSettings.get(actionType) ||
               this.capabilities.defaultInterventionLevel;
    }

    /**
     * Show a notification to the user about an action
     */
    private async showNotification(request: InterventionRequest): Promise<void> {
        vscode.window.showInformationMessage(
            `X10sion AI is performing: ${request.description}`,
            'OK'
        );
    }

    /**
     * Request approval from the user for an action
     */
    private async requestApproval(request: InterventionRequest): Promise<InterventionResponse> {
        return new Promise<InterventionResponse>((resolve) => {
            // Set timeout handler
            const timeoutHandler = setTimeout(() => {
                // Auto-approve low-risk actions on timeout if configured
                const isLowRisk = this.isLowRiskAction(request.actionType);
                const approved = this.capabilities.allowAutoApprovalForLowRisk && isLowRisk;

                resolve({
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved,
                    feedback: approved ? 'Auto-approved (timeout)' : 'Auto-rejected (timeout)'
                });

                // Remove from pending interventions
                this.pendingInterventions.delete(request.id);
            }, request.timeout || this.capabilities.timeoutMs);

            // Show approval request
            vscode.window.showInformationMessage(
                `X10sion AI needs approval: ${request.description}`,
                'Approve',
                'Reject'
            ).then(async (selection) => {
                clearTimeout(timeoutHandler);

                const approved = selection === 'Approve';
                let feedback = '';

                if (this.capabilities.collectFeedback && approved) {
                    feedback = await vscode.window.showInputBox({
                        prompt: 'Optional feedback for this approval',
                        placeHolder: 'Enter feedback or leave empty'
                    }) || '';
                }

                const response: InterventionResponse = {
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved,
                    feedback
                };

                // Store the response
                this.interventionResponses.set(request.id, response);

                // Remove from pending interventions
                this.pendingInterventions.delete(request.id);

                // Resolve the promise
                resolve(response);

                // If adaptive mode is enabled, adjust intervention levels based on feedback
                if (this.capabilities.adaptiveMode) {
                    this.adjustInterventionLevels(request.actionType, approved);
                }
            });
        });
    }

    /**
     * Request guidance from the user
     */
    private async requestGuidance(request: InterventionRequest): Promise<InterventionResponse> {
        return new Promise<InterventionResponse>(async (resolve) => {
            // Show options if alternatives are provided
            if (request.alternatives && request.alternatives.length > 0) {
                const selection = await vscode.window.showQuickPick(
                    [request.suggestedAction || 'Suggested action', ...request.alternatives],
                    {
                        placeHolder: request.description,
                        canPickMany: false
                    }
                );

                if (!selection) {
                    // User cancelled
                    resolve({
                        requestId: request.id,
                        timestamp: Date.now(),
                        approved: false,
                        feedback: 'User cancelled guidance request'
                    });
                    return;
                }

                const selectedIndex = selection === request.suggestedAction ?
                    -1 : request.alternatives.indexOf(selection);

                resolve({
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved: true,
                    selectedAlternative: selectedIndex,
                    modifiedAction: selection
                });
            } else {
                // Request free-form guidance
                const guidance = await vscode.window.showInputBox({
                    prompt: request.description,
                    placeHolder: request.suggestedAction || 'Enter your guidance here'
                });

                if (guidance === undefined) {
                    // User cancelled
                    resolve({
                        requestId: request.id,
                        timestamp: Date.now(),
                        approved: false,
                        feedback: 'User cancelled guidance request'
                    });
                    return;
                }

                resolve({
                    requestId: request.id,
                    timestamp: Date.now(),
                    approved: true,
                    modifiedAction: guidance
                });
            }

            // Remove from pending interventions
            this.pendingInterventions.delete(request.id);
        });
    }

    /**
     * Request user to take over a task
     */
    private async requestTakeover(request: InterventionRequest): Promise<InterventionResponse> {
        // Show takeover request
        const result = await vscode.window.showInformationMessage(
            `X10sion AI needs you to take over: ${request.description}`,
            { modal: true },
            'Take Over',
            'Cancel'
        );

        if (result === 'Take Over') {
            // Open relevant files or UI for the user to take over
            if (request.context && request.context.filePath) {
                const document = await vscode.workspace.openTextDocument(request.context.filePath);
                await vscode.window.showTextDocument(document);
            }

            // Collect user feedback after takeover
            const feedback = await vscode.window.showInputBox({
                prompt: 'Please provide feedback on why you needed to take over',
                placeHolder: 'Enter feedback to help improve AI assistance'
            });

            return {
                requestId: request.id,
                timestamp: Date.now(),
                approved: false, // AI doesn't proceed since human took over
                feedback: feedback || 'User took over the task'
            };
        }

        // User cancelled takeover
        return {
            requestId: request.id,
            timestamp: Date.now(),
            approved: false,
            feedback: 'User cancelled takeover request'
        };
    }

    /**
     * Handle approval response from UI
     */
    private async handleApproval(requestId: string, approved: boolean): Promise<void> {
        const request = this.pendingInterventions.get(requestId);
        if (!request) {
            return;
        }

        let feedback = '';
        if (this.capabilities.collectFeedback) {
            feedback = await vscode.window.showInputBox({
                prompt: `Optional feedback for ${approved ? 'approval' : 'rejection'}`,
                placeHolder: 'Enter feedback or leave empty'
            }) || '';
        }

        const response: InterventionResponse = {
            requestId,
            timestamp: Date.now(),
            approved,
            feedback
        };

        this.interventionResponses.set(requestId, response);
        this.pendingInterventions.delete(requestId);

        // If adaptive mode is enabled, adjust intervention levels based on feedback
        if (this.capabilities.adaptiveMode) {
            this.adjustInterventionLevels(request.actionType, approved);
        }
    }

    /**
     * Handle guidance response from UI
     */
    private async handleGuidance(requestId: string): Promise<void> {
        const request = this.pendingInterventions.get(requestId);
        if (!request) {
            return;
        }

        const guidance = await vscode.window.showInputBox({
            prompt: request.description,
            placeHolder: request.suggestedAction || 'Enter your guidance here'
        });

        if (guidance === undefined) {
            // User cancelled
            return;
        }

        const response: InterventionResponse = {
            requestId,
            timestamp: Date.now(),
            approved: true,
            modifiedAction: guidance
        };

        this.interventionResponses.set(requestId, response);
        this.pendingInterventions.delete(requestId);
    }

    /**
     * Check if an action type is considered low risk
     */
    private isLowRiskAction(actionType: ActionType): boolean {
        const lowRiskActions = [
            ActionType.CODE_GENERATION, // Just generating code, not modifying
            ActionType.UNCERTAIN_OUTPUT // Uncertain but not harmful
        ];

        return lowRiskActions.includes(actionType);
    }

    /**
     * Adjust intervention levels based on user feedback (adaptive mode)
     */
    private adjustInterventionLevels(actionType: ActionType, approved: boolean): void {
        // This is a simple adaptive algorithm that could be enhanced with more sophisticated learning
        const currentLevel = this.capabilities.actionTypeSettings.get(actionType) ||
                            this.capabilities.defaultInterventionLevel;

        // If user consistently approves, gradually reduce intervention level
        if (approved) {
            if (currentLevel === InterventionLevel.APPROVAL &&
                this.getApprovalRate(actionType) > 0.8) { // 80% approval rate
                this.capabilities.actionTypeSettings.set(actionType, InterventionLevel.NOTIFICATION);
            } else if (currentLevel === InterventionLevel.GUIDANCE &&
                      this.getApprovalRate(actionType) > 0.9) { // 90% approval rate
                this.capabilities.actionTypeSettings.set(actionType, InterventionLevel.APPROVAL);
            }
        }
        // If user rejects, increase intervention level
        else {
            if (currentLevel === InterventionLevel.NOTIFICATION) {
                this.capabilities.actionTypeSettings.set(actionType, InterventionLevel.APPROVAL);
            } else if (currentLevel === InterventionLevel.APPROVAL) {
                this.capabilities.actionTypeSettings.set(actionType, InterventionLevel.GUIDANCE);
            }
        }
    }

    /**
     * Calculate approval rate for a specific action type
     */
    private getApprovalRate(actionType: ActionType): number {
        let approved = 0;
        let total = 0;

        // Count approvals for this action type in the last 10 interactions
        for (const [_, response] of this.interventionResponses) {
            const request = this.pendingInterventions.get(response.requestId);
            if (request && request.actionType === actionType) {
                total++;
                if (response.approved) {
                    approved++;
                }

                // Only consider the last 10 interactions
                if (total >= 10) {
                    break;
                }
            }
        }

        return total > 0 ? approved / total : 0;
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
    }
}
