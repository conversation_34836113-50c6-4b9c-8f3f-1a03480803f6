/**
 * Prompt Analyzer for Prompt Enhancement Agent
 * 
 * Handles analysis of prompts to understand intent, extract keywords, and determine complexity.
 */

import { TaskPriority } from '../../parallel/worker-pool.js';

/**
 * Prompt Analysis Result
 */
export interface PromptAnalysisResult {
    intent: string;
    keywords: string[];
    topics: string[];
    complexity: 'simple' | 'moderate' | 'complex';
    requiredContext: string[];
    suggestedTools: string[];
}

/**
 * Prompt Analyzer class
 */
export class PromptAnalyzer {
    private stopWords: Set<string>;

    constructor() {
        this.stopWords = new Set([
            'the', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with',
            'by', 'about', 'as', 'of', 'from', 'is', 'are', 'was', 'were',
            'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'should', 'can', 'could', 'may', 'might', 'must',
            'this', 'that', 'these', 'those', 'it', 'its', 'they', 'them',
            'their', 'he', 'him', 'his', 'she', 'her', 'hers', 'we', 'us', 'our'
        ]);
    }

    /**
     * Analyze a prompt to understand intent, extract keywords, etc.
     */
    async analyzePrompt(prompt: string): Promise<PromptAnalysisResult> {
        // Extract keywords
        const keywords = this.extractKeywords(prompt);
        
        // Determine intent
        const intent = this.determineIntent(prompt);
        
        // Determine complexity
        const complexity = this.determineComplexity(prompt);
        
        // Determine required context
        const requiredContext = this.determineRequiredContext(prompt);
        
        // Determine suggested tools
        const suggestedTools = this.determineSuggestedTools(prompt);
        
        // Extract topics (top keywords)
        const topics = keywords.slice(0, 3);

        return {
            intent,
            keywords,
            topics,
            complexity,
            requiredContext,
            suggestedTools
        };
    }

    /**
     * Extract keywords from prompt
     */
    private extractKeywords(prompt: string): string[] {
        const words = prompt.toLowerCase().split(/\s+/);
        const wordFrequency: Record<string, number> = {};

        for (const word of words) {
            // Skip common words and punctuation
            if (word.length < 3 || this.stopWords.has(word)) {
                continue;
            }

            // Clean word (remove punctuation)
            const cleanWord = word.replace(/[^\w]/g, '');
            if (cleanWord.length < 3) {
                continue;
            }

            wordFrequency[cleanWord] = (wordFrequency[cleanWord] || 0) + 1;
        }

        // Sort by frequency and return top keywords
        return Object.entries(wordFrequency)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(entry => entry[0]);
    }

    /**
     * Determine the intent of the prompt
     */
    private determineIntent(prompt: string): string {
        const promptLower = prompt.toLowerCase();

        // Define intent patterns
        const intentPatterns = [
            { intent: 'explanation', patterns: [/explain|describe|what is|how does|tell me about/i] },
            { intent: 'code_generation', patterns: [/create|generate|write|implement|code|build|develop/i] },
            { intent: 'debugging', patterns: [/fix|debug|error|issue|problem|bug|troubleshoot/i] },
            { intent: 'code_review', patterns: [/review|improve|optimize|refactor|analyze|check/i] },
            { intent: 'documentation', patterns: [/document|docs|readme|comment|annotate/i] },
            { intent: 'testing', patterns: [/test|unit test|integration test|spec|verify/i] },
            { intent: 'search', patterns: [/find|search|look for|locate|discover/i] },
            { intent: 'comparison', patterns: [/compare|versus|vs|difference|better|best/i] },
            { intent: 'tutorial', patterns: [/how to|tutorial|guide|step by step|walkthrough/i] }
        ];

        // Check each pattern
        for (const { intent, patterns } of intentPatterns) {
            if (patterns.some(pattern => pattern.test(promptLower))) {
                return intent;
            }
        }

        return 'general';
    }

    /**
     * Determine the complexity of the prompt
     */
    private determineComplexity(prompt: string): 'simple' | 'moderate' | 'complex' {
        // Factors that indicate complexity
        let complexityScore = 0;

        // Length factor
        if (prompt.length > 300) {
            complexityScore += 2;
        } else if (prompt.length > 150) {
            complexityScore += 1;
        }

        // Technical terms factor
        const technicalTerms = [
            'algorithm', 'architecture', 'framework', 'library', 'database',
            'api', 'microservice', 'deployment', 'optimization', 'performance',
            'security', 'authentication', 'authorization', 'encryption',
            'machine learning', 'artificial intelligence', 'neural network'
        ];
        
        const technicalTermCount = technicalTerms.filter(term => 
            prompt.toLowerCase().includes(term)
        ).length;
        
        complexityScore += Math.min(technicalTermCount, 3);

        // Multiple requirements factor
        const requirementIndicators = ['and', 'also', 'additionally', 'furthermore', 'moreover'];
        const requirementCount = requirementIndicators.filter(indicator =>
            prompt.toLowerCase().includes(indicator)
        ).length;
        
        complexityScore += Math.min(requirementCount, 2);

        // Question complexity factor
        const questionWords = prompt.toLowerCase().match(/\b(what|how|why|when|where|which)\b/g);
        if (questionWords && questionWords.length > 2) {
            complexityScore += 1;
        }

        // Determine complexity level
        if (complexityScore >= 4) {
            return 'complex';
        } else if (complexityScore >= 2) {
            return 'moderate';
        } else {
            return 'simple';
        }
    }

    /**
     * Determine required context types
     */
    private determineRequiredContext(prompt: string): string[] {
        const promptLower = prompt.toLowerCase();
        const requiredContext: string[] = [];

        const contextPatterns = [
            { type: 'code', patterns: [/code|function|class|method|implementation|syntax/] },
            { type: 'documentation', patterns: [/documentation|docs|readme|manual|guide/] },
            { type: 'error_logs', patterns: [/error|exception|stack trace|log|crash/] },
            { type: 'configuration', patterns: [/config|settings|environment|setup/] },
            { type: 'dependencies', patterns: [/dependency|package|library|import|require/] },
            { type: 'database', patterns: [/database|sql|query|table|schema/] },
            { type: 'api', patterns: [/api|endpoint|request|response|rest|graphql/] },
            { type: 'testing', patterns: [/test|spec|mock|stub|assertion/] },
            { type: 'performance', patterns: [/performance|benchmark|profiling|optimization/] },
            { type: 'security', patterns: [/security|vulnerability|authentication|authorization/] }
        ];

        for (const { type, patterns } of contextPatterns) {
            if (patterns.some(pattern => pattern.test(promptLower))) {
                requiredContext.push(type);
            }
        }

        return requiredContext;
    }

    /**
     * Determine suggested tools
     */
    private determineSuggestedTools(prompt: string): string[] {
        const promptLower = prompt.toLowerCase();
        const suggestedTools: string[] = [];

        const toolPatterns = [
            { tool: 'search', patterns: [/search|find|look for|locate|discover/] },
            { tool: 'shell_command', patterns: [/run|execute|command|terminal|shell/] },
            { tool: 'file_system', patterns: [/file|read|write|save|create|delete/] },
            { tool: 'code_analysis', patterns: [/analyze|parse|ast|syntax tree/] },
            { tool: 'git', patterns: [/git|commit|branch|merge|repository/] },
            { tool: 'package_manager', patterns: [/install|npm|pip|cargo|maven/] },
            { tool: 'database', patterns: [/database|sql|query|migrate/] },
            { tool: 'web_scraping', patterns: [/scrape|crawl|extract|web data/] },
            { tool: 'api_client', patterns: [/api call|http request|rest|graphql/] },
            { tool: 'testing', patterns: [/test|unit test|integration test/] }
        ];

        for (const { tool, patterns } of toolPatterns) {
            if (patterns.some(pattern => pattern.test(promptLower))) {
                suggestedTools.push(tool);
            }
        }

        return suggestedTools;
    }

    /**
     * Extract entities from prompt (named entities, technical terms, etc.)
     */
    extractEntities(prompt: string): {
        technologies: string[];
        languages: string[];
        frameworks: string[];
        concepts: string[];
    } {
        const promptLower = prompt.toLowerCase();

        const technologies = [
            'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'terraform',
            'jenkins', 'github', 'gitlab', 'bitbucket', 'jira'
        ].filter(tech => promptLower.includes(tech));

        const languages = [
            'javascript', 'typescript', 'python', 'java', 'c#', 'c++',
            'go', 'rust', 'ruby', 'php', 'swift', 'kotlin', 'scala'
        ].filter(lang => promptLower.includes(lang));

        const frameworks = [
            'react', 'angular', 'vue', 'express', 'fastapi', 'django',
            'spring', 'laravel', 'rails', 'flutter', 'xamarin'
        ].filter(framework => promptLower.includes(framework));

        const concepts = [
            'microservices', 'monolith', 'serverless', 'devops', 'cicd',
            'machine learning', 'artificial intelligence', 'blockchain',
            'cryptocurrency', 'iot', 'edge computing'
        ].filter(concept => promptLower.includes(concept));

        return {
            technologies,
            languages,
            frameworks,
            concepts
        };
    }

    /**
     * Analyze prompt sentiment and tone
     */
    analyzeSentiment(prompt: string): {
        sentiment: 'positive' | 'neutral' | 'negative';
        urgency: 'low' | 'medium' | 'high';
        formality: 'casual' | 'professional' | 'formal';
    } {
        const promptLower = prompt.toLowerCase();

        // Sentiment analysis
        const positiveWords = ['good', 'great', 'excellent', 'awesome', 'perfect', 'love'];
        const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'broken', 'failed'];
        
        const positiveCount = positiveWords.filter(word => promptLower.includes(word)).length;
        const negativeCount = negativeWords.filter(word => promptLower.includes(word)).length;
        
        let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
        if (positiveCount > negativeCount) {
            sentiment = 'positive';
        } else if (negativeCount > positiveCount) {
            sentiment = 'negative';
        }

        // Urgency analysis
        const urgentWords = ['urgent', 'asap', 'immediately', 'critical', 'emergency', 'now'];
        const urgentCount = urgentWords.filter(word => promptLower.includes(word)).length;
        
        let urgency: 'low' | 'medium' | 'high' = 'low';
        if (urgentCount > 0 || prompt.includes('!')) {
            urgency = 'high';
        } else if (promptLower.includes('soon') || promptLower.includes('quickly')) {
            urgency = 'medium';
        }

        // Formality analysis
        const casualWords = ['hey', 'hi', 'thanks', 'cool', 'awesome'];
        const formalWords = ['please', 'kindly', 'respectfully', 'sincerely'];
        
        const casualCount = casualWords.filter(word => promptLower.includes(word)).length;
        const formalCount = formalWords.filter(word => promptLower.includes(word)).length;
        
        let formality: 'casual' | 'professional' | 'formal' = 'professional';
        if (casualCount > formalCount) {
            formality = 'casual';
        } else if (formalCount > 0) {
            formality = 'formal';
        }

        return {
            sentiment,
            urgency,
            formality
        };
    }
}
