/**
 * Context Prioritizer for Prompt Enhancement Agent
 * 
 * Handles prioritization and scoring of context items based on relevance.
 */

import { PromptAnalysisResult } from './prompt-analyzer.js';

/**
 * Context Item with Relevance Score
 */
export interface ScoredContextItem {
    id: string;
    content: string;
    relevanceScore: number;
    tokenCount: number;
    type: string;
    metadata?: Record<string, any>;
}

/**
 * Context Prioritization Strategy
 */
export type ContextPrioritizationStrategy = 'relevance' | 'recency' | 'hybrid' | 'importance' | 'similarity';

/**
 * Context Prioritizer Options
 */
export interface ContextPrioritizerOptions {
    strategy: ContextPrioritizationStrategy;
    minRelevanceScore: number;
    keywordWeight: number;
    typeWeight: number;
    recencyWeight: number;
    importanceWeight: number;
}

/**
 * Context Prioritizer class
 */
export class ContextPrioritizer {
    private options: ContextPrioritizerOptions;

    constructor(options: Partial<ContextPrioritizerOptions> = {}) {
        this.options = {
            strategy: 'hybrid',
            minRelevanceScore: 0.5,
            keywordWeight: 0.4,
            typeWeight: 0.3,
            recencyWeight: 0.2,
            importanceWeight: 0.1,
            ...options
        };
    }

    /**
     * Prioritize context based on relevance to the prompt
     */
    async prioritizeContext(
        prompt: string,
        analysis: PromptAnalysisResult,
        contextItems: any[]
    ): Promise<ScoredContextItem[]> {
        // Convert context items to standardized format
        const standardizedItems = this.standardizeContextItems(contextItems);

        // Calculate relevance scores
        this.calculateRelevanceScores(standardizedItems, prompt, analysis);

        // Sort based on strategy
        this.sortByStrategy(standardizedItems);

        // Filter out items below minimum relevance threshold
        return standardizedItems.filter(item =>
            item.relevanceScore >= this.options.minRelevanceScore
        );
    }

    /**
     * Convert context items to standardized format
     */
    private standardizeContextItems(contextItems: any[]): ScoredContextItem[] {
        return contextItems.map(item => {
            if ('relevanceScore' in item) {
                return item as ScoredContextItem;
            }

            // Create a standardized item
            return {
                id: item.id || `ctx-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                content: item.content || item.text || item.toString(),
                relevanceScore: 0,
                tokenCount: this.estimateTokenCount(item.content || item.text || item.toString()),
                type: item.type || 'unknown',
                metadata: item.metadata || {}
            };
        });
    }

    /**
     * Calculate relevance scores for context items
     */
    private calculateRelevanceScores(
        items: ScoredContextItem[],
        prompt: string,
        analysis: PromptAnalysisResult
    ): void {
        for (const item of items) {
            let score = 0;

            // Keyword matching score
            const keywordScore = this.calculateKeywordScore(item, analysis.keywords);
            score += keywordScore * this.options.keywordWeight;

            // Context type score
            const typeScore = this.calculateTypeScore(item, analysis.requiredContext);
            score += typeScore * this.options.typeWeight;

            // Recency score
            const recencyScore = this.calculateRecencyScore(item);
            score += recencyScore * this.options.recencyWeight;

            // Importance score
            const importanceScore = this.calculateImportanceScore(item);
            score += importanceScore * this.options.importanceWeight;

            // Semantic similarity score (simplified)
            const similarityScore = this.calculateSimilarityScore(item, prompt);
            score += similarityScore * 0.2; // Additional weight for similarity

            // Ensure score is between 0 and 1
            item.relevanceScore = Math.min(1, Math.max(0, score));
        }
    }

    /**
     * Calculate keyword matching score
     */
    private calculateKeywordScore(item: ScoredContextItem, keywords: string[]): number {
        if (keywords.length === 0) {
            return 0;
        }

        const content = item.content.toLowerCase();
        let matches = 0;
        let totalWeight = 0;

        for (let i = 0; i < keywords.length; i++) {
            const keyword = keywords[i].toLowerCase();
            const weight = 1 / (i + 1); // Higher weight for more important keywords
            totalWeight += weight;

            if (content.includes(keyword)) {
                // Count occurrences for better scoring
                const occurrences = (content.match(new RegExp(keyword, 'g')) || []).length;
                matches += Math.min(occurrences, 3) * weight; // Cap at 3 occurrences
            }
        }

        return totalWeight > 0 ? matches / totalWeight : 0;
    }

    /**
     * Calculate context type score
     */
    private calculateTypeScore(item: ScoredContextItem, requiredContext: string[]): number {
        if (requiredContext.length === 0) {
            return 0.5; // Neutral score if no specific context required
        }

        // Exact match
        if (requiredContext.includes(item.type)) {
            return 1.0;
        }

        // Partial match based on type similarity
        const typeScore = this.getTypeSimilarity(item.type, requiredContext);
        return typeScore;
    }

    /**
     * Calculate recency score
     */
    private calculateRecencyScore(item: ScoredContextItem): number {
        const timestamp = item.metadata?.timestamp;
        if (!timestamp) {
            return 0.5; // Neutral score if no timestamp
        }

        const ageInHours = (Date.now() - timestamp) / (1000 * 60 * 60);
        
        // Score decreases with age
        if (ageInHours < 1) {
            return 1.0; // Very recent
        } else if (ageInHours < 24) {
            return 0.8; // Recent
        } else if (ageInHours < 168) { // 1 week
            return 0.6; // Somewhat recent
        } else if (ageInHours < 720) { // 1 month
            return 0.4; // Old
        } else {
            return 0.2; // Very old
        }
    }

    /**
     * Calculate importance score
     */
    private calculateImportanceScore(item: ScoredContextItem): number {
        const importance = item.metadata?.importance;
        if (typeof importance === 'number') {
            return Math.min(1, Math.max(0, importance));
        }

        if (typeof importance === 'string') {
            switch (importance.toLowerCase()) {
                case 'critical':
                case 'high':
                    return 1.0;
                case 'medium':
                case 'normal':
                    return 0.6;
                case 'low':
                    return 0.3;
                default:
                    return 0.5;
            }
        }

        // Default importance based on content type
        switch (item.type) {
            case 'error':
            case 'critical':
                return 0.9;
            case 'warning':
                return 0.7;
            case 'info':
                return 0.5;
            case 'debug':
                return 0.3;
            default:
                return 0.5;
        }
    }

    /**
     * Calculate semantic similarity score (simplified)
     */
    private calculateSimilarityScore(item: ScoredContextItem, prompt: string): number {
        const promptWords = new Set(prompt.toLowerCase().split(/\s+/));
        const contentWords = new Set(item.content.toLowerCase().split(/\s+/));

        // Calculate Jaccard similarity
        const intersection = new Set([...promptWords].filter(word => contentWords.has(word)));
        const union = new Set([...promptWords, ...contentWords]);

        return union.size > 0 ? intersection.size / union.size : 0;
    }

    /**
     * Get type similarity score
     */
    private getTypeSimilarity(itemType: string, requiredTypes: string[]): number {
        // Define type relationships
        const typeRelationships: Record<string, string[]> = {
            'code': ['function', 'class', 'method', 'implementation', 'source'],
            'documentation': ['readme', 'docs', 'manual', 'guide', 'comment'],
            'error': ['exception', 'bug', 'issue', 'problem', 'failure'],
            'test': ['spec', 'unit_test', 'integration_test', 'e2e_test'],
            'config': ['configuration', 'settings', 'environment', 'setup'],
            'api': ['endpoint', 'service', 'interface', 'contract'],
            'database': ['schema', 'table', 'query', 'migration'],
            'log': ['trace', 'debug', 'info', 'warning', 'error']
        };

        let maxSimilarity = 0;

        for (const requiredType of requiredTypes) {
            // Direct match
            if (itemType === requiredType) {
                return 1.0;
            }

            // Check relationships
            const relatedTypes = typeRelationships[requiredType] || [];
            if (relatedTypes.includes(itemType)) {
                maxSimilarity = Math.max(maxSimilarity, 0.7);
            }

            // Check reverse relationships
            const itemRelatedTypes = typeRelationships[itemType] || [];
            if (itemRelatedTypes.includes(requiredType)) {
                maxSimilarity = Math.max(maxSimilarity, 0.7);
            }

            // Partial string matching
            if (itemType.includes(requiredType) || requiredType.includes(itemType)) {
                maxSimilarity = Math.max(maxSimilarity, 0.5);
            }
        }

        return maxSimilarity;
    }

    /**
     * Sort items based on prioritization strategy
     */
    private sortByStrategy(items: ScoredContextItem[]): void {
        switch (this.options.strategy) {
            case 'relevance':
                items.sort((a, b) => b.relevanceScore - a.relevanceScore);
                break;

            case 'recency':
                items.sort((a, b) => {
                    const aTime = a.metadata?.timestamp || 0;
                    const bTime = b.metadata?.timestamp || 0;
                    return bTime - aTime;
                });
                break;

            case 'importance':
                items.sort((a, b) => {
                    const aImportance = this.calculateImportanceScore(a);
                    const bImportance = this.calculateImportanceScore(b);
                    return bImportance - aImportance;
                });
                break;

            case 'similarity':
                // Already incorporated into relevance score
                items.sort((a, b) => b.relevanceScore - a.relevanceScore);
                break;

            case 'hybrid':
            default:
                // Use the calculated relevance score which already incorporates multiple factors
                items.sort((a, b) => b.relevanceScore - a.relevanceScore);
                break;
        }
    }

    /**
     * Estimate token count for text (simplified)
     */
    private estimateTokenCount(text: string): number {
        // Rough estimation: 1 token ≈ 4 characters for English text
        return Math.ceil(text.length / 4);
    }

    /**
     * Filter context items by type
     */
    filterByType(items: ScoredContextItem[], types: string[]): ScoredContextItem[] {
        return items.filter(item => types.includes(item.type));
    }

    /**
     * Filter context items by minimum score
     */
    filterByScore(items: ScoredContextItem[], minScore: number): ScoredContextItem[] {
        return items.filter(item => item.relevanceScore >= minScore);
    }

    /**
     * Get top N context items
     */
    getTopItems(items: ScoredContextItem[], count: number): ScoredContextItem[] {
        return items.slice(0, count);
    }

    /**
     * Update prioritization options
     */
    updateOptions(options: Partial<ContextPrioritizerOptions>): void {
        this.options = { ...this.options, ...options };
    }
}
