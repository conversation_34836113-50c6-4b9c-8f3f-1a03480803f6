/**
 * Token Manager for Prompt Enhancement Agent
 * 
 * Handles token counting, budget management, and content compression.
 */

import { ScoredContextItem } from './context-prioritizer.js';

/**
 * Token Budget Allocation
 */
export interface TokenBudget {
    total: number;
    prompt: number;
    context: number;
    response: number;
    remaining: number;
}

/**
 * Token Management Options
 */
export interface TokenManagerOptions {
    defaultTokenBudget: number;
    promptAllocation: number; // Percentage of total budget
    responseAllocation: number; // Percentage of total budget
    enableCompression: boolean;
    compressionThreshold: number; // Minimum compression ratio
    minUsefulTokens: number; // Minimum tokens for content to be useful
}

/**
 * Token Manager class
 */
export class TokenManager {
    private options: TokenManagerOptions;

    constructor(options: Partial<TokenManagerOptions> = {}) {
        this.options = {
            defaultTokenBudget: 4000,
            promptAllocation: 0.1, // 10%
            responseAllocation: 0.3, // 30%
            enableCompression: true,
            compressionThreshold: 0.3, // Don't compress more than 70%
            minUsefulTokens: 50,
            ...options
        };
    }

    /**
     * Count tokens in text (simplified estimation)
     */
    countTokens(text: string): number {
        // Rough estimation based on character count
        // More sophisticated tokenizers would be used in production
        
        // Remove extra whitespace
        const cleanText = text.trim().replace(/\s+/g, ' ');
        
        // Estimate tokens
        // English: ~4 characters per token
        // Code: ~3 characters per token (more symbols)
        const isCode = this.isCodeContent(text);
        const charsPerToken = isCode ? 3 : 4;
        
        return Math.ceil(cleanText.length / charsPerToken);
    }

    /**
     * Manage token budget for prompt, context, and expected response
     */
    manageTokenBudget(
        prompt: string,
        contextItems: ScoredContextItem[],
        maxTokens?: number
    ): TokenBudget {
        // Determine total token budget
        const totalBudget = maxTokens || this.options.defaultTokenBudget;

        // Count tokens in the prompt
        const promptTokens = this.countTokens(prompt);

        // Calculate total tokens in all context items
        const totalContextTokens = contextItems.reduce((sum, item) => sum + item.tokenCount, 0);

        // Allocate budget
        const minPromptBudget = Math.max(
            promptTokens, 
            Math.floor(totalBudget * this.options.promptAllocation)
        );
        const responseBudget = Math.floor(totalBudget * this.options.responseAllocation);
        const contextBudget = totalBudget - minPromptBudget - responseBudget;

        return {
            total: totalBudget,
            prompt: minPromptBudget,
            context: Math.max(0, contextBudget),
            response: responseBudget,
            remaining: Math.max(0, contextBudget - Math.min(contextBudget, totalContextTokens))
        };
    }

    /**
     * Select context items that fit within the token budget
     */
    selectContextWithinBudget(
        contextItems: ScoredContextItem[],
        tokenBudget: number
    ): ScoredContextItem[] {
        const selectedItems: ScoredContextItem[] = [];
        let usedTokens = 0;

        // Sort items by relevance score (highest first)
        const sortedItems = [...contextItems].sort((a, b) => b.relevanceScore - a.relevanceScore);

        // Add items until we reach the budget
        for (const item of sortedItems) {
            if (usedTokens + item.tokenCount <= tokenBudget) {
                selectedItems.push(item);
                usedTokens += item.tokenCount;
            } else if (this.options.enableCompression) {
                // Try to compress the item to fit
                const availableTokens = tokenBudget - usedTokens;
                const compressedItem = this.compressContextItem(item, availableTokens);
                if (compressedItem) {
                    selectedItems.push(compressedItem);
                    usedTokens += compressedItem.tokenCount;
                    break; // Stop after compression as we've likely filled the budget
                }
            }
        }

        return selectedItems;
    }

    /**
     * Compress a context item to fit within a token budget
     */
    compressContextItem(
        item: ScoredContextItem,
        availableTokens: number
    ): ScoredContextItem | null {
        if (availableTokens < this.options.minUsefulTokens) {
            // Not enough tokens to be useful
            return null;
        }

        // Calculate compression ratio
        const compressionRatio = availableTokens / item.tokenCount;
        if (compressionRatio < this.options.compressionThreshold) {
            // Too much compression would lose too much information
            return null;
        }

        // Choose compression strategy based on content type
        let compressedContent: string;
        
        switch (item.type) {
            case 'code':
                compressedContent = this.compressCode(item.content, availableTokens);
                break;
            case 'documentation':
                compressedContent = this.compressDocumentation(item.content, availableTokens);
                break;
            case 'error':
            case 'log':
                compressedContent = this.compressLog(item.content, availableTokens);
                break;
            default:
                compressedContent = this.compressGeneral(item.content, availableTokens);
                break;
        }

        return {
            ...item,
            content: compressedContent,
            tokenCount: this.countTokens(compressedContent),
            metadata: {
                ...item.metadata,
                compressed: true,
                originalTokenCount: item.tokenCount,
                compressionRatio: this.countTokens(compressedContent) / item.tokenCount
            }
        };
    }

    /**
     * Compress code content
     */
    private compressCode(content: string, targetTokens: number): string {
        const lines = content.split('\n');
        
        // Remove comments and empty lines first
        const codeLines = lines.filter(line => {
            const trimmed = line.trim();
            return trimmed.length > 0 && 
                   !trimmed.startsWith('//') && 
                   !trimmed.startsWith('/*') && 
                   !trimmed.startsWith('*') &&
                   !trimmed.startsWith('#');
        });

        // If still too long, truncate intelligently
        if (this.countTokens(codeLines.join('\n')) > targetTokens) {
            // Keep function signatures and important declarations
            const importantLines = codeLines.filter(line => {
                const trimmed = line.trim();
                return trimmed.includes('function') ||
                       trimmed.includes('class') ||
                       trimmed.includes('interface') ||
                       trimmed.includes('export') ||
                       trimmed.includes('import') ||
                       trimmed.includes('const') ||
                       trimmed.includes('let') ||
                       trimmed.includes('var');
            });

            // Add some context lines
            const contextLines = codeLines.slice(0, Math.floor(targetTokens / 10));
            const combined = [...new Set([...importantLines, ...contextLines])];
            
            return this.truncateToTokenCount(combined.join('\n'), targetTokens);
        }

        return codeLines.join('\n');
    }

    /**
     * Compress documentation content
     */
    private compressDocumentation(content: string, targetTokens: number): string {
        // Split into paragraphs
        const paragraphs = content.split('\n\n').filter(p => p.trim().length > 0);
        
        // Prioritize paragraphs with headings or important keywords
        const prioritizedParagraphs = paragraphs.sort((a, b) => {
            const aScore = this.getDocumentationPriority(a);
            const bScore = this.getDocumentationPriority(b);
            return bScore - aScore;
        });

        // Add paragraphs until we reach the token limit
        let result = '';
        for (const paragraph of prioritizedParagraphs) {
            const testResult = result + (result ? '\n\n' : '') + paragraph;
            if (this.countTokens(testResult) <= targetTokens) {
                result = testResult;
            } else {
                break;
            }
        }

        return result || this.truncateToTokenCount(content, targetTokens);
    }

    /**
     * Compress log content
     */
    private compressLog(content: string, targetTokens: number): string {
        const lines = content.split('\n');
        
        // Prioritize error and warning lines
        const prioritizedLines = lines.sort((a, b) => {
            const aScore = this.getLogPriority(a);
            const bScore = this.getLogPriority(b);
            return bScore - aScore;
        });

        // Take the most important lines
        let result = '';
        for (const line of prioritizedLines) {
            const testResult = result + (result ? '\n' : '') + line;
            if (this.countTokens(testResult) <= targetTokens) {
                result = testResult;
            } else {
                break;
            }
        }

        return result || this.truncateToTokenCount(content, targetTokens);
    }

    /**
     * Compress general content
     */
    private compressGeneral(content: string, targetTokens: number): string {
        // Simple truncation with sentence boundary awareness
        return this.truncateToTokenCount(content, targetTokens);
    }

    /**
     * Truncate content to fit within token count
     */
    truncateToTokenCount(content: string, maxTokens: number): string {
        if (this.countTokens(content) <= maxTokens) {
            return content;
        }

        // Estimate character limit
        const avgCharsPerToken = content.length / this.countTokens(content);
        const maxChars = Math.floor(maxTokens * avgCharsPerToken * 0.9); // 10% buffer

        if (content.length <= maxChars) {
            return content;
        }

        // Truncate at sentence boundary if possible
        const truncated = content.substring(0, maxChars);
        const lastSentence = truncated.lastIndexOf('.');
        const lastNewline = truncated.lastIndexOf('\n');
        
        const cutPoint = Math.max(lastSentence, lastNewline);
        if (cutPoint > maxChars * 0.7) { // Don't cut too much
            return truncated.substring(0, cutPoint + 1) + '...';
        }

        return truncated + '...';
    }

    /**
     * Check if content appears to be code
     */
    private isCodeContent(content: string): boolean {
        const codeIndicators = [
            '{', '}', '(', ')', ';', 'function', 'class', 'import', 'export',
            'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return'
        ];

        const indicatorCount = codeIndicators.filter(indicator => 
            content.includes(indicator)
        ).length;

        return indicatorCount >= 3;
    }

    /**
     * Get priority score for documentation paragraphs
     */
    private getDocumentationPriority(paragraph: string): number {
        let score = 0;
        
        // Headings get high priority
        if (paragraph.startsWith('#') || paragraph.includes('##')) {
            score += 10;
        }

        // Important keywords
        const importantKeywords = ['important', 'note', 'warning', 'example', 'usage'];
        for (const keyword of importantKeywords) {
            if (paragraph.toLowerCase().includes(keyword)) {
                score += 5;
            }
        }

        // Code blocks get medium priority
        if (paragraph.includes('```') || paragraph.includes('`')) {
            score += 3;
        }

        return score;
    }

    /**
     * Get priority score for log lines
     */
    private getLogPriority(line: string): number {
        const lineLower = line.toLowerCase();
        
        if (lineLower.includes('error') || lineLower.includes('exception')) {
            return 10;
        }
        if (lineLower.includes('warning') || lineLower.includes('warn')) {
            return 7;
        }
        if (lineLower.includes('info')) {
            return 5;
        }
        if (lineLower.includes('debug')) {
            return 2;
        }
        
        return 1;
    }

    /**
     * Update token manager options
     */
    updateOptions(options: Partial<TokenManagerOptions>): void {
        this.options = { ...this.options, ...options };
    }

    /**
     * Get current options
     */
    getOptions(): TokenManagerOptions {
        return { ...this.options };
    }
}
