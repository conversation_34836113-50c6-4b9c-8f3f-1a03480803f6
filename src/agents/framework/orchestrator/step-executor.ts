/**
 * Step Executor for Agent Orchestrator
 * 
 * Handles the execution of individual workflow steps.
 */

import * as vscode from 'vscode';
import { WorkflowStep, Workflow, StepResult } from './types.js';
import { X10sionAgentSystem } from '../agent-system.js';

/**
 * Step Executor class
 */
export class StepExecutor {
    private agentSystem: X10sionAgentSystem;
    private eventEmitter: vscode.EventEmitter<any>;

    constructor(agentSystem: X10sionAgentSystem, eventEmitter: vscode.EventEmitter<any>) {
        this.agentSystem = agentSystem;
        this.eventEmitter = eventEmitter;
    }

    /**
     * Execute a workflow step
     */
    async executeStep(
        step: WorkflowStep,
        context: any,
        workflow: Workflow
    ): Promise<StepResult> {
        const agent = this.agentSystem.getAgent(step.agentId);
        if (!agent) {
            throw new Error(`Agent ${step.agentId} not found`);
        }

        const startTime = Date.now();

        // Emit event
        this.eventEmitter.fire({
            type: 'step_started',
            stepId: step.id,
            agentId: step.agentId,
            workflowId: workflow.id
        });

        try {
            // Execute task with timeout
            const timeoutMs = step.timeout || workflow.timeout || 30000;
            const result = await this.executeWithTimeout(
                () => agent.execute(step.task, {
                    ...context,
                    stepId: step.id,
                    workflowId: workflow.id
                }),
                timeoutMs
            );

            const endTime = Date.now();

            // Emit event
            this.eventEmitter.fire({
                type: 'step_completed',
                stepId: step.id,
                agentId: step.agentId,
                workflowId: workflow.id,
                result
            });

            return {
                stepId: step.id,
                agentId: step.agentId,
                result,
                status: 'completed',
                startTime,
                endTime
            };
        } catch (error) {
            const endTime = Date.now();

            // Emit event
            this.eventEmitter.fire({
                type: 'step_failed',
                stepId: step.id,
                agentId: step.agentId,
                workflowId: workflow.id,
                error
            });

            return {
                stepId: step.id,
                agentId: step.agentId,
                result: null,
                error: error as Error,
                status: 'failed',
                startTime,
                endTime
            };
        }
    }

    /**
     * Execute a function with a timeout
     */
    private async executeWithTimeout<T>(fn: () => Promise<T>, timeoutMs: number): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Execution timed out after ${timeoutMs}ms`));
            }, timeoutMs);

            fn()
                .then(result => {
                    clearTimeout(timeoutId);
                    resolve(result);
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    reject(error);
                });
        });
    }

    /**
     * Execute multiple steps in parallel with concurrency control
     */
    async executeStepsInParallel(
        steps: WorkflowStep[],
        context: any,
        workflow: Workflow,
        maxConcurrency: number = steps.length
    ): Promise<Map<string, StepResult>> {
        const results = new Map<string, StepResult>();
        const semaphore = new Semaphore(maxConcurrency);

        const stepPromises = steps.map(async (step) => {
            await semaphore.acquire();
            try {
                const result = await this.executeStep(step, context, workflow);
                results.set(step.id, result);
                return result;
            } finally {
                semaphore.release();
            }
        });

        await Promise.all(stepPromises);
        return results;
    }

    /**
     * Execute steps with dependency resolution
     */
    async executeStepsWithDependencies(
        steps: WorkflowStep[],
        context: any,
        workflow: Workflow
    ): Promise<Map<string, StepResult>> {
        const results = new Map<string, StepResult>();
        const completed = new Set<string>();
        const inProgress = new Set<string>();

        const executeStepWithDeps = async (step: WorkflowStep): Promise<StepResult> => {
            // Check if already completed or in progress
            if (completed.has(step.id)) {
                return results.get(step.id)!;
            }
            if (inProgress.has(step.id)) {
                throw new Error(`Circular dependency detected involving step ${step.id}`);
            }

            inProgress.add(step.id);

            try {
                // Wait for dependencies to complete
                if (step.dependencies && step.dependencies.length > 0) {
                    const dependencySteps = steps.filter(s => step.dependencies!.includes(s.id));
                    await Promise.all(dependencySteps.map(depStep => executeStepWithDeps(depStep)));
                }

                // Execute the step
                const result = await this.executeStep(step, context, workflow);
                results.set(step.id, result);
                completed.add(step.id);
                inProgress.delete(step.id);

                return result;
            } catch (error) {
                inProgress.delete(step.id);
                throw error;
            }
        };

        // Execute all steps
        await Promise.all(steps.map(step => executeStepWithDeps(step)));
        return results;
    }

    /**
     * Validate step configuration
     */
    validateStep(step: WorkflowStep): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        // Check required fields
        if (!step.id) {
            errors.push('Step ID is required');
        }
        if (!step.agentId) {
            errors.push('Agent ID is required');
        }
        if (!step.task) {
            errors.push('Task is required');
        }

        // Check timeout values
        if (step.timeout !== undefined && step.timeout <= 0) {
            errors.push('Timeout must be positive');
        }

        // Check retry configuration
        if (step.retryCount !== undefined && step.retryCount < 0) {
            errors.push('Retry count must be non-negative');
        }
        if (step.retryDelay !== undefined && step.retryDelay < 0) {
            errors.push('Retry delay must be non-negative');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Get step execution metrics
     */
    getStepMetrics(stepResult: StepResult): {
        duration: number;
        success: boolean;
        errorType?: string;
        performance: 'fast' | 'normal' | 'slow';
    } {
        const duration = stepResult.endTime - stepResult.startTime;
        const success = stepResult.status === 'completed';
        const errorType = stepResult.error?.constructor.name;

        // Classify performance (these thresholds could be configurable)
        let performance: 'fast' | 'normal' | 'slow';
        if (duration < 1000) {
            performance = 'fast';
        } else if (duration < 5000) {
            performance = 'normal';
        } else {
            performance = 'slow';
        }

        return {
            duration,
            success,
            errorType,
            performance
        };
    }

    /**
     * Create a step result for a skipped step
     */
    createSkippedStepResult(step: WorkflowStep, reason: string): StepResult {
        const now = Date.now();
        return {
            stepId: step.id,
            agentId: step.agentId,
            result: { skipped: true, reason },
            status: 'skipped',
            startTime: now,
            endTime: now
        };
    }

    /**
     * Create a step result for a failed step
     */
    createFailedStepResult(step: WorkflowStep, error: Error): StepResult {
        const now = Date.now();
        return {
            stepId: step.id,
            agentId: step.agentId,
            result: null,
            error,
            status: 'failed',
            startTime: now,
            endTime: now
        };
    }
}

/**
 * Simple semaphore implementation for concurrency control
 */
class Semaphore {
    private permits: number;
    private waitQueue: (() => void)[] = [];

    constructor(permits: number) {
        this.permits = permits;
    }

    async acquire(): Promise<void> {
        if (this.permits > 0) {
            this.permits--;
            return Promise.resolve();
        }

        return new Promise<void>((resolve) => {
            this.waitQueue.push(resolve);
        });
    }

    release(): void {
        this.permits++;
        if (this.waitQueue.length > 0) {
            const resolve = this.waitQueue.shift()!;
            this.permits--;
            resolve();
        }
    }
}
