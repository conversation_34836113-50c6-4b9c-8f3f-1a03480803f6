/**
 * Dependency Resolver for Agent Orchestrator
 * 
 * Handles dependency resolution and step ordering for workflows.
 */

import { WorkflowStep } from './types.js';

/**
 * Dependency Resolver class
 */
export class DependencyResolver {
    /**
     * Group workflow steps by their dependency level
     */
    groupStepsByDependencyLevel(steps: WorkflowStep[]): WorkflowStep[][] {
        const stepsByLevel: WorkflowStep[][] = [];
        const remainingSteps = new Set(steps);
        const stepMap = new Map(steps.map(step => [step.id, step]));
        
        // Find steps with no dependencies
        let currentLevel: WorkflowStep[] = [];
        for (const step of steps) {
            if (!step.dependencies || step.dependencies.length === 0) {
                currentLevel.push(step);
                remainingSteps.delete(step);
            }
        }
        
        if (currentLevel.length === 0 && remainingSteps.size > 0) {
            throw new Error('No steps without dependencies found - possible circular dependency');
        }
        
        stepsByLevel.push(currentLevel);
        
        // Group remaining steps by dependency level
        while (remainingSteps.size > 0) {
            const nextLevel: WorkflowStep[] = [];
            
            for (const step of remainingSteps) {
                // Check if all dependencies are in previous levels
                const allDependenciesMet = step.dependencies?.every(depId => {
                    const depStep = stepMap.get(depId);
                    return depStep && !remainingSteps.has(depStep);
                }) ?? true;
                
                if (allDependenciesMet) {
                    nextLevel.push(step);
                }
            }
            
            if (nextLevel.length === 0) {
                throw new Error('Circular dependency detected in workflow');
            }
            
            stepsByLevel.push(nextLevel);
            
            for (const step of nextLevel) {
                remainingSteps.delete(step);
            }
        }
        
        return stepsByLevel;
    }

    /**
     * Perform topological sort on workflow steps
     */
    topologicalSort(steps: WorkflowStep[]): WorkflowStep[] {
        const stepMap = new Map(steps.map(step => [step.id, step]));
        const visited = new Set<string>();
        const visiting = new Set<string>();
        const result: WorkflowStep[] = [];

        const visit = (stepId: string): void => {
            if (visiting.has(stepId)) {
                throw new Error(`Circular dependency detected involving step ${stepId}`);
            }
            if (visited.has(stepId)) {
                return;
            }

            const step = stepMap.get(stepId);
            if (!step) {
                throw new Error(`Step ${stepId} not found`);
            }

            visiting.add(stepId);

            // Visit dependencies first
            if (step.dependencies) {
                for (const depId of step.dependencies) {
                    visit(depId);
                }
            }

            visiting.delete(stepId);
            visited.add(stepId);
            result.push(step);
        };

        // Visit all steps
        for (const step of steps) {
            if (!visited.has(step.id)) {
                visit(step.id);
            }
        }

        return result;
    }

    /**
     * Find all dependencies of a step (including transitive dependencies)
     */
    findAllDependencies(stepId: string, steps: WorkflowStep[]): string[] {
        const stepMap = new Map(steps.map(step => [step.id, step]));
        const allDependencies = new Set<string>();
        const visited = new Set<string>();

        const findDeps = (currentStepId: string): void => {
            if (visited.has(currentStepId)) {
                return;
            }
            visited.add(currentStepId);

            const step = stepMap.get(currentStepId);
            if (!step || !step.dependencies) {
                return;
            }

            for (const depId of step.dependencies) {
                allDependencies.add(depId);
                findDeps(depId);
            }
        };

        findDeps(stepId);
        return Array.from(allDependencies);
    }

    /**
     * Find all dependents of a step (steps that depend on this step)
     */
    findAllDependents(stepId: string, steps: WorkflowStep[]): string[] {
        const dependents = new Set<string>();

        for (const step of steps) {
            if (step.dependencies && step.dependencies.includes(stepId)) {
                dependents.add(step.id);
                // Recursively find dependents of dependents
                const transitiveDependents = this.findAllDependents(step.id, steps);
                for (const dep of transitiveDependents) {
                    dependents.add(dep);
                }
            }
        }

        return Array.from(dependents);
    }

    /**
     * Check if there are any circular dependencies
     */
    hasCircularDependencies(steps: WorkflowStep[]): boolean {
        try {
            this.topologicalSort(steps);
            return false;
        } catch (error) {
            return true;
        }
    }

    /**
     * Find circular dependencies in the workflow
     */
    findCircularDependencies(steps: WorkflowStep[]): string[][] {
        const stepMap = new Map(steps.map(step => [step.id, step]));
        const visited = new Set<string>();
        const visiting = new Set<string>();
        const cycles: string[][] = [];

        const findCycles = (stepId: string, path: string[]): void => {
            if (visiting.has(stepId)) {
                // Found a cycle
                const cycleStart = path.indexOf(stepId);
                if (cycleStart !== -1) {
                    cycles.push(path.slice(cycleStart).concat(stepId));
                }
                return;
            }
            if (visited.has(stepId)) {
                return;
            }

            const step = stepMap.get(stepId);
            if (!step) {
                return;
            }

            visiting.add(stepId);
            path.push(stepId);

            // Visit dependencies
            if (step.dependencies) {
                for (const depId of step.dependencies) {
                    findCycles(depId, [...path]);
                }
            }

            visiting.delete(stepId);
            visited.add(stepId);
            path.pop();
        };

        // Check all steps
        for (const step of steps) {
            if (!visited.has(step.id)) {
                findCycles(step.id, []);
            }
        }

        return cycles;
    }

    /**
     * Calculate the critical path of the workflow
     */
    calculateCriticalPath(steps: WorkflowStep[], stepDurations: Map<string, number>): {
        path: string[];
        totalDuration: number;
    } {
        const stepMap = new Map(steps.map(step => [step.id, step]));
        const earliestStart = new Map<string, number>();
        const latestStart = new Map<string, number>();
        
        // Calculate earliest start times (forward pass)
        const calculateEarliestStart = (stepId: string): number => {
            if (earliestStart.has(stepId)) {
                return earliestStart.get(stepId)!;
            }

            const step = stepMap.get(stepId);
            if (!step) {
                return 0;
            }

            let maxDependencyEnd = 0;
            if (step.dependencies) {
                for (const depId of step.dependencies) {
                    const depEarliestStart = calculateEarliestStart(depId);
                    const depDuration = stepDurations.get(depId) || 0;
                    maxDependencyEnd = Math.max(maxDependencyEnd, depEarliestStart + depDuration);
                }
            }

            earliestStart.set(stepId, maxDependencyEnd);
            return maxDependencyEnd;
        };

        // Calculate earliest start for all steps
        for (const step of steps) {
            calculateEarliestStart(step.id);
        }

        // Find project end time
        let projectEndTime = 0;
        for (const step of steps) {
            const stepStart = earliestStart.get(step.id) || 0;
            const stepDuration = stepDurations.get(step.id) || 0;
            projectEndTime = Math.max(projectEndTime, stepStart + stepDuration);
        }

        // Calculate latest start times (backward pass)
        const calculateLatestStart = (stepId: string): number => {
            if (latestStart.has(stepId)) {
                return latestStart.get(stepId)!;
            }

            const step = stepMap.get(stepId);
            const stepDuration = stepDurations.get(stepId) || 0;
            
            // Find all dependents
            const dependents = steps.filter(s => s.dependencies?.includes(stepId));
            
            if (dependents.length === 0) {
                // This is an end step
                const latest = projectEndTime - stepDuration;
                latestStart.set(stepId, latest);
                return latest;
            }

            let minDependentStart = Infinity;
            for (const dependent of dependents) {
                const dependentLatestStart = calculateLatestStart(dependent.id);
                minDependentStart = Math.min(minDependentStart, dependentLatestStart);
            }

            const latest = minDependentStart - stepDuration;
            latestStart.set(stepId, latest);
            return latest;
        };

        // Calculate latest start for all steps
        for (const step of steps) {
            calculateLatestStart(step.id);
        }

        // Find critical path (steps with zero slack)
        const criticalSteps: string[] = [];
        for (const step of steps) {
            const earliest = earliestStart.get(step.id) || 0;
            const latest = latestStart.get(step.id) || 0;
            if (Math.abs(earliest - latest) < 0.001) { // Account for floating point precision
                criticalSteps.push(step.id);
            }
        }

        // Sort critical steps by earliest start time
        criticalSteps.sort((a, b) => {
            const aStart = earliestStart.get(a) || 0;
            const bStart = earliestStart.get(b) || 0;
            return aStart - bStart;
        });

        return {
            path: criticalSteps,
            totalDuration: projectEndTime
        };
    }

    /**
     * Optimize step order for better parallelization
     */
    optimizeStepOrder(steps: WorkflowStep[]): WorkflowStep[] {
        // Group steps by dependency level
        const levels = this.groupStepsByDependencyLevel(steps);
        
        // Within each level, sort by priority and estimated duration
        const optimizedLevels = levels.map(level => {
            return level.sort((a, b) => {
                // First sort by priority (higher priority first)
                const aPriority = a.priority || 0;
                const bPriority = b.priority || 0;
                if (aPriority !== bPriority) {
                    return bPriority - aPriority;
                }
                
                // Then sort by estimated complexity (simpler tasks first for better parallelization)
                const aComplexity = this.estimateStepComplexity(a);
                const bComplexity = this.estimateStepComplexity(b);
                return aComplexity - bComplexity;
            });
        });

        // Flatten the optimized levels
        return optimizedLevels.flat();
    }

    /**
     * Estimate step complexity based on task description and configuration
     */
    private estimateStepComplexity(step: WorkflowStep): number {
        let complexity = 1;
        
        // Factor in timeout (longer timeout suggests more complex task)
        if (step.timeout) {
            complexity += step.timeout / 10000; // Normalize to reasonable scale
        }
        
        // Factor in retry count (more retries suggest less reliable/more complex task)
        if (step.retryCount) {
            complexity += step.retryCount * 0.5;
        }
        
        // Factor in task description length (longer description might indicate complexity)
        complexity += step.task.length / 1000;
        
        return complexity;
    }
}
