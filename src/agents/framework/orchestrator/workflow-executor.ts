/**
 * Workflow Executor for Agent Orchestrator
 * 
 * Handles the execution of workflows in both sequential and parallel modes.
 */

import { WorkflowStep, Workflow, WorkflowResult, StepResult } from './types.js';
import { StepExecutor } from './step-executor.js';
import { DependencyResolver } from './dependency-resolver.js';

/**
 * Workflow Executor class
 */
export class WorkflowExecutor {
    private stepExecutor: StepExecutor;
    private dependencyResolver: DependencyResolver;

    constructor(stepExecutor: StepExecutor) {
        this.stepExecutor = stepExecutor;
        this.dependencyResolver = new DependencyResolver();
    }

    /**
     * Execute a sequential workflow
     */
    async executeSequentialWorkflow(
        workflow: Workflow,
        workflowResult: WorkflowResult,
        context: any
    ): Promise<void> {
        // Execute steps in sequence
        for (const step of workflow.steps) {
            try {
                // Check if dependencies are satisfied
                if (step.dependencies && step.dependencies.length > 0) {
                    const unsatisfiedDependencies = step.dependencies.filter(depId => {
                        const depResult = workflowResult.stepResults.get(depId);
                        return !depResult || depResult.status !== 'completed';
                    });

                    if (unsatisfiedDependencies.length > 0) {
                        // Skip step if dependencies are not satisfied
                        workflowResult.stepResults.set(step.id, {
                            stepId: step.id,
                            agentId: step.agentId,
                            result: null,
                            status: 'skipped',
                            startTime: Date.now(),
                            endTime: Date.now()
                        });
                        continue;
                    }
                }

                // Execute step
                const stepResult = await this.stepExecutor.executeStep(step, context, workflow);
                workflowResult.stepResults.set(step.id, stepResult);

                // Update context with step result
                context = {
                    ...context,
                    [step.id]: stepResult.result
                };

                // Handle step failure
                if (stepResult.status === 'failed') {
                    await this.handleStepFailure(step, workflow, workflowResult, context);
                }
            } catch (error) {
                // Handle unexpected errors
                workflowResult.stepResults.set(step.id, {
                    stepId: step.id,
                    agentId: step.agentId,
                    result: null,
                    error: error as Error,
                    status: 'failed',
                    startTime: Date.now(),
                    endTime: Date.now()
                });

                if (workflow.onError === 'stop') {
                    throw error;
                }
            }
        }
    }

    /**
     * Execute a parallel workflow
     */
    async executeParallelWorkflow(
        workflow: Workflow,
        workflowResult: WorkflowResult,
        context: any
    ): Promise<void> {
        // Group steps by their dependencies
        const stepsByLevel = this.dependencyResolver.groupStepsByDependencyLevel(workflow.steps);
        
        // Execute steps level by level
        for (const levelSteps of stepsByLevel) {
            // Execute steps in this level in parallel
            const maxConcurrent = workflow.maxConcurrentSteps || levelSteps.length;
            const stepPromises: Promise<void>[] = [];
            
            for (let i = 0; i < levelSteps.length; i += maxConcurrent) {
                const batch = levelSteps.slice(i, i + maxConcurrent);
                
                const batchPromises = batch.map(async (step) => {
                    try {
                        const stepResult = await this.stepExecutor.executeStep(step, context, workflow);
                        workflowResult.stepResults.set(step.id, stepResult);
                        
                        // Update context with step result
                        context = {
                            ...context,
                            [step.id]: stepResult.result
                        };
                    } catch (error) {
                        workflowResult.stepResults.set(step.id, {
                            stepId: step.id,
                            agentId: step.agentId,
                            result: null,
                            error: error as Error,
                            status: 'failed',
                            startTime: Date.now(),
                            endTime: Date.now()
                        });
                        
                        if (workflow.onError === 'stop') {
                            throw error;
                        }
                    }
                });
                
                stepPromises.push(...batchPromises);
                
                // Wait for this batch to complete before starting the next batch
                await Promise.all(batchPromises);
            }
            
            // Wait for all steps in this level to complete
            await Promise.all(stepPromises);
        }
    }

    /**
     * Handle step failure based on workflow error handling strategy
     */
    private async handleStepFailure(
        step: WorkflowStep,
        workflow: Workflow,
        workflowResult: WorkflowResult,
        context: any
    ): Promise<void> {
        if (workflow.onError === 'stop') {
            const stepResult = workflowResult.stepResults.get(step.id);
            throw new Error(`Step ${step.id} failed: ${stepResult?.error?.message}`);
        } else if (workflow.onError === 'retry') {
            // Retry step
            const retryCount = step.retryCount || 1;
            const retryDelay = step.retryDelay || 1000;

            for (let i = 0; i < retryCount; i++) {
                // Wait for retry delay
                await new Promise(resolve => setTimeout(resolve, retryDelay));

                // Retry step
                const retryResult = await this.stepExecutor.executeStep(step, context, workflow);
                workflowResult.stepResults.set(step.id, retryResult);

                // Update context with retry result
                context = {
                    ...context,
                    [step.id]: retryResult.result
                };

                // Break if successful
                if (retryResult.status === 'completed') {
                    break;
                }
            }
        }
        // For 'continue', just move on to the next step
    }

    /**
     * Aggregate workflow results
     */
    aggregateResults(stepResults: Map<string, StepResult>): any {
        const results: Record<string, any> = {};
        const errors: Record<string, Error> = {};
        let hasErrors = false;

        for (const [stepId, stepResult] of stepResults) {
            if (stepResult.status === 'completed') {
                results[stepId] = stepResult.result;
            } else if (stepResult.status === 'failed' && stepResult.error) {
                errors[stepId] = stepResult.error;
                hasErrors = true;
            }
        }

        return {
            results,
            errors: hasErrors ? errors : undefined,
            totalSteps: stepResults.size,
            completedSteps: Array.from(stepResults.values()).filter(r => r.status === 'completed').length,
            failedSteps: Array.from(stepResults.values()).filter(r => r.status === 'failed').length,
            skippedSteps: Array.from(stepResults.values()).filter(r => r.status === 'skipped').length
        };
    }

    /**
     * Validate workflow before execution
     */
    validateWorkflow(workflow: Workflow): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        // Check for empty steps
        if (!workflow.steps || workflow.steps.length === 0) {
            errors.push('Workflow must have at least one step');
        }

        // Check for duplicate step IDs
        const stepIds = new Set<string>();
        for (const step of workflow.steps) {
            if (stepIds.has(step.id)) {
                errors.push(`Duplicate step ID: ${step.id}`);
            }
            stepIds.add(step.id);
        }

        // Check for invalid dependencies
        for (const step of workflow.steps) {
            if (step.dependencies) {
                for (const depId of step.dependencies) {
                    if (!stepIds.has(depId)) {
                        errors.push(`Step ${step.id} depends on non-existent step: ${depId}`);
                    }
                }
            }
        }

        // Check for circular dependencies
        try {
            this.dependencyResolver.groupStepsByDependencyLevel(workflow.steps);
        } catch (error) {
            errors.push('Circular dependency detected in workflow');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Get workflow execution statistics
     */
    getExecutionStatistics(workflowResult: WorkflowResult): {
        totalDuration: number;
        averageStepDuration: number;
        longestStep: { stepId: string; duration: number } | null;
        shortestStep: { stepId: string; duration: number } | null;
        successRate: number;
    } {
        const stepResults = Array.from(workflowResult.stepResults.values());
        const totalDuration = workflowResult.endTime - workflowResult.startTime;
        
        if (stepResults.length === 0) {
            return {
                totalDuration,
                averageStepDuration: 0,
                longestStep: null,
                shortestStep: null,
                successRate: 0
            };
        }

        const stepDurations = stepResults.map(result => ({
            stepId: result.stepId,
            duration: result.endTime - result.startTime
        }));

        const averageStepDuration = stepDurations.reduce((sum, step) => sum + step.duration, 0) / stepDurations.length;
        const longestStep = stepDurations.reduce((longest, current) => 
            current.duration > longest.duration ? current : longest
        );
        const shortestStep = stepDurations.reduce((shortest, current) => 
            current.duration < shortest.duration ? current : shortest
        );

        const completedSteps = stepResults.filter(r => r.status === 'completed').length;
        const successRate = completedSteps / stepResults.length;

        return {
            totalDuration,
            averageStepDuration,
            longestStep,
            shortestStep,
            successRate
        };
    }
}
