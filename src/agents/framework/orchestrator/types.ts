/**
 * Type definitions for Agent Orchestrator
 * 
 * Shared types and interfaces for the orchestrator system.
 */

import { TaskPriority } from '../../../parallel/worker-pool.js';

/**
 * Workflow Step
 */
export interface WorkflowStep {
    id: string;
    agentId: string;
    task: string;
    context?: any;
    dependencies?: string[]; // IDs of steps that must complete before this step
    priority?: TaskPriority;
    timeout?: number;
    retryCount?: number;
    retryDelay?: number;
}

/**
 * Workflow
 */
export interface Workflow {
    id: string;
    name: string;
    description: string;
    steps: WorkflowStep[];
    parallelExecution?: boolean;
    maxConcurrentSteps?: number;
    timeout?: number;
    onError?: 'continue' | 'stop' | 'retry';
    context?: any;
}

/**
 * Step Result
 */
export interface StepResult {
    stepId: string;
    agentId: string;
    result: any;
    error?: Error;
    status: 'completed' | 'failed' | 'skipped';
    startTime: number;
    endTime: number;
}

/**
 * Workflow Result
 */
export interface WorkflowResult {
    workflowId: string;
    status: 'completed' | 'failed' | 'cancelled';
    stepResults: Map<string, StepResult>;
    aggregatedResult?: any;
    startTime: number;
    endTime: number;
    error?: Error;
}

/**
 * Workflow Status
 */
export enum WorkflowStatus {
    PENDING = 'pending',
    RUNNING = 'running',
    COMPLETED = 'completed',
    FAILED = 'failed',
    CANCELLED = 'cancelled'
}

/**
 * Workflow Event Types
 */
export interface WorkflowEvent {
    type: 'workflow_started' | 'workflow_completed' | 'workflow_failed' | 'workflow_cancelled';
    workflowId: string;
    workflowName: string;
    timestamp: number;
    data?: any;
}

export interface StepEvent {
    type: 'step_started' | 'step_completed' | 'step_failed' | 'step_skipped';
    stepId: string;
    agentId: string;
    workflowId: string;
    timestamp: number;
    data?: any;
}

/**
 * Workflow Template
 */
export interface WorkflowTemplate {
    id: string;
    name: string;
    description: string;
    category: string;
    tags: string[];
    steps: Omit<WorkflowStep, 'id'>[];
    defaultContext?: any;
    requiredAgents: string[];
    estimatedDuration?: number;
    complexity: 'low' | 'medium' | 'high';
}

/**
 * Workflow Execution Options
 */
export interface WorkflowExecutionOptions {
    context?: any;
    timeout?: number;
    maxRetries?: number;
    onStepComplete?: (stepResult: StepResult) => void;
    onStepFailed?: (stepResult: StepResult) => void;
    onWorkflowProgress?: (progress: WorkflowProgress) => void;
}

/**
 * Workflow Progress
 */
export interface WorkflowProgress {
    workflowId: string;
    totalSteps: number;
    completedSteps: number;
    failedSteps: number;
    skippedSteps: number;
    currentStep?: string;
    estimatedTimeRemaining?: number;
    percentComplete: number;
}

/**
 * Workflow Metrics
 */
export interface WorkflowMetrics {
    workflowId: string;
    totalDuration: number;
    averageStepDuration: number;
    successRate: number;
    parallelizationEfficiency: number;
    resourceUtilization: {
        cpu: number;
        memory: number;
        agents: number;
    };
    bottlenecks: {
        stepId: string;
        duration: number;
        reason: string;
    }[];
}

/**
 * Agent Utilization
 */
export interface AgentUtilization {
    agentId: string;
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    averageTaskDuration: number;
    utilizationPercentage: number;
    currentLoad: number;
}

/**
 * Workflow Schedule
 */
export interface WorkflowSchedule {
    id: string;
    workflowId: string;
    cronExpression?: string;
    interval?: number;
    startTime?: Date;
    endTime?: Date;
    enabled: boolean;
    lastRun?: Date;
    nextRun?: Date;
    runCount: number;
    maxRuns?: number;
}

/**
 * Workflow Dependency Graph
 */
export interface WorkflowDependencyGraph {
    nodes: {
        id: string;
        label: string;
        type: 'step' | 'workflow';
        metadata?: any;
    }[];
    edges: {
        from: string;
        to: string;
        type: 'dependency' | 'data_flow';
        weight?: number;
    }[];
}

/**
 * Workflow Optimization Suggestion
 */
export interface WorkflowOptimizationSuggestion {
    type: 'parallelization' | 'step_order' | 'resource_allocation' | 'timeout_adjustment';
    description: string;
    impact: 'low' | 'medium' | 'high';
    estimatedImprovement: {
        duration?: number;
        successRate?: number;
        resourceUsage?: number;
    };
    implementation: {
        changes: any[];
        effort: 'low' | 'medium' | 'high';
        risks: string[];
    };
}

/**
 * Workflow Execution Context
 */
export interface WorkflowExecutionContext {
    workflowId: string;
    executionId: string;
    userId?: string;
    environment: 'development' | 'staging' | 'production';
    variables: Record<string, any>;
    secrets: Record<string, string>;
    permissions: string[];
    resourceLimits: {
        maxDuration?: number;
        maxMemory?: number;
        maxConcurrentSteps?: number;
    };
}

/**
 * Workflow Audit Log Entry
 */
export interface WorkflowAuditLogEntry {
    id: string;
    workflowId: string;
    executionId: string;
    timestamp: Date;
    event: string;
    actor: string;
    details: any;
    changes?: {
        before: any;
        after: any;
    };
}

/**
 * Workflow Error Types
 */
export class WorkflowError extends Error {
    constructor(
        message: string,
        public workflowId: string,
        public stepId?: string,
        public cause?: Error
    ) {
        super(message);
        this.name = 'WorkflowError';
    }
}

export class StepExecutionError extends WorkflowError {
    constructor(
        message: string,
        workflowId: string,
        stepId: string,
        public agentId: string,
        cause?: Error
    ) {
        super(message, workflowId, stepId, cause);
        this.name = 'StepExecutionError';
    }
}

export class DependencyError extends WorkflowError {
    constructor(
        message: string,
        workflowId: string,
        public dependencyChain: string[]
    ) {
        super(message, workflowId);
        this.name = 'DependencyError';
    }
}

export class TimeoutError extends WorkflowError {
    constructor(
        message: string,
        workflowId: string,
        stepId?: string,
        public timeoutMs?: number
    ) {
        super(message, workflowId, stepId);
        this.name = 'TimeoutError';
    }
}

/**
 * Workflow State
 */
export interface WorkflowState {
    status: WorkflowStatus;
    currentStep?: string;
    completedSteps: string[];
    failedSteps: string[];
    skippedSteps: string[];
    context: any;
    startTime: number;
    lastUpdateTime: number;
    error?: Error;
}

/**
 * Workflow Configuration
 */
export interface WorkflowConfiguration {
    defaultTimeout: number;
    maxConcurrentWorkflows: number;
    maxConcurrentStepsPerWorkflow: number;
    retryPolicy: {
        maxRetries: number;
        backoffMultiplier: number;
        maxBackoffMs: number;
    };
    logging: {
        level: 'debug' | 'info' | 'warn' | 'error';
        includeStepDetails: boolean;
        includeContext: boolean;
    };
    monitoring: {
        enableMetrics: boolean;
        metricsInterval: number;
        enableTracing: boolean;
    };
}
