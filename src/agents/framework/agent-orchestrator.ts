/**
 * Agent Orchestrator for X10sion
 *
 * This module implements an orchestrator for coordinating AI agents
 * in the X10sion system. Refactored for modularity and maintainability.
 */

import * as vscode from 'vscode';
import { BaseAgent, AgentStatus, AgentTask } from '../base-agent.js';
import { X10sionAgentSystem } from './agent-system.js';
import { WorkerPool, TaskPriority } from '../../parallel/worker-pool.js';
import {
    Workflow,
    WorkflowResult,
    WorkflowStatus,
    WorkflowStep,
    StepResult,
    WorkflowExecutionOptions,
    WorkflowProgress
} from './orchestrator/types.js';
import { WorkflowExecutor } from './orchestrator/workflow-executor.js';
import { StepExecutor } from './orchestrator/step-executor.js';

// Re-export types for backward compatibility
export {
    Workflow,
    WorkflowStep,
    WorkflowResult,
    StepResult,
    WorkflowStatus,
    WorkflowExecutionOptions,
    WorkflowProgress
} from './orchestrator/types.js';

/**
 * Agent Orchestrator class
 *
 * Coordinates AI agents to execute workflows. Refactored to use modular components.
 */
export class AgentOrchestrator {
    private agentSystem: X10sionAgentSystem;
    private workerPool: WorkerPool;
    private workflows: Map<string, Workflow> = new Map();
    private workflowStatus: Map<string, WorkflowStatus> = new Map();
    private workflowResults: Map<string, WorkflowResult> = new Map();
    private runningWorkflows: Set<string> = new Set();
    private eventEmitter: vscode.EventEmitter<any> = new vscode.EventEmitter<any>();
    private disposables: vscode.Disposable[] = [];
    private nextWorkflowId: number = 0;

    // Modular components
    private workflowExecutor: WorkflowExecutor;
    private stepExecutor: StepExecutor;

    /**
     * Constructor for AgentOrchestrator
     */
    constructor(
        agentSystem: X10sionAgentSystem,
        workerPool: WorkerPool
    ) {
        this.agentSystem = agentSystem;
        this.workerPool = workerPool;

        // Initialize modular components
        this.stepExecutor = new StepExecutor(agentSystem, this.eventEmitter);
        this.workflowExecutor = new WorkflowExecutor(this.stepExecutor);
    }

    /**
     * Register a workflow
     * @param workflow Workflow to register
     * @returns Workflow ID
     */
    registerWorkflow(workflow: Omit<Workflow, 'id'>): string {
        const id = `workflow-${this.nextWorkflowId++}`;

        const fullWorkflow: Workflow = {
            ...workflow,
            id
        };

        this.workflows.set(id, fullWorkflow);
        this.workflowStatus.set(id, WorkflowStatus.PENDING);

        return id;
    }

    /**
     * Execute a workflow
     * @param workflowId Workflow ID
     * @param context Optional context to merge with workflow context
     * @returns Promise that resolves with the workflow result
     */
    async executeWorkflow(workflowId: string, context?: any): Promise<WorkflowResult> {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) {
            throw new Error(`Workflow ${workflowId} not found`);
        }

        if (this.runningWorkflows.has(workflowId)) {
            throw new Error(`Workflow ${workflowId} is already running`);
        }

        // Initialize workflow result
        const workflowResult: WorkflowResult = {
            workflowId,
            status: 'completed',
            stepResults: new Map(),
            startTime: Date.now(),
            endTime: 0
        };

        // Update status
        this.workflowStatus.set(workflowId, WorkflowStatus.RUNNING);
        this.runningWorkflows.add(workflowId);

        // Emit event
        this.eventEmitter.fire({
            type: 'workflow_started',
            workflowId,
            workflowName: workflow.name
        });

        try {
            // Merge context
            const mergedContext = {
                ...workflow.context,
                ...context
            };

            // Execute workflow using the workflow executor
            if (workflow.parallelExecution) {
                await this.workflowExecutor.executeParallelWorkflow(workflow, workflowResult, mergedContext);
            } else {
                await this.workflowExecutor.executeSequentialWorkflow(workflow, workflowResult, mergedContext);
            }

            // Aggregate results
            workflowResult.aggregatedResult = this.workflowExecutor.aggregateResults(workflowResult.stepResults);
            workflowResult.endTime = Date.now();
            workflowResult.status = 'completed';

            // Update status
            this.workflowStatus.set(workflowId, WorkflowStatus.COMPLETED);
        } catch (error) {
            workflowResult.status = 'failed';
            workflowResult.error = error as Error;
            workflowResult.endTime = Date.now();

            // Update status
            this.workflowStatus.set(workflowId, WorkflowStatus.FAILED);

            // Emit event
            this.eventEmitter.fire({
                type: 'workflow_failed',
                workflowId,
                workflowName: workflow.name,
                error
            });
        } finally {
            this.runningWorkflows.delete(workflowId);
            this.workflowResults.set(workflowId, workflowResult);

            // Emit event
            this.eventEmitter.fire({
                type: 'workflow_completed',
                workflowId,
                workflowName: workflow.name,
                result: workflowResult
            });
        }

        return workflowResult;
    }







    /**
     * Validate a workflow before execution
     */
    validateWorkflow(workflow: Workflow): { isValid: boolean; errors: string[] } {
        return this.workflowExecutor.validateWorkflow(workflow);
    }

    /**
     * Get workflow execution statistics
     */
    getWorkflowStatistics(workflowId: string): any {
        const result = this.workflowResults.get(workflowId);
        if (!result) {
            return null;
        }
        return this.workflowExecutor.getExecutionStatistics(result);
    }

    /**
     * Cancel a running workflow
     * @param workflowId Workflow ID
     */
    cancelWorkflow(workflowId: string): void {
        if (!this.runningWorkflows.has(workflowId)) {
            return;
        }

        this.workflowStatus.set(workflowId, WorkflowStatus.CANCELLED);
        this.runningWorkflows.delete(workflowId);

        // Emit event
        this.eventEmitter.fire({
            type: 'workflow_cancelled',
            workflowId
        });
    }

    /**
     * Get workflow status
     * @param workflowId Workflow ID
     * @returns Workflow status
     */
    getWorkflowStatus(workflowId: string): WorkflowStatus | undefined {
        return this.workflowStatus.get(workflowId);
    }

    /**
     * Get workflow result
     * @param workflowId Workflow ID
     * @returns Workflow result
     */
    getWorkflowResult(workflowId: string): WorkflowResult | undefined {
        return this.workflowResults.get(workflowId);
    }

    /**
     * Subscribe to orchestrator events
     * @param listener Event listener
     * @returns Disposable
     */
    onEvent(listener: (event: any) => void): vscode.Disposable {
        return this.eventEmitter.event(listener);
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        // Cancel all running workflows
        for (const workflowId of this.runningWorkflows) {
            this.cancelWorkflow(workflowId);
        }

        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Dispose of event emitter
        this.eventEmitter.dispose();
    }
}
