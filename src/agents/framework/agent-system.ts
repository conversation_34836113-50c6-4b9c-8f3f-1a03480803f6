/**
 * Agent System for X10sion
 *
 * This module implements the core agent system that manages all AI agents
 * in the X10sion extension.
 */

import * as vscode from 'vscode';
import { BaseAgent, AgentStatus } from '../base-agent.js';
import { WorkerPool } from '../../parallel/worker-pool.js';
import { LLMMonitor } from '../../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../../llm/providers/base-provider.js';

/**
 * Agent System Options
 */
export interface AgentSystemOptions {
    maxAgents?: number;
    enableParallelExecution?: boolean;
    enableAgentCollaboration?: boolean;
    debugMode?: boolean;
    [key: string]: any;
}

/**
 * Agent System class
 *
 * Manages all AI agents in the system, including registration, discovery,
 * and coordination.
 */
export class X10sionAgentSystem {
    private agents: Map<string, BaseAgent> = new Map();
    private llmProviders: Map<string, LanguageModelProvider> = new Map();
    private workerPool: WorkerPool;
    private llmMonitor: LLMMonitor;
    private options: AgentSystemOptions;
    private eventEmitter: vscode.EventEmitter<any> = new vscode.EventEmitter<any>();
    private disposables: vscode.Disposable[] = [];
    private isInitialized: boolean = false;

    /**
     * Constructor for X10sionAgentSystem
     */
    constructor(
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor,
        options: AgentSystemOptions = {}
    ) {
        this.workerPool = workerPool;
        this.llmMonitor = llmMonitor;

        // Set default options
        this.options = {
            maxAgents: 20,
            enableParallelExecution: true,
            enableAgentCollaboration: true,
            debugMode: false,
            ...options
        };

        console.log('Agent System initialized with options:', this.options);
    }

    /**
     * Initialize the agent system
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        // Subscribe to LLM monitor events
        const monitorSubscription = this.llmMonitor.onEvent((event) => {
            // Forward relevant events
            if (event.type === 'issue') {
                this.eventEmitter.fire({
                    type: 'llm_issue',
                    issue: event.issue
                });
            } else if (event.type === 'agent_intervention') {
                // Handle agent intervention
                this.handleAgentIntervention(event.agentId, event.intervention, event.issue);

                // Forward the event
                this.eventEmitter.fire({
                    type: 'agent_intervention',
                    agentId: event.agentId,
                    intervention: event.intervention,
                    issue: event.issue
                });
            }
        });

        this.disposables.push(monitorSubscription);
        this.isInitialized = true;

        console.log('Agent System initialized');
    }

    /**
     * Register an LLM provider with the system
     * @param provider LLM provider to register
     */
    registerLLMProvider(provider: LanguageModelProvider): void {
        this.llmProviders.set(provider.getName(), provider);

        if (this.options.debugMode) {
            console.log(`LLM Provider registered: ${provider.getName()}`);
        }

        // Emit event
        this.eventEmitter.fire({
            type: 'provider_registered',
            providerName: provider.getName()
        });
    }

    /**
     * Unregister an LLM provider from the system
     * @param providerName Name of the provider to unregister
     */
    unregisterLLMProvider(providerName: string): void {
        this.llmProviders.delete(providerName);

        if (this.options.debugMode) {
            console.log(`LLM Provider unregistered: ${providerName}`);
        }

        // Emit event
        this.eventEmitter.fire({
            type: 'provider_unregistered',
            providerName
        });
    }

    /**
     * Get an LLM provider by name
     * @param name Provider name
     * @returns LLM provider or undefined if not found
     */
    getLLMProvider(name: string): LanguageModelProvider | undefined {
        return this.llmProviders.get(name);
    }

    /**
     * Get all registered LLM providers
     * @returns Map of provider names to providers
     */
    getAllLLMProviders(): Map<string, LanguageModelProvider> {
        return new Map(this.llmProviders);
    }

    /**
     * Get the best available LLM provider based on requirements
     * @param requirements Provider requirements
     * @returns Best matching provider or undefined if none available
     */
    getBestLLMProvider(requirements?: {
        preferredProvider?: string;
        minTokenLimit?: number;
        capabilities?: string[];
    }): LanguageModelProvider | undefined {
        if (!requirements) {
            // Return the first available provider if no requirements
            return this.llmProviders.size > 0 ?
                this.llmProviders.values().next().value : undefined;
        }

        // If preferred provider is specified and available, use it
        if (requirements.preferredProvider &&
            this.llmProviders.has(requirements.preferredProvider)) {
            return this.llmProviders.get(requirements.preferredProvider);
        }

        // Otherwise, find the best match based on requirements
        // This is a simple implementation that can be enhanced
        // For now, just return the first provider since checking availability
        // would require an async method, which we can't use in this synchronous method
        if (this.llmProviders.size > 0) {
            return this.llmProviders.values().next().value;
        }

        return undefined;
    }

    /**
     * Register an agent with the system
     * @param agent Agent to register
     */
    registerAgent(agent: BaseAgent): void {
        if (this.agents.size >= this.options.maxAgents!) {
            throw new Error(`Maximum number of agents (${this.options.maxAgents}) reached`);
        }

        this.agents.set(agent.getId(), agent);

        if (this.options.debugMode) {
            console.log(`Agent registered: ${agent.getId()} (${agent.getName()})`);
        }

        // Emit event
        this.eventEmitter.fire({
            type: 'agent_registered',
            agentId: agent.getId(),
            agentName: agent.getName()
        });
    }

    /**
     * Unregister an agent from the system
     * @param agentId ID of the agent to unregister
     */
    unregisterAgent(agentId: string): void {
        const agent = this.agents.get(agentId);
        if (!agent) {
            return;
        }

        this.agents.delete(agentId);

        if (this.options.debugMode) {
            console.log(`Agent unregistered: ${agentId} (${agent.getName()})`);
        }

        // Emit event
        this.eventEmitter.fire({
            type: 'agent_unregistered',
            agentId,
            agentName: agent.getName()
        });
    }

    /**
     * Get an agent by ID
     * @param agentId Agent ID
     * @returns Agent or undefined if not found
     */
    getAgent(agentId: string): BaseAgent | undefined {
        return this.agents.get(agentId);
    }

    /**
     * Get all registered agents
     * @returns Map of agent IDs to agents
     */
    getAllAgents(): Map<string, BaseAgent> {
        return new Map(this.agents);
    }

    /**
     * Get agents by status
     * @param status Agent status
     * @returns Array of agents with the specified status
     */
    getAgentsByStatus(status: AgentStatus): BaseAgent[] {
        return Array.from(this.agents.values())
            .filter(agent => agent.getStatus() === status);
    }

    /**
     * Handle agent intervention
     * @param agentId Agent ID
     * @param intervention Intervention details
     * @param issue Issue that triggered the intervention
     */
    private handleAgentIntervention(agentId: string, intervention: any, issue: any): void {
        // Get the agent
        const agent = this.agents.get(agentId);
        if (!agent) {
            console.warn(`Cannot handle intervention: Agent ${agentId} not found`);
            return;
        }

        // Apply intervention based on strategy
        switch (intervention.strategy) {
            case 'RETRY':
                // Reset agent state and retry
                if (agent.getStatus() === AgentStatus.ERROR) {
                    // Reset agent status to IDLE
                    (agent as any).status = AgentStatus.IDLE;

                    if (this.options.debugMode) {
                        console.log(`Agent ${agentId} status reset to IDLE after intervention`);
                    }
                }
                break;

            case 'SWITCH_MODEL':
                // Try to find a better model for the agent
                const currentProvider = agent.getLLMProvider();
                if (currentProvider) {
                    // Find a larger model if available
                    const betterProvider = this.getBestLLMProvider({
                        capabilities: ['larger_context', 'better_reasoning'],
                        minTokenLimit: 8000
                    });

                    if (betterProvider && betterProvider.getName() !== currentProvider.getName()) {
                        // Switch the agent to use the better provider
                        agent.setLLMProvider(betterProvider);

                        if (this.options.debugMode) {
                            console.log(`Agent ${agentId} switched to model ${betterProvider.getName()}`);
                        }
                    }
                }
                break;

            case 'HUMAN_ASSISTANCE':
                // Notify the user about the issue
                vscode.window.showWarningMessage(
                    `Agent ${agent.getName()} needs assistance: ${issue.description}`,
                    'View Details'
                ).then(selection => {
                    if (selection === 'View Details') {
                        // Show agent details
                        vscode.window.showInformationMessage(
                            `Agent: ${agent.getName()}\nIssue: ${issue.type}\nDescription: ${issue.description}`
                        );
                    }
                });
                break;

            case 'FALLBACK':
                // Use a fallback approach
                // This could involve using a simpler prompt or a different approach
                if (this.options.debugMode) {
                    console.log(`Using fallback approach for agent ${agentId}`);
                }
                break;

            default:
                // No specific handling for other strategies
                break;
        }

        // Log the intervention
        if (this.options.debugMode) {
            console.log(`Agent intervention handled: ${intervention.strategy} for ${agentId}`);
        }
    }

    /**
     * Subscribe to agent system events
     * @param listener Event listener
     * @returns Disposable
     */
    onEvent(listener: (event: any) => void): vscode.Disposable {
        return this.eventEmitter.event(listener);
    }

    /**
     * Dispose of resources
     */
    dispose(): void {
        // Dispose of all agents
        for (const agent of this.agents.values()) {
            agent.dispose();
        }

        // Clear agents
        this.agents.clear();

        // Dispose of all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }

        // Dispose of event emitter
        this.eventEmitter.dispose();

        console.log('Agent System disposed');
    }
}
