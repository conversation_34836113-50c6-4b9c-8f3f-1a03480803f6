/**
 * Agent Factory for X10sion
 *
 * This module implements a factory for creating AI agents in the X10sion system.
 */

import { BaseAgent, AgentOptions } from '../base-agent.js';
import { WorkerPool } from '../../parallel/worker-pool.js';
import { LLMMonitor } from '../../monitoring/llm-monitor.js';
import { LanguageModelProvider } from '../../llm/providers/base-provider.js';
import { X10sionAgentSystem } from './agent-system.js';
import { HumanInTheLoopAgent, HumanInTheLoopCapabilities } from '../human-in-the-loop-agent.js';

/**
 * Agent Type
 */
export enum AgentType {
    BASIC = 'basic',
    HUMAN_IN_THE_LOOP = 'human_in_the_loop',
    CODE_REVIEWER = 'code_reviewer',
    DOCUMENTATION = 'documentation',
    DEBUG_ASSISTANT = 'debug_assistant',
    DEPENDENCY_MANAGER = 'dependency_manager',
    CUSTOM = 'custom'
}

/**
 * Agent Configuration
 */
export interface AgentConfiguration {
    id?: string;
    name: string;
    description: string;
    type: AgentType;
    llmProviderName?: string;
    options?: AgentOptions;
    capabilities?: any;
    customImplementation?: typeof BaseAgent;
}

/**
 * Agent Factory class
 *
 * Creates and configures AI agents for the X10sion system.
 */
export class AgentFactory {
    private agentSystem: X10sionAgentSystem;
    private workerPool: WorkerPool;
    private llmMonitor: LLMMonitor;
    private customAgentImplementations: Map<string, typeof BaseAgent> = new Map();
    private nextAgentId: number = 0;

    /**
     * Constructor for AgentFactory
     */
    constructor(
        agentSystem: X10sionAgentSystem,
        workerPool: WorkerPool,
        llmMonitor: LLMMonitor
    ) {
        this.agentSystem = agentSystem;
        this.workerPool = workerPool;
        this.llmMonitor = llmMonitor;
    }

    /**
     * Create an agent
     * @param config Agent configuration
     * @returns Created agent
     */
    createAgent(config: AgentConfiguration): BaseAgent {
        // Generate ID if not provided
        const id = config.id || `agent-${config.type}-${this.nextAgentId++}`;

        // Get LLM provider
        const llmProvider = this.getLLMProvider(config.llmProviderName);

        // Create agent based on type
        let agent: BaseAgent;

        switch (config.type) {
            case AgentType.HUMAN_IN_THE_LOOP:
                agent = this.createHumanInTheLoopAgent(id, config, llmProvider);
                break;
            case AgentType.CUSTOM:
                agent = this.createCustomAgent(id, config, llmProvider);
                break;
            case AgentType.BASIC:
            default:
                agent = this.createBasicAgent(id, config, llmProvider);
                break;
        }

        // Register agent with the system
        this.agentSystem.registerAgent(agent);

        return agent;
    }

    /**
     * Create a basic agent
     * @param id Agent ID
     * @param config Agent configuration
     * @param llmProvider LLM provider
     * @returns Basic agent
     */
    private createBasicAgent(
        id: string,
        config: AgentConfiguration,
        llmProvider: LanguageModelProvider
    ): BaseAgent {
        return new BaseAgent(
            id,
            config.name,
            config.description,
            this.workerPool,
            this.llmMonitor,
            llmProvider,
            config.options
        );
    }

    /**
     * Create a human-in-the-loop agent
     * @param id Agent ID
     * @param config Agent configuration
     * @param llmProvider LLM provider
     * @returns Human-in-the-loop agent
     */
    private createHumanInTheLoopAgent(
        id: string,
        config: AgentConfiguration,
        llmProvider: LanguageModelProvider
    ): HumanInTheLoopAgent {
        const capabilities = config.capabilities as HumanInTheLoopCapabilities;
        
        return new HumanInTheLoopAgent(
            this.workerPool,
            this.llmMonitor,
            llmProvider,
            capabilities
        );
    }

    /**
     * Create a custom agent
     * @param id Agent ID
     * @param config Agent configuration
     * @param llmProvider LLM provider
     * @returns Custom agent
     */
    private createCustomAgent(
        id: string,
        config: AgentConfiguration,
        llmProvider: LanguageModelProvider
    ): BaseAgent {
        if (!config.customImplementation) {
            throw new Error('Custom implementation is required for custom agent type');
        }

        const AgentClass = config.customImplementation;
        
        return new AgentClass(
            id,
            config.name,
            config.description,
            this.workerPool,
            this.llmMonitor,
            llmProvider,
            config.options
        );
    }

    /**
     * Register a custom agent implementation
     * @param name Custom agent name
     * @param implementation Custom agent implementation
     */
    registerCustomAgentImplementation(name: string, implementation: typeof BaseAgent): void {
        this.customAgentImplementations.set(name, implementation);
    }

    /**
     * Get a custom agent implementation by name
     * @param name Custom agent name
     * @returns Custom agent implementation or undefined if not found
     */
    getCustomAgentImplementation(name: string): typeof BaseAgent | undefined {
        return this.customAgentImplementations.get(name);
    }

    /**
     * Get an LLM provider
     * @param providerName Provider name (optional)
     * @returns LLM provider
     */
    private getLLMProvider(providerName?: string): LanguageModelProvider {
        let provider: LanguageModelProvider | undefined;

        if (providerName) {
            provider = this.agentSystem.getLLMProvider(providerName);
        }

        if (!provider) {
            provider = this.agentSystem.getBestLLMProvider();
        }

        if (!provider) {
            throw new Error('No LLM provider available');
        }

        return provider;
    }
}
